#!/bin/bash
#. "$(dirname "$0")/_/husky.sh"
set -euo pipefail
ts-node scripts/check_dependecies.ts
pnpm dlx turbo tsc-check
pnpm run circular-check

# 检查文件名是否为下划线命名（不准使用驼峰命名！！）
camel_case_files=$(git diff --cached --name-only --diff-filter=ACM "*.ts" | { grep -E '/?[^/]*[A-Z][^/]*' | grep -v 'admin_platform' || true; })
if [ -n "$camel_case_files" ]; then
  echo "❌ 提交被阻止！以下文件或路径包含大写字母（驼峰命名），请改为下划线命名法："
  echo "$camel_case_files" | while read -r line; do
    echo "  - $line"
  done
  exit 1
fi

# 新增检查：禁止出现“yong户”字样，需改为“客户”！！！
if git grep -l --exclude-standard "用户" --include="*.ts" | grep -v "husky"; then
  echo "❌ 提交被阻止！检测到代码中包含 'yong户' 字样，请改为 '客户' 后重新提交！！！"
  exit 1
fi