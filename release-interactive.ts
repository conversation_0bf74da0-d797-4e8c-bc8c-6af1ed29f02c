#!/usr/bin/env ts-node
/**
 * 交互式发布脚本 - 支持上下箭头选择
 */

import { execSync } from 'child_process'
import inquirer from 'inquirer'
import chalk from 'chalk'

// 颜色定义
const log = (message: string) => console.log(chalk.green(`[${new Date().toISOString().replace('T', ' ').substring(0, 19)}] ${message}`))
const error = (message: string) => console.log(chalk.red(`[ERROR] ${message}`))
const warn = (message: string) => console.log(chalk.yellow(`[WARN] ${message}`))
const info = (message: string) => console.log(chalk.blue(`[INFO] ${message}`))

// 定义分支名称
const DEV_BRANCH = 'develop'
const PRD_BRANCH = 'main'

// 检查是否有未提交的更改
function checkGitStatus() {
  try {
    const status = execSync('git status --porcelain', { encoding: 'utf8' })
    if (status.trim()) {
      error('存在未提交的更改，请先提交或暂存更改')
      console.log(status)
      process.exit(1)
    }
  } catch (err) {
    error('检查 Git 状态失败')
    process.exit(1)
  }
}

// Git 操作
function performGitOperations() {
  log('开始 Git 操作...')

  try {
    info(`切换到开发分支 (${DEV_BRANCH})...`)
    execSync(`git checkout ${DEV_BRANCH}`, { stdio: 'inherit' })

    info(`更新开发分支 (${DEV_BRANCH})...`)
    execSync(`git pull origin ${DEV_BRANCH}`, { stdio: 'inherit' })

    info(`切换到主分支 (${PRD_BRANCH})...`)
    execSync(`git checkout ${PRD_BRANCH}`, { stdio: 'inherit' })

    info(`更新主分支 (${PRD_BRANCH})...`)
    execSync(`git pull origin ${PRD_BRANCH}`, { stdio: 'inherit' })

    info(`将开发分支 (${DEV_BRANCH}) 合并到主分支 (${PRD_BRANCH})...`)
    execSync(`git merge ${DEV_BRANCH} --no-edit`, { stdio: 'inherit' })

    info('推送合并后的主分支到远程仓库...')
    execSync(`git push origin ${PRD_BRANCH}`, { stdio: 'inherit' })

    execSync(`git checkout ${DEV_BRANCH}`, { stdio: 'inherit' })

    log('Git 操作完成')
  } catch (err) {
    error('Git 操作失败')
    process.exit(1)
  }
}

// 执行部署
function deployProject(project: string) {
  try {
    switch (project) {
      case 'yuhe':
        log('开始部署 YUHE 项目...')
        execSync('npm run deploy:yuhe', { stdio: 'inherit' })
        break
      case 'moer_overseas':
        log('开始部署 MOER_OVERSEAS 项目...')
        execSync('npm run deploy:moer_overseas', { stdio: 'inherit' })
        break
      case 'all':
        log('开始部署所有项目...')
        execSync('npm run deploy', { stdio: 'inherit' })
        break
      default:
        error(`未知项目: ${project}`)
        process.exit(1)
    }
  } catch (err) {
    error('部署失败')
    process.exit(1)
  }
}

// 主函数
async function main() {
  log('===== Eliza Sales Monorepo 发布流程开始 =====')

  // 检查 Git 状态
  checkGitStatus()

  // 执行 Git 操作
  performGitOperations()

  // 选择项目 - 使用上下箭头
  const { project } = await inquirer.prompt([
    {
      type: 'list',
      name: 'project',
      message: '选择要部署的项目:',
      choices: [
        { name: 'YUHE 项目', value: 'yuhe' },
        { name: 'MOER_OVERSEAS 项目', value: 'moer_overseas' },
        { name: '两个项目都部署', value: 'all' },
        { name: '退出', value: 'exit' }
      ]
    }
  ])

  if (project === 'exit') {
    info('退出部署')
    process.exit(0)
  }

  // 直接部署，无需确认
  deployProject(project)
  log('===== 发布流程完成 =====')
}

// 执行主流程
main().catch((error) => {
  error(`主流程发生错误: ${error.message}`)
  process.exit(1)
})
