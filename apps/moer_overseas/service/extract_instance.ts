import { MoerOverseasVisualizedSopProcessor } from '../visualized_sop/visualized_sop_processor'
import { chatDBCommonClient, chatHistoryServiceClient } from './base_instance'
import { enterpriseName } from './global_data'
import { yCloudCommonMessageSender } from './instance'

export const moerOverseasVisualizedSopProcessor = new MoerOverseasVisualizedSopProcessor(enterpriseName, chatDBCommonClient, chatHistoryServiceClient, yCloudCommonMessageSender)