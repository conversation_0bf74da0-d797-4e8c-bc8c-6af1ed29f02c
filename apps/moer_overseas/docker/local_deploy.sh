#!/bin/bash

# MOER_OVERSEAS 本地部署脚本
# 用于本地测试构建和部署流程

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# 配置变量
PROJECT_NAME="moer_overseas"
LOCAL_IMAGE_TAG="${PROJECT_NAME}:local"
REMOTE_IMAGE_TAG="crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/${PROJECT_NAME}:latest"

# 主函数
main() {
    log "===== 开始 MOER_OVERSEAS 本地部署流程 ====="
    
    # 回到项目根目录
    cd ../../../
    
    # 构建 Docker 镜像
    log "构建 Docker 镜像: ${LOCAL_IMAGE_TAG}"
    docker buildx build --platform linux/amd64 --build-arg PROJECT=${PROJECT_NAME} -t ${LOCAL_IMAGE_TAG} . -f ./Dockerfile
    
    log_success "镜像构建完成"
    
    # 标记为远程镜像名称（用于本地测试）
    log "标记镜像为远程名称: ${REMOTE_IMAGE_TAG}"
    docker tag ${LOCAL_IMAGE_TAG} ${REMOTE_IMAGE_TAG}
    
    # 回到 docker 目录
    cd apps/${PROJECT_NAME}/docker
    
    # 停止现有容器
    log "停止现有容器..."
    docker-compose down || true
    
    # 启动服务
    log "启动服务..."
    docker-compose up -d
    
    # 显示状态
    log "显示服务状态..."
    docker-compose ps
    
    # 显示日志
    log "显示最近的日志..."
    docker-compose logs --tail=20
    
    log_success "===== MOER_OVERSEAS 本地部署完成 ====="
    log "容器正在运行，端口: 6001"
    log "环境变量: NODE_ENV=dev, WECHAT_NAME=moer_whatsapp_online_test"
    log "使用 'docker-compose logs -f' 查看实时日志"
    log "使用 'docker-compose down' 停止服务"
}

# 执行主函数
main "$@"
