import axios from 'axios'


describe('client_server_test', () => {
  it('TestMoerEvent', async () => {
    const data = {
      logid: 'k9OEmbW76VTwfmH7NDD4',
      examScore: 82,
      userId: 39814602,
      mobile: '13458697059',
      nationcode: '86',
      event: 'jinshuju_user_exam_score',
      project: 'international',
    }
    await axios.post('http://free-spirit.natapp1.cc/moer/event', data, { insecureHTTPParser: true })
  }, 60000)
})