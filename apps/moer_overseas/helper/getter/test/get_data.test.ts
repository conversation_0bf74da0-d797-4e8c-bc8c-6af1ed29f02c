import { Date<PERSON>elper } from 'lib/date/date'
import { DataService } from '../get_data'


describe('get_data', () => {
  it('test_date', () => {
    console.log(DateHelper.formatDate(new Date(), 'HH:mm:ss'))
  })

  it('test_getCourseStartTimeByCourseNo', async () => {
    console.log(await DataService.getCourseStartTimeByCourseNo(100))
  }, 60000)

  it('test_getCurrentWeekCourseNo', async () => {
    console.log(DataService.getCurrentWeekCourseNo())
  }, 60000)

  it('test_isPaySystemCourse', async () => {
    console.log(await DataService.isPaidSystemCourse('+85292203450_+85297257364'))
  }, 60000)

  // it('isRegisterCourse', async () => {
  //   console.log(await DataService.isRegisterCourse('+8613301849894_+85297257364'))
  // }, 60000)

  it('getCourseLink', async () => {
    console.log(await DataService.getCourseLink(1, '+8617326651677_+85297257364'))
  }, 60000)
})