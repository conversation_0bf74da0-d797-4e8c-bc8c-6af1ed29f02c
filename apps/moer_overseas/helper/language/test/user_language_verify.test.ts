import { chatStateStoreClient, chatHistoryServiceClient, chatDBClient } from '../../../service/base_instance'
import { UserLanguage } from '../user_language_verify'

describe('UserLanguage.verify Tests', () => {

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks()
  })

  describe('Basic Language Detection', () => {
    it('should detect Chinese language when Chinese characters > 60%', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3) // Trigger detection (3 % 3 === 0)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '老师好，我想要报名课程',
        '请问什么时候开始上课？',
        '我对冥想很感兴趣'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_1')

      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_1', {
        state: { language: UserLanguage.Language_ZH }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should detect English language when English characters > 60%', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(6) // Trigger detection (6 % 3 === 0)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '老师好，I want to register for the course',
        'When does the meditation class start today',
        'I am very interested in learning meditation techniques'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_2')

      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_2', {
        state: { language: UserLanguage.Language_EN }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should not change language when neither language reaches 60% threshold', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(9) // Trigger detection (9 % 3 === 0)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello 老师',
        '////////////////////////////////////',
        '！！！！！！！！！！！！！！！！！！！！'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_3')

      expect(chatStateStoreClient.update).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })
  })

  describe('Message Filtering', () => {
    it('should filter out image messages starting with 【普通图片】', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '【普通图片】这是一张显示课程安排的图片，包含了详细的时间表和课程内容介绍',
        'Hello teacher',
        '【视频】这是一个关于冥想练习的视频，讲的是为什么意大利面要配42号混凝土',
        'When does the class start?'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_filter')

      // Should detect English since image/video descriptions are filtered out
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_filter', {
        state: { language: UserLanguage.Language_EN }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should skip detection when filtered messages are too short', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '【普通图片】图片描述',
        '【视频】视频内容',
        'ok',
        'yes'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_short')

      // Should not update language due to insufficient characters after filtering
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })
  })

  describe('Language Stability', () => {
    it('should require higher threshold to switch from existing language', async () => {
      // Mock dependencies - user currently set to Chinese
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for course', // 65% English
        '你说什么屁话呢',
        '有多远滚多远，哪凉快哪呆着去'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: UserLanguage.Language_ZH })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_stability')

      // Should NOT switch to English because 65% < 70% (switch threshold)
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })

    it('should switch language when threshold is high enough', async () => {
      // Mock dependencies - user currently set to Chinese
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for the meditation course',
        'When does the five day meditation practice start?',
        'I am very interested in learning meditation techniques',
        'Thank you very much for your help and guidance'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: UserLanguage.Language_ZH })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_switch')

      // Should switch to English because > 70% English
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_switch', {
        state: { language: UserLanguage.Language_EN }
      })
    })
  })

  describe('Detection Timing - New Logic', () => {
    it('should detect on 1st message (first conversation)', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(1) // First message
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '老师好，我想要报名课程'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: UserLanguage.Language_EN })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_first')

      // Should detect on first message
      expect(chatHistoryServiceClient.getUserMessages).toHaveBeenCalled()
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_first', {
        state: { language: UserLanguage.Language_ZH }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should not detect on 2nd message', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(2) // Second message
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_second')

      // Should not detect on second message
      expect(chatHistoryServiceClient.getUserMessages).not.toHaveBeenCalled()
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should detect on 3rd message (confirmation) - only analyze 2nd and 3rd messages', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3) // Third message
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '老师好，我想要报名课程', // 1st message (Chinese) - should be ignored in 3rd round
        'When does the class start?', // 2nd message (English) - should be analyzed
        'I am very interested in meditation' // 3rd message (English) - should be analyzed
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: UserLanguage.Language_ZH })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_third')

      // Should detect on third message, but only analyze 2nd and 3rd messages (English)
      // Since 2nd and 3rd messages are English, should switch to English
      expect(chatHistoryServiceClient.getUserMessages).toHaveBeenCalled()
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_third', {
        state: { language: UserLanguage.Language_EN }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should verify 3rd round only analyzes 2nd and 3rd messages (not 1st)', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3) // Third message
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for the course', // 1st message (English) - should be ignored
        '老师好，我想要报名课程', // 2nd message (Chinese) - should be analyzed
        '我对冥想很感兴趣' // 3rd message (Chinese) - should be analyzed
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: UserLanguage.Language_EN })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_third_chinese')

      // Should detect Chinese based on 2nd and 3rd messages only (ignoring 1st English message)
      expect(chatHistoryServiceClient.getUserMessages).toHaveBeenCalled()
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_third_chinese', {
        state: { language: UserLanguage.Language_ZH }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should not detect on 4th and 5th messages', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Test 4th message
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(4)
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_fourth')

      expect(chatHistoryServiceClient.getUserMessages).not.toHaveBeenCalled()
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()

      // Test 5th message
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(5)
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_fifth')

      expect(chatHistoryServiceClient.getUserMessages).not.toHaveBeenCalled()
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should detect on 6th message (start of 3-round cycle)', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(6) // Sixth message
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '老师好，我想要报名课程',
        '请问什么时候开始上课？',
        '我对冥想很感兴趣',
        '费用是多少呢？',
        '需要准备什么吗？',
        '谢谢老师的回答'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_sixth')

      // Should detect on sixth message (6 >= 6 && 6 % 3 === 0)
      expect(chatHistoryServiceClient.getUserMessages).toHaveBeenCalled()
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_sixth', {
        state: { language: UserLanguage.Language_ZH }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should detect on 9th and 12th messages', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Test 9th message
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(9)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for the course'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_ninth')

      expect(chatHistoryServiceClient.getUserMessages).toHaveBeenCalled()
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_ninth', {
        state: { language: UserLanguage.Language_EN }
      })

      // Reset mocks for 12th message test
      jest.clearAllMocks()
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Test 12th message
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(12)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for the course'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_twelfth')

      expect(chatHistoryServiceClient.getUserMessages).toHaveBeenCalled()
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_twelfth', {
        state: { language: UserLanguage.Language_EN }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should stop detection after 12 messages', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(15) // > 12 messages
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_limit')

      // Should not detect after 12 messages
      expect(chatHistoryServiceClient.getUserMessages).not.toHaveBeenCalled()
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })
  })

  describe('New Detection Logic Integration Tests', () => {
    it('should follow the complete new detection pattern: 1st -> 3rd -> 6th -> 9th -> 12th', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      const testMessages = [
        '老师好，我想要报名课程', // 1st - should detect
        '请问什么时候开始？', // 2nd - should not detect
        '我对冥想很感兴趣', // 3rd - should detect
        '费用是多少呢？', // 4th - should not detect
        '需要准备什么吗？', // 5th - should not detect
        '谢谢老师的回答', // 6th - should detect
        '还有其他问题', // 7th - should not detect
        '我很期待参加', // 8th - should not detect
        '什么时候报名截止？', // 9th - should detect
        '好的，我明白了', // 10th - should not detect
        '那我先准备一下', // 11th - should not detect
        '再次感谢老师', // 12th - should detect
        '后续还有问题再联系' // 13th - should not detect (>12)
      ]

      const detectionRounds = [1, 3, 6, 9, 12] // Expected detection rounds
      const noDetectionRounds = [2, 4, 5, 7, 8, 10, 11, 13] // Should not detect

      // Test detection rounds
      for (const round of detectionRounds) {
        jest.clearAllMocks()
        mockCheckLanguageSwitchRequest.mockResolvedValue(false)

        chatStateStoreClient.increaseNodeCount = jest.fn()
        chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(round)
        chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue(
          testMessages.slice(0, round)
        )
        chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
        chatStateStoreClient.update = jest.fn()

        await UserLanguage.verify(`test_chat_round_${round}`)

        expect(chatHistoryServiceClient.getUserMessages).toHaveBeenCalled()
        // Should detect Chinese since all messages are in Chinese
        expect(chatStateStoreClient.update).toHaveBeenCalledWith(`test_chat_round_${round}`, {
          state: { language: UserLanguage.Language_ZH }
        })
      }

      // Test non-detection rounds
      for (const round of noDetectionRounds) {
        jest.clearAllMocks()
        mockCheckLanguageSwitchRequest.mockResolvedValue(false)

        chatStateStoreClient.increaseNodeCount = jest.fn()
        chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(round)
        chatHistoryServiceClient.getUserMessages = jest.fn()
        chatStateStoreClient.update = jest.fn()

        await UserLanguage.verify(`test_chat_round_${round}`)

        expect(chatHistoryServiceClient.getUserMessages).not.toHaveBeenCalled()
        expect(chatStateStoreClient.update).not.toHaveBeenCalled()
      }

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should detect English on first message and confirm on third', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Test 1st message - English detection
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(1)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for the meditation course'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_en_first')

      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_en_first', {
        state: { language: UserLanguage.Language_EN }
      })

      // Test 3rd message - Confirm English
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for the meditation course',
        '有多远滚多远哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈',
        '哪凉快哪呆着去'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: UserLanguage.Language_EN })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_en_third')

      // Should confirm English language (using confirmThreshold 0.6)
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_en_third', {
        state: { language: UserLanguage.Language_ZH }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })
  })

  describe('Language Switch Request Detection', () => {
    it('should detect and handle language switch request', async () => {
      // Mock language switch detection
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(true)

      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn()
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_switch_request', 'chinese better')

      // Should call language switch detection and return early
      expect(mockCheckLanguageSwitchRequest).toHaveBeenCalledWith('test_chat_switch_request', 'chinese better')
      expect(chatStateStoreClient.increaseNodeCount).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should continue normal detection when no switch request detected', async () => {
      // Mock language switch detection
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(1) // Trigger detection on 1st message
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for the course'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_no_switch', 'Hello teacher')

      // Should call language switch detection but continue with normal flow
      expect(mockCheckLanguageSwitchRequest).toHaveBeenCalledWith('test_chat_no_switch', 'Hello teacher')
      expect(chatStateStoreClient.increaseNodeCount).toHaveBeenCalled()
      // Should also trigger language detection since it's the 1st message
      expect(chatHistoryServiceClient.getUserMessages).toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty user messages', async () => {
      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_empty')

      // Should not update language with empty messages
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })

    it('should handle messages with only special characters', async () => {
      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '!!!',
        '???',
        '...',
        '123456'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_special')

      // Should not update language with only special characters
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })
  })

  describe('Language Switch Request Tests', () => {
    beforeEach(() => {
      // Mock LLM for language switch detection
      jest.clearAllMocks()
    })

    it('should detect Chinese switch request', async () => {
      // Mock dependencies
      chatHistoryServiceClient.getRecentConversations = jest.fn().mockResolvedValue([
        { role: 'user', content: 'Hello teacher' },
        { role: 'assistant', content: 'Hi there! How can I help you?' },
        { role: 'user', content: 'chinese better' }
      ])
      chatStateStoreClient.update = jest.fn()

      // Mock LLM directly in the module
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const { LLM } = require('lib/ai/llm/llm_model')
      const mockPredict = jest.fn().mockResolvedValue('SWITCH_TO_CHINESE')
      LLM.mockImplementation(() => ({ predict: mockPredict }))

      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'chinese better')

      expect(result).toBe(true)
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat', {
        state: { language: UserLanguage.Language_ZH }
      })
    })

    it('should detect English switch request', async () => {
      // Mock dependencies
      chatHistoryServiceClient.getRecentConversations = jest.fn().mockResolvedValue([
        { role: 'user', content: '老师好' },
        { role: 'assistant', content: '你好！有什么可以帮助你的吗？' },
        { role: 'user', content: 'english please' }
      ])
      chatStateStoreClient.update = jest.fn()

      // Mock LLM directly in the module
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const { LLM } = require('lib/ai/llm/llm_model')
      const mockPredict = jest.fn().mockResolvedValue('SWITCH_TO_ENGLISH')
      LLM.mockImplementation(() => ({ predict: mockPredict }))

      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'english please')

      expect(result).toBe(true)
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat', {
        state: { language: UserLanguage.Language_EN }
      })
    })

    it('should not switch when no language keywords present', async () => {
      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'How are you today?')

      expect(result).toBe(false)
    })

    it('should not switch when LLM determines no switch intent', async () => {
      // Mock dependencies
      chatHistoryServiceClient.getRecentConversations = jest.fn().mockResolvedValue([
        { role: 'user', content: 'I like Chinese food' }
      ])
      chatStateStoreClient.update = jest.fn()

      // Don't mock LLM - test real LLM response
      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'I like Chinese food')

      console.log('LLM result for "I like Chinese food":', result)

      // The result should be false since this is not a language switch request
      expect(result).toBe(false)
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    }, 30000) // Increase timeout for LLM call

    it('should test real LLM for language switch request', async () => {
      // Mock dependencies
      chatHistoryServiceClient.getRecentConversations = jest.fn().mockResolvedValue([
        { role: 'user', content: 'Hello teacher' },
        { role: 'assistant', content: 'Hi there! How can I help you?' },
        { role: 'user', content: 'chinese better' }
      ])
      chatStateStoreClient.update = jest.fn()

      // Test real LLM response for a clear language switch request
      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'chinese better')

      console.log('LLM result for "chinese better":', result)

      // This should be true since it's a clear language switch request
      expect(result).toBe(true)
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat', {
        state: { language: UserLanguage.Language_ZH }
      })
    }, 30000) // Increase timeout for LLM call

    it('should test real LLM for English switch request', async () => {
      // Mock dependencies
      chatHistoryServiceClient.getRecentConversations = jest.fn().mockResolvedValue([
        { role: 'user', content: '老师好' },
        { role: 'assistant', content: '你好！有什么可以帮助你的吗？' },
        { role: 'user', content: 'english please' }
      ])
      chatStateStoreClient.update = jest.fn()

      // Test real LLM response for English switch request
      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'english please')

      console.log('LLM result for "english please":', result)

      // This should be true since it's a clear language switch request
      expect(result).toBe(true)
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat', {
        state: { language: UserLanguage.Language_EN }
      })
    }, 30000) // Increase timeout for LLM call
  })

  // Original test case (preserved)
  describe('Legacy Tests', () => {
    it('testVerify (original)', async () => {
      const sentenceCN = '老师，我要报名课程 It\'s 11:30 am here what is the time there now'
      const sentenceEN = '想参加五天冥想营 It\'s 11:30 am here what is the time there now'

      chatDBClient.create = jest.fn().mockResolvedValue({})
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([sentenceEN])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()
      chatStateStoreClient.increaseNodeCount = jest.fn()

      await UserLanguage.verify('11')

      expect(chatStateStoreClient.increaseNodeCount).toHaveBeenCalled()
    }, 60000)
  })
})