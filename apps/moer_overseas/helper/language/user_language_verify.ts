import { IChattingFlag } from '../../state/user_flags'
import { LLM } from 'lib/ai/llm/llm_model'
import logger from 'model/logger/logger'
import { chatHistoryServiceClient, chatStateStoreClient } from '../../service/base_instance'

export class UserLanguage {

  public static Language_EN = 'en'
  public static Language_ZH = 'cn'
  private static userMessageNodeName = 'userLanguageVerifyNodeName'

  public static async verify(chat_id: string, currentUserMessage?: string) {

    await chatStateStoreClient.increaseNodeCount(chat_id, UserLanguage.userMessageNodeName)
    const userMessageCount = await chatStateStoreClient.getNodeCount(chat_id, UserLanguage.userMessageNodeName)

    // 首先检查客户是否主动要求切换语言（如Chinese better，eng pls等）
    if (currentUserMessage && await UserLanguage.checkLanguageSwitchRequest(chat_id, currentUserMessage)) {
      return
    }

    // 检查语言是否已被锁定，如果已锁定则跳过自动检测
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)
    if (flags.language_locked) {
      return
    }
    // 新的检测逻辑：
    // 第1轮：立即检测（首次对话）
    // 第3轮：再次检测确认
    // 第6轮及以后：每3轮检测一次（包含第12轮）
    const shouldDetect = userMessageCount === 1 ||
                        userMessageCount === 3 ||
                        (userMessageCount >= 6 && userMessageCount % 3 === 0 && userMessageCount <= 12)

    if (shouldDetect) {
      const userMessages = await chatHistoryServiceClient.getUserMessages(chat_id)

      // 根据不同的检测轮次选择不同的消息范围
      let messagesToAnalyze: string[]
      if (userMessageCount === 1) {
        // 第1轮：只分析第1条消息
        messagesToAnalyze = userMessages.slice(0, 1)
      } else if (userMessageCount === 3) {
        // 第3轮：只分析第2轮和第3轮的消息（跳过第1轮）
        messagesToAnalyze = userMessages.slice(1, 3)
      } else {
        // 第6轮及以后：分析所有消息
        messagesToAnalyze = userMessages
      }

      // 过滤掉图片消息（以【普通图片】开头）和视频消息（以【视频】开头）
      const filteredMessages = messagesToAnalyze.filter((msg) =>
        !msg.startsWith('【普通图片】') &&
        !msg.startsWith('【视频】') &&
        !msg.startsWith('【表情】')
      )

      const userMsgStr = filteredMessages.join(' ') // 用空格连接，保持单词边界

      // 计算中文字符数量（每个中文字符算作一个单位）
      const chineseMatches = userMsgStr.match(/[\u4e00-\u9fff\u3400-\u4dbf]/g)
      const chineseCount = chineseMatches ? chineseMatches.length : 0

      // 计算英文单词数量（连续的英文字母组成一个单词）
      const englishMatches = userMsgStr.match(/[a-zA-Z]+/g)
      const englishCount = englishMatches ? englishMatches.length : 0

      // 总单位数 = 中文字符数 + 英文单词数
      const totalUnits = chineseCount + englishCount

      // 如果过滤后的有效单位太少，跳过本次语言检测
      const MIN_UNIT_THRESHOLD = 5 // 至少5个单位（字符或单词）
      if (totalUnits < MIN_UNIT_THRESHOLD) {
        return
      }

      // 获取当前语言设置，避免频繁切换
      const currentLanguage = await UserLanguage.getLanguage(chat_id)

      // 计算比例：中文字符数/总单位数，英文单词数/总单位数
      const chineseRatio = chineseCount / totalUnits
      const englishRatio = englishCount / totalUnits

      // 添加调试日志
      logger.log(`Language detection for chat ${chat_id}: Chinese chars: ${chineseCount}, English words: ${englishCount}, Total units: ${totalUnits}, Chinese ratio: ${chineseRatio.toFixed(2)}, English ratio: ${englishRatio.toFixed(2)}, Current language: ${currentLanguage}, Message count: ${userMessageCount}`)

      // 增加语言稳定性：如果当前已有语言设置，需要更高的阈值才能切换
      const confirmThreshold = 0.6  // 确认当前语言的阈值
      const switchThreshold = 0.7   // 切换到新语言的阈值

      const chineseThreshold = currentLanguage === UserLanguage.Language_ZH ? confirmThreshold : switchThreshold
      const englishThreshold = currentLanguage === UserLanguage.Language_EN ? confirmThreshold : switchThreshold

      // 检查是否满足中文阈值
      if (chineseRatio > chineseThreshold) {
        if (currentLanguage !== UserLanguage.Language_ZH) {
          logger.log(`Language switched to Chinese for chat ${chat_id}: ratio ${chineseRatio.toFixed(2)} > threshold ${chineseThreshold}`)
        }
        await chatStateStoreClient.update(chat_id, {
          state:<IChattingFlag>{
            language:  UserLanguage.Language_ZH,
          }
        })
      }
      // 检查是否满足英文阈值（只有在不满足中文阈值的情况下才检查）
      else if (englishRatio > englishThreshold) {
        if (currentLanguage !== UserLanguage.Language_EN) {
          logger.log(`Language switched to English for chat ${chat_id}: ratio ${englishRatio.toFixed(2)} > threshold ${englishThreshold}`)
        }
        await chatStateStoreClient.update(chat_id, {
          state:<IChattingFlag> {
            language: UserLanguage.Language_EN
          }
        })
      }
      // 如果两种语言都不满足阈值，保持当前语言设置不变
      // 这样可以避免在语言检测不确定时频繁切换语言
      else {
        logger.log(`Language detection inconclusive for chat ${chat_id}: Chinese ratio ${chineseRatio.toFixed(2)} (threshold: ${chineseThreshold}), English ratio ${englishRatio.toFixed(2)} (threshold: ${englishThreshold}). Keeping current language: ${currentLanguage}`)
      }
    }
  }

  public static async getLanguage(chatId: string) {
    const language = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).language
    if (!language) {
      return UserLanguage.Language_ZH
    }
    return language
  }

  public static async getLanguageSetting(chatId: string) {
    const language = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).language
    const languageMap = {
      [UserLanguage.Language_ZH]: '**請使用繁體中文輸出**',
      [UserLanguage.Language_EN]: '**must output with english**'
    }
    return language ? languageMap[language] : ''
  }
  /**
   * 检查客户是否主动要求切换语言
   */
  public static async checkLanguageSwitchRequest(chat_id: string, currentMessage: string): Promise<boolean> {
    const languageKeywords = [
      'chinese', 'english', 'mandarin', 'cantonese', 'traditional', 'simplified', 'eng',
      '中文', '英文', '英语', '中国话', '普通话', '繁体', '简体',
      'switch language', 'change language', 'language preference',
      '切换语言', '换语言', '语言偏好',
      'can\'t english', 'cant english', 'no english', 'don\'t english', 'dont english',
      'only chinese', 'only mandarin', 'chinese only', 'mandarin only',
      '不会英文', '不懂英文', '只会中文', '只懂中文'
    ]

    const hasLanguageKeyword = languageKeywords.some((keyword) =>
      currentMessage.toLowerCase().includes(keyword.toLowerCase())
    )

    if (!hasLanguageKeyword) {
      return false
    }

    // 获取最近几轮对话作为上下文
    const recentMessages = await chatHistoryServiceClient.getRecentConversations(chat_id, 3) // 最近3轮对话
    const context = recentMessages.map((msg) => `${msg.role}: ${msg.content}`).join('\n')

    // 让AI分析客户是否真的想切换语言
    const analysisPrompt = `# 语言切换意图分析

## 任务
分析客户是否明确表达了想要切换对话语言的意图。

## 对话历史
${context}

## 当前客户消息
${currentMessage}

## 分析规则
1. 客户明确说要用中文/英文交流
2. 客户说当前语言不方便，想换语言
3. 客户直接要求"请用中文回复"或"please reply in English"
4. 客户说"chinese better"、"english please"等明确切换意图
5. 客户说"can't english"、"no english"、"only chinese"等表示不会英文
6. 客户说"不会英文"、"只会中文"等表示语言能力限制

## 输出格式
只输出以下之一：
- SWITCH_TO_CHINESE：客户想切换到中文
- SWITCH_TO_ENGLISH：客户想切换到英文
- NO_SWITCH：客户没有切换语言的意图`
    try {
      const llm = new LLM({
        maxTokens: 50,
        meta: { promptName: 'language_switch_detection', chat_id, description: '语言切换检测' }
      })

      const result = await llm.predict(analysisPrompt)
      const trimmedResult = result.trim()

      if (trimmedResult === 'SWITCH_TO_CHINESE') {
        await UserLanguage.switchLanguage(chat_id, UserLanguage.Language_ZH)
        return true
      } else if (trimmedResult === 'SWITCH_TO_ENGLISH') {
        await UserLanguage.switchLanguage(chat_id, UserLanguage.Language_EN)
        return true
      }

      return false
    } catch (error) {
      console.error('语言切换检测失败:', error)
      return false
    }
  }

  /**
   * 切换语言设置
   */
  private static async switchLanguage(chat_id: string, targetLanguage: string) {
    await chatStateStoreClient.update(chat_id, {
      state: <IChattingFlag>{
        language: targetLanguage,
        language_locked: true, // 用户明确表达语言偏好后锁定语言
      }
    })

    // 记录语言切换日志
    logger.log(`Language switched for chat ${chat_id} to ${targetLanguage} and locked`)
  }

  /**
   * 解锁语言设置，允许重新进行自动检测
   */
  public static async unlockLanguage(chat_id: string) {
    await chatStateStoreClient.update(chat_id, {
      state: <IChattingFlag>{
        language_locked: false,
      }
    })
    logger.log(`Language unlocked for chat ${chat_id}`)
  }
}