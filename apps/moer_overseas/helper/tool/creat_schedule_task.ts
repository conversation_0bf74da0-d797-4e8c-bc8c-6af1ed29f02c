


/*
 * 按照是否是课程前一周、课程周、课程结束后的第几周，1-7 进行 + 时间 进行任务创建。
 */
import { DateHelper } from 'lib/date/date'


export interface IScheduleTime {
  is_course_week?: boolean // true 表示课程周，false 表示课程前一周
  post_course_week?: number // 课程结束后的第几周，1 表示第一周，2 表示第二周，依此类推
  day: number // 1-7 表示周一到周日
  time: string // 格式为 'HH:MM:SS'，例如 '08:00:00'
}

/**
 * 返回 date1 是否 在 date 2 之前
 * @param date1
 * @param date2
 */
export function isScheduleTimeBefore(date1: IScheduleTime, date2: IScheduleTime): boolean {
  // 调用 isScheduleTimeAfter 函数，将参数顺序颠倒
  return isScheduleTimeAfter(date2, date1)
}

/**
 * 返回 date1 是否 在 date 2之后
 * @param date1
 * @param date2
 */
export function isScheduleTimeAfter(date1: IScheduleTime, date2: IScheduleTime): boolean {
  // 定义一个排序规则：
  // 如果存在 post_course_week，则返回它的值（默认 >= 1，表示上课周之后的周次）
  // 如果没有 post_course_week，则根据 is_course_week 判断：
  //   - 课程周（is_course_week 为 true）返回 0
  //   - 课程前一周（is_course_week 为 false）返回 -1
  function getWeekRank(schedule: IScheduleTime): number {
    if (typeof schedule.post_course_week === 'number') {
      // post_course_week 存在时，其值默认 >= 1，表示课程后一周、后两周等
      return schedule.post_course_week
    }
    if (typeof schedule.is_course_week === 'boolean') {
      // 课程周 > 课程前一周，因此：课程周返回 0，课程前一周返回 -1
      return schedule.is_course_week ? 0 : -1
    }
    // 如果都没提供，则默认当作课程周前一周
    return -1
  }

  const rank1 = getWeekRank(date1)
  const rank2 = getWeekRank(date2)

  // 如果不在同一周，直接比较周级别
  if (rank1 !== rank2) {
    return rank1 > rank2
  }

  // 同一周，则先比较 day（1～7，数字越大越靠后）
  if (date1.day !== date2.day) {
    return date1.day > date2.day
  }

  // 如果 day 也相同，则比较具体时间，利用 DateHelper.isTimeAfter 函数
  return DateHelper.isTimeAfter(date1.time, date2.time)
}