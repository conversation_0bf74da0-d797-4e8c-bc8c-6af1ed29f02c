import ElasticSearchService from 'model/elastic_search/elastic_search'
import { StringHelper } from 'lib/string'



export interface ImageSearchResult {
  url: string
  description: string
}

export class MoreImageGeneral {

  public static index = 'moer_image_rag_2048d'

  public static async searchSalesCaseImage(chunk: string, tag:string[], resNum: number): Promise<ImageSearchResult[]> {
    if (StringHelper.isEmpty(chunk)) {
      return []
    }
    const filter = {
      bool: {
        should: tag.map((tag) => ({ term: { 'metadata.tag': tag } })),
      },
    }
    const searchResult = await ElasticSearchService.embeddingSearch(
      this.index,
      chunk,
      resNum,
      0.9,
      filter
    )
    return searchResult.map((item) => {
      return {
        description: this.addSquareBracket(item.metadata.chunk),
        url: item.metadata.a
      }
    })
  }
  private static addSquareBracket(content: string): string {
    return `[${content}]`
  }
}