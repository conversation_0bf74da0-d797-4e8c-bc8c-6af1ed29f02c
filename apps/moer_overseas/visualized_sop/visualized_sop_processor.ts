import { LLM } from 'lib/ai/llm/llm_model'
import { LLMReply } from 'service/llm/llm_reply'
import { getState } from 'service/llm/state'
import { HandleActionOption, VisualizedSopProcessor } from 'service/visualized_sop/visualized_sop_processor'
import { ContentDynamicPrompt, SopScheduleTime } from 'service/visualized_sop/visualized_sop_type'
import { XMLHelper } from 'lib/xml/xml'
import { ContextBuilder } from '../workflow/context'
import { actionCustomMap, conditionJudgeMap, linkSourceVariableTagMap, textVariableMap } from './visualized_sop_variable'
import dayjs from 'dayjs'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'
import { DataService } from '../helper/getter/get_data'
import { yCloudCommonMessageSender } from '../service/instance'

export class MoerOverseasVisualizedSopProcessor extends VisualizedSopProcessor {
  async handleActionDynamicPrompt(chatId: string, userId: string, action: ContentDynamicPrompt, opt: HandleActionOption): Promise<void> {
    const state = await getState(chatId, userId)
    const context = await ContextBuilder.build({
      state,
      retrievedKnowledge: action.includeRAG,
      customerMemory: action.includeMemory,
      customerBehavior: action.includeUserBehavior,
      customerPortrait: action.includeUserSlots,
      temporalInformation: action.includeTimeInfo,
      talkStrategyPrompt: action.dynamicPrompt,
      customerChatRounds: action.chatHistoryRounds,
      customPrompt: action.customPrompt == '' ? '# 动态话术\n你需要根据任务与客户画像来生成简短、自然、贴近客户的销售话术，直接面向客户' : '',
    })

    const llm = new LLM({
      maxTokens: 400,
      projectName:'moer_overseas',
      promptName: `sop_${action.description}`,
      meta: {
        chat_id: state.chat_id,
        round_id: state.round_id,
        promptName: `sop_${action.description}`,
      },
    })
    const res = await llm.predictMessage(context)
    if (action.noSplit) {
      await yCloudCommonMessageSender.sendMsg(chatId, [{
        type: SendMessageType.text,
        text: res,
        description: action.description
      }], {
        roundId:state.round_id,
        sopId:opt.sop_id
      })
    } else {
      const splitSentence = LLMReply.splitIntoSentencesWithMaxSentences(res, 2)
      await yCloudCommonMessageSender.sendMsg(chatId, splitSentence.map((item) => {
        return {
          type: SendMessageType.text,
          text: item,
          description: action.description
        }
      }), {
        roundId:state.round_id,
        sopId:opt.sop_id
      })
    }
  }

  async judgeDynamicCondition(chatId: string, userId: string, condition: string): Promise<boolean> {
    const state = await getState(chatId, userId, '')
    const context = await ContextBuilder.build({
      state,
      customerMemory: true,
      injectChatHistory: true,
      talkStrategyPrompt: condition,
      customPrompt: MoerOverseasVisualizedSopProcessor.getThinkPrompt(),
    })

    const result = await LLM.predictMessage(context, { projectName:'moer_overseas', promptName:'think_condition', maxTokens:1000, meta:{ chatId:chatId, promptName:'think_condition'  } })
    const judge = XMLHelper.extractContent(result, 'judge') || ''
    return judge == 'true'
  }

  private static getThinkPrompt() {
    return `# 角色设定
- 你是顶级销售，擅长根据对话记录，客户行为和客户画像，判断条件是否是正确的

## 主要任务
- 思考（think）总结聊天记录，判断是否和条件内容相关，结合客户行为和客户画像判断条件是否正确
- 判断（judge）只输出"true"或"false",条件正确则输出"true",条件错误则输出"false"

## 格式要求
- 请先将思考输出到 <think></think> 标签中
- 然后将判断输出到 <judge></judge> 标签中`
  }

  getActionCustomMap(): Record<string, (params: { chatId: string; userId: string;opt:HandleActionOption }) => Promise<void>> {
    return actionCustomMap
  }
  getConditionJudgeMap(): Record<string, ((params: { chatId: string; userId: string; }) => Promise<boolean>)> {
    return conditionJudgeMap
  }
  getLinkSourceVariableTagMap(): Record<string, (params: { chatId: string; userId: string; }) => Promise<string>> {
    return linkSourceVariableTagMap
  }
  getTextVariableMap(): Record<string, (params: { chatId: string; userId: string; }) => Promise<string>> {
    return textVariableMap
  }
}

export async function calSopTime(time:SopScheduleTime, chatId:string):Promise<Date> {
  const day0 = dayjs(await DataService.getCourseStartTime(chatId)).hour(0).minute(0).second(0).subtract(1, 'day')
  console.log(day0.format('YYYY-MM-DDTHH:mm:ssZ[Z]'))
  const timeStringSplited = time.time.split(':')
  return day0.add(time.week * 7 + time.day, 'day').hour(Number(timeStringSplited[0])).minute(Number(timeStringSplited[1])).second(Number(timeStringSplited[2])).toDate()
}