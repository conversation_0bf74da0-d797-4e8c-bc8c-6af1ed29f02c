{"name": "moer_overseas", "version": "1.0.0", "scripts": {"tsc-check": "tsc --noEmit", "prisma_generate": "cd prisma && pnpm dlx prisma generate", "prisma_db_push": "npx prisma db push", "client:whatsapp": "export NODE_ENV=dev WECHAT_NAME=moer_whatsapp_test && ts-node client/client_server.ts", "client:whatsapp:online": "export NODE_ENV=dev WECHAT_NAME=moer_whatsapp1 && ts-node client/client_server.ts", "moer_overseas:event:server": "ts-node client/event_foraward/event_server.ts", "client": "ts-node client/client_server.ts", "local": "ts-node local.ts", "deploy": "ts-node docker/deploy.ts"}, "author": "free spirit", "license": "ISC", "devDependencies": {"@types/express": "^4.17.19", "@types/jest": "^30.0.0", "@types/node": "^20.7.0", "jest": "^29.7.0", "prisma": "^6.13.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"langchain": "^0.3.33", "@langchain/core": "^0.3.75", "@prisma/client": "^6.13.0", "@types/inquirer": "^9.0.8", "axios": "^1.10.0", "bullmq": "^5.12.9", "chalk": "^4.1.2", "config": "workspace:*", "dayjs": "^1.11.13", "express": "^4.21.2", "inquirer": "^8.2.6", "js-yaml": "^4.1.0", "lib": "workspace:*", "model": "workspace:*", "openai": "^4.98.0", "service": "workspace:*", "zod": "^3.23.8"}}