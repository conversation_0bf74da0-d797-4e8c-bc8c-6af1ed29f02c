import { BaseExtractUserSlots } from 'service/user_slots/extract_user_slots'

const TopicRecommendations = `- 基本信息：客户年龄（整数），性别，居住地，生活角色
- 兴趣爱好：书籍，电影，音乐，美食，运动
- 工作：职位，工作技能
- 过往冥想经验（客户以前的冥想经验，包括是否有经验和使用过的方法，冥想入门营的感受不应分类为此类） 
- 痛点：情绪焦虑，经济压力，家庭矛盾
- 冥想目标（明确提及的冥想目标）：改善睡眠，缓解压力
- 冥想课后感受（冥想入门营课后感受）：第一节课感受，第二节课感受，第三节课感受，第四节课感受
- 购买意向：系统班（客户是否对系统班感兴趣）`

const TopicRules = `- 不需要提取是否报名“五天冥想入门营”
- 不需要提取课程进度，观看情况，参与安排和课程完成情况，课程如如冥想入门营，先导课，小讲堂，第一节课，第二节课，第三节课
- 不需要提取客户的咨询或技术问题
- 购买意向::系统班：可以通过客户是否询问系统班相关信息来判断是否对系统班感兴趣`



export class ExtractUserSlots extends BaseExtractUserSlots {
  getTopicRules(): string {
    return TopicRules
  }

  getTopicRecommendations(): string {
    return TopicRecommendations
  }
}