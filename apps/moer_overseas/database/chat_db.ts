import { Config } from 'config'
import { PrismaMongoClient } from '../helper/mongodb/prisma'
import { IChat } from 'service/database/chat'
import { PrismaClient } from '../prisma_client'


export class ChatDB {
  private mongoClient:PrismaClient
  constructor(mongoClient:PrismaClient) {
    this.mongoClient = mongoClient
  }
  public async create(chat: IChat): Promise<IChat> {
    // @ts-ignore fuck you, primsa
    return PrismaMongoClient.getInstance().chat.create({
      data: chat
    })
  }
  public async getByMoerId(moerId: string) {
    if (!moerId) {
      throw new Error('moer_id is required')
    }

    if (Config.setting.wechatConfig?.id && !Config.setting.eventForward) {
      return this.mongoClient.chat.findFirst({
        where: {
          moer_id: moerId,
          wx_id: Config.setting.wechatConfig?.id as string
        }
      })
    } else {
      return this.mongoClient.chat.findFirst({
        where: {
          moer_id: moerId
        }
      })
    }
  }


  public async getById(id: string): Promise<IChat | null> {
    //@ts-ignore fuck
    return this.mongoClient.chat.findUnique({
      where: {
        id
      }
    })
  }

  public async setHumanInvolvement(chatId: string, isHumanInvolved: boolean) {
    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        is_human_involved: isHumanInvolved
      }
    })
  }

  public async updatePhoneNumber(chatId: string, phoneNumber: string) {
    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        phone_number: phoneNumber
      }
    })
  }

  public async updateEmail(chatId: string, email: string) {
    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        email: email
      }
    })
  }

  public async getPhoneNumber(chatId: string) {
    const data = await this.mongoClient.chat.findFirst({
      where: {
        id: chatId
      },
      select: {
        phone_number: true
      }
    })

    if (!data) {
      return null
    }

    return data.phone_number
  }

  public async getEmail(chatId: string) {
    const data = await this.mongoClient.chat.findFirst({
      where: {
        id: chatId
      },
      select: {
        email: true
      }
    })
    if (!data) {
      return null
    }
    return data.email
  }

  public async updateMoerId(chatId: string, moerId: string) {
    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        moer_id: moerId
      }
    })
  }

  public async updateCourseNo(chatId: string, courseNo: number) {
    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        course_no: courseNo
      }
    })
  }
}