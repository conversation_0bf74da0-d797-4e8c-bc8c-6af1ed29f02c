import NextAuth from 'next-auth'
import Credentials from 'next-auth/providers/credentials'

export const { handlers, signIn, signOut, auth } = NextAuth({
  secret:'6X06w0mpJe824lZJ74nO5VtArXlu_HWoK7XcXQ1HywPyjIls5WdnmPb7WE-emsxNx1i6-NUsq12nOOQ4GwLpJQ',
  pages:{
    signIn:'/login',
    signOut:'/login'
  },
  callbacks: {
    authorized: async ({ auth }) => {
      // Logged in users are authenticated, otherwise redirect to login page
      return Boolean(auth)
    },
  },
  providers:[
    Credentials({
      credentials: {
        account: {},
        password: {}
      },
      authorize: async(credentials) => {
        if (credentials.account == 'freespirit' && credentials.password == 'helloworld') {
          return {
            name:credentials.account as string,
          }
        } else if (credentials.account == 'visitor' && credentials.password == 'hello') {
          return {
            name:credentials.account as string,
          }
        } else {
          return null
        }
      }
    })
  ]
})