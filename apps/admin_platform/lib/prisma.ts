import { PrismaMongoClient } from 'model/mongodb/prisma'
import { PrismaMongoClient as MoerOverseasPrismaMongoClient } from 'moer_overseas/helper/mongodb/prisma'
import { PrismaMongoClient as YuhePrismaMongoClient } from 'yuhe/database/prisma'
import { PrismaMongoClient as HaoguPrismaMongoClient } from 'haogu/src/database/prisma'

// 扩展全局对象类型以支持 Prisma 实例
const globalForPrisma = globalThis as unknown as {
  yuhePrisma?: ReturnType<typeof PrismaMongoClient.newInstance>
  moerOverseasCommonPrisma?: ReturnType<typeof PrismaMongoClient.newInstance>
  moerOverseasPrisma?: ReturnType<typeof MoerOverseasPrismaMongoClient.getInstance>
  prismaConfig?: ReturnType<typeof PrismaMongoClient.getConfigInstance>
  haoguCommonPrisma?: ReturnType<typeof HaoguPrismaMongoClient.getCommonInstance>
  haoguPrisma?: ReturnType<typeof HaoguPrismaMongoClient.getInstance>
}

// 主数据库实例
export const yuhePrisma =
  globalForPrisma.yuhePrisma ||
  YuhePrismaMongoClient.getInstance()

// 配置数据库实例
export const prismaConfig =
  globalForPrisma.prismaConfig ||
  PrismaMongoClient.getConfigInstance()

export const moerOverseasCommonPrisma =
  globalForPrisma.moerOverseasCommonPrisma ||
  PrismaMongoClient.newInstance('moer_overseas')

export const moerOverseasPrisma =
  globalForPrisma.moerOverseasPrisma ||
  MoerOverseasPrismaMongoClient.getInstance()

export const haoguCommonPrisma =
  globalForPrisma.haoguCommonPrisma ||
  HaoguPrismaMongoClient.getCommonInstance()

export const haoguPrisma =
  globalForPrisma.haoguPrisma ||
  HaoguPrismaMongoClient.getInstance()

// 在开发环境中将实例保存到全局变量，防止热重载时创建多个实例
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.yuhePrisma = yuhePrisma
  globalForPrisma.prismaConfig = prismaConfig
  globalForPrisma.moerOverseasCommonPrisma = moerOverseasCommonPrisma
  globalForPrisma.moerOverseasPrisma = moerOverseasPrisma
  globalForPrisma.haoguCommonPrisma = haoguCommonPrisma
  globalForPrisma.haoguPrisma = haoguPrisma
}

// 兼容现有的 PrismaMongoClient 类接口
export class AdminPrismaMongoClient {
  public static getYuheInstance() {
    return yuhePrisma
  }

  public static getConfigInstance() {
    return prismaConfig
  }

  public static getMoerOverseasCommonInstance() {
    return moerOverseasCommonPrisma
  }

  public static getMoerOverseasInstance() {
    return moerOverseasPrisma
  }

  public static getHaoguCommonInstance() {
    return haoguCommonPrisma
  }

  public static getHaoguInstance() {
    return haoguPrisma
  }

  // 添加清理方法，用于优雅关闭连接
  public static async disconnect(): Promise<void> {
    await Promise.all([
      yuhePrisma.$disconnect(),
      moerOverseasCommonPrisma.$disconnect(),
      moerOverseasPrisma.$disconnect(),
      prismaConfig.$disconnect()
    ])
  }
}
