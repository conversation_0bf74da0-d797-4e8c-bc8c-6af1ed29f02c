'use client'
import { Sop } from '@/app/type/sop'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { Action, ActionType, TextType } from 'service/visualized_sop/visualized_sop_type'
import { testSopChatIdKey } from './sopShow'

export function SopDetail({
  id,
  querySopById,
  copySop,
  testAction,
  testSop
}:{
  id:string
  querySopById(id: string): Promise<Sop>
  copySop(sop_id: string): Promise<Sop>
  testAction(chatId: string, id: string): Promise<void>
  testSop(chatId:string, sopId:string): Promise<void>
}) {
  const router = useRouter()
  const [sop, setSop] = useState<Sop | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  useEffect(() => {
    toast
      .promise(querySopById(id), {
        pending: 'query pending',
        success: 'query success',
        error: 'query error',
      })
      .then((resultSop) => {
        setSop(resultSop)
      })
  }, [id])
  return (
    <div className="m-2 p-2">
      {sop ? (
        <>
          <div className="flex justify-between">
            <div className="text-4xl">{sop.title}</div>
            <div className="flex gap-2">
              <button
                disabled={loading}
                className="btn btn-neutral disabled:btn-disabled"
                onClick={() => {
                  setLoading(true)
                  toast
                    .promise(testSop(localStorage.getItem(testSopChatIdKey) ?? '', id), {
                      pending: 'test pending',
                      success: 'test success',
                      error: {
                        render({ data }) {
                          return `${data}`
                        },
                      },
                    })
                    .finally(() => {
                      setLoading(false)
                    })
                }}
              >
                    test
              </button>
              <button
                disabled={loading}
                className="btn btn-neutral disabled:btn-disabled"
                onClick={() => {
                  setLoading(true)
                  toast
                    .promise(testAction(localStorage.getItem(testSopChatIdKey) ?? '', id), {
                      pending: 'test pending',
                      success: 'test success',
                      error: {
                        render({ data }) {
                          return `${data}`
                        },
                      },
                    })
                    .finally(() => {
                      setLoading(false)
                    })
                }}
              >
                    test action
              </button>

              <Link href={`./${id}/edit`} className="btn"> 编辑</Link>
              <button className="btn" onClick={() => {
                toast.promise(copySop(id), {
                  pending:'copy pending',
                  success:'copy success',
                  error:'copy error'
                }).then((sop) => {
                  router.push(`./${sop.id}`)
                })
              }}>复制</button>
            </div>
          </div>
          <div>
            第{sop.week}周 第{sop.day}天 {sop.time}
          </div>
          <div>
            tag: {sop.tag}
          </div>
          <div className="divider"></div>
          {sop.situations.map((situation, index) => {
            return (
              <div key={index}>
                <div>场景 {index + 1}</div>
                <div className="flex">
                  <span>条件：</span>
                  {situation.conditions.map((condition, index) => {
                    return (
                      <div key={index}>
                        {!condition.isOrNotIs && '非'} {condition.condition}
                      </div>
                    )
                  })}
                </div>
                <div>
                  {situation.action.map((actionJson, index) => {
                    const action = actionJson as Action
                    return (
                      <div className="whitespace-pre-wrap" key={index}>
                        {index != 0 && <div className="divider" />}
                        <div>步骤{index + 1}：</div>
                        <div>类型：{action.type}</div>
                        {action.type == ActionType.text && (
                          <div className="rounded-md border border-gray-300 p-2 whitespace-pre">
                            {action.textList.map((text, index) => {
                              return (
                                <span key={index}>
                                  {text.type == TextType.fixed && text.text}
                                  {text.type == TextType.variable && (
                                    <span className="badge badge-neutral">
                                      {text.tag}
                                    </span>
                                  )}
                                </span>
                              )
                            })}
                          </div>
                        )}
                        {action.type == ActionType.custom && (
                          <div className="rounded-md border border-gray-300 p-2">
                            tag:{action.tag}
                          </div>
                        )}
                        {action.type != ActionType.custom &&
                          action.type != ActionType.text &&
                          JSON.stringify(action, null, 2)}
                      </div>
                    )
                  })}
                </div>
                <div className="divider divider-neutral"></div>
              </div>
            )
          })}
        </>
      ) : (
        <span className="loading loading-dots loading-xl"></span>
      )}
    </div>
  )
}