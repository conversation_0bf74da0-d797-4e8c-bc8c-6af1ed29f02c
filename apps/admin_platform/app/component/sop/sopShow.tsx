'use client'

import Link from 'next/link'
import { Dispatch, RefObject, SetStateAction, useEffect, useRef, useState } from 'react'
import { toast } from 'react-toastify'
import { IoSettingsSharp } from 'react-icons/io5'
import { Sop } from '@/app/type/sop'

export const testSopChatIdKey = 'test_sop_chat_id'
export function SopShow({
  sops,
  setSops,
  deleteSop,
  testAction,
  updateSop,
  selectedSop,
  setSelectedSop,
}: {
  sops: Sop[]
  setSops: Dispatch<SetStateAction<Sop[]>>
  deleteSop(id: string): Promise<void>
  testAction(chatId: string, id: string): Promise<void>
  updateSop(sop_id: string, sop: Partial<Sop>): Promise<void>
  selectedSop: string[]
  setSelectedSop: Dispatch<SetStateAction<string[]>>
}) {
  const [loading, setLoading] = useState(false)
  const [testSopChatId, setTestSopChatId] = useState('')
  const [waitDeleteSopId, setWaitDeleteSopId] = useState<string>('')
  const confirmDeleteSopDialogRef = useRef<HTMLDialogElement>(null)
  useEffect(() => {
    setTestSopChatId(localStorage.getItem(testSopChatIdKey) ?? '')
  }, [])
  return (
    <div>
      <table className="table-sm table">
        <thead>
          <tr>
            <th><input className='checkbox' type='checkbox' checked={selectedSop.length == sops.length} onChange={(e) => {
              if (e.currentTarget.checked) {
                setSelectedSop(sops.map((item) => item.id))
              } else {
                setSelectedSop([])
              }
            }}/></th>
            <th>标题</th>
            <th>周</th>
            <th>日</th>
            <th className="text-center">时间</th>
            <th className="text-center">tag</th>
            <th className="text-center">action</th>
            <th>enable</th>
          </tr>
        </thead>
        <tbody>
          {sops.map((item, index) => {
            return (
              <tr key={item.id}>
                <th><input type='checkbox' className='checkbox' checked={selectedSop.includes(item.id)} onChange={(e) => {
                  if (e.currentTarget.checked && !selectedSop.includes(item.id)) {
                    setSelectedSop([...selectedSop, item.id])
                  } else {
                    setSelectedSop(selectedSop.filter((sop) => sop != item.id))
                  }
                }} /></th>
                <td>{item.title}</td>
                <td>{item.week}</td>
                <td>{item.day}</td>
                <td className="text-center">{item.time}</td>
                <td className="text-center">{item.tag}</td>
                <td className="flex justify-center gap-2">
                  <button
                    disabled={loading}
                    className="btn btn-neutral disabled:btn-disabled"
                    onClick={() => {
                      confirmDeleteSopDialogRef.current?.showModal()
                      setWaitDeleteSopId(item.id)
                    }}
                  >
                    delete
                  </button>
                  <Link href={`../../${item.id}`}>
                    <button className="btn btn-neutral">more</button>
                  </Link>
                  <button
                    disabled={loading}
                    className="btn btn-neutral disabled:btn-disabled"
                    onClick={() => {
                      setLoading(true)
                      toast
                        .promise(testAction(localStorage.getItem(testSopChatIdKey) ?? '', item.id), {
                          pending: 'test pending',
                          success: 'test success',
                          error: {
                            render({ data }) {
                              return `${data}`
                            },
                          },
                        })
                        .finally(() => {
                          setLoading(false)
                        })
                    }}
                  >
                    test
                  </button>
                </td>
                <td>
                  <input type="checkbox" className="toggle toggle-success disabled:" checked={item.enable} disabled={loading} onChange={(e) => {
                    const willEnable = e.currentTarget.checked
                    setLoading(true)
                    toast.promise(async() => {
                      await updateSop(item.id, {
                        enable:willEnable
                      })
                    }, {
                      pending:'enable change pending',
                      error:'enable change error',
                      success:'enable change success'
                    }).then(() => {
                      const newSops = [...sops]
                      for (const sop of newSops) {
                        if (sop.id == item.id) {
                          sop.enable = willEnable
                        }
                      }
                      setSops(newSops)
                    }).finally(() => {
                      setLoading(false)
                    })
                  }}/>
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
      <div className="dropdown dropdown-left dropdown-end fixed right-10 bottom-10">
        <div tabIndex={0} role="button" className="btn m-1 btn-circle btn-neutral"><IoSettingsSharp size={26}/></div>
        <div tabIndex={0} className="dropdown-content bg-base-100 rounded-box z-1 p-8 shadow-md">
          <form onSubmit={(e) => {
            e.preventDefault()
            const form = new FormData(e.currentTarget)
            const id = form.get('id') as string
            window.localStorage.setItem('test_sop_chat_id', id)
            toast.success('save success')
          }} className='flex items-center gap-2'>
            <label className='label'>测试chat_id</label>
            <input type="text" name='id' className='input focus-within:outline-0 w-96' defaultValue={testSopChatId}/>
            <input type="submit" value={'save'} className='btn btn-info btn-soft'/>
          </form>
        </div>
      </div>
      <ConfirmDeleteSopButtonAndDialog deleteSop={deleteSop} sopId={waitDeleteSopId} confirmDeleteSopDialogRef={confirmDeleteSopDialogRef} setSops={setSops}/>
    </div>
  )
}

function ConfirmDeleteSopButtonAndDialog({
  sopId,
  deleteSop,
  confirmDeleteSopDialogRef,
  setSops
}:{
  sopId:string
  deleteSop(id: string): Promise<void>
  confirmDeleteSopDialogRef:RefObject<HTMLDialogElement | null>
  setSops: Dispatch<SetStateAction<Sop[]>>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  return <>
    <dialog ref={confirmDeleteSopDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">delete sop?</h3>
        <fieldset className="fieldset">
          <legend className="fieldset-legend">delete sop</legend>
        </fieldset>
        <div className="modal-action">
          <button className="btn btn-error btn-soft disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            toast.promise(deleteSop(sopId), {
              pending:'delete pending',
              success:'delete success',
              error: 'delete error'
            }).then(() => {
              setSops((sops) =>
                sops.filter((info) => info.id != sopId),
              )
              confirmDeleteSopDialogRef.current?.close()
            }).finally(() => {
              setLoading(false)
            })
          }}>delete!!!</button>
          <button className="btn" onClick={() => {
            confirmDeleteSopDialogRef.current?.close()
          }}>cancel</button>
        </div>
      </div>
    </dialog>
  </>
}