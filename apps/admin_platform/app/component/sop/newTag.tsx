'use client'
import { AccountData } from '@/app/type/account'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'

export function NewTag({
  queryAccounts,
  createTag
}:{
queryAccounts(): Promise<AccountData[]>
createTag(name: string, enableAccounts: string[]): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(true)
  const [accounts, setAccounts] = useState<AccountData[]>([])
  const router = useRouter()

  useEffect(() => {
    toast.promise(queryAccounts, {
      pending:'query pending',
      success:'query success',
      error: 'query error'
    }).then((configs) => {
      setAccounts(configs)
    }).finally(() => {
      setLoading(false)
    })
  }, [])

  return <div className="flex min-h-screen justify-center pt-30">
    <form action={(form) => {
      const accounts = form.getAll('accounts') as string[]
      const name = (form.get('name') ?? '') as string
      setLoading(true)
      toast.promise(createTag(name, accounts), {
        pending:'create pending',
        success: 'create success',
        error: 'create error'
      }).then(() => {
        router.push('.')
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">new tags</legend>

        <label className="label">name</label>
        <input type="text" className="input" name='name' required placeholder="name" disabled={loading} />

        <label className="label">accounts</label>
        {accounts.map((item) => {
          return <div className='flex gap-2 items-center' key={item.id}>
            <input type='checkbox' className='checkbox' name='accounts' disabled={loading} value={item.id}/><label>{item.accountName}</label>
          </div>
        })}

        <button className="btn btn-neutral mt-4 disabled:btn-disabled" disabled={loading}>create</button>
      </fieldset>
    </form>
  </div>

}