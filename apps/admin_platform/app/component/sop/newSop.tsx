'use client'
import { Sop } from '@/app/type/sop'
import { useState, useEffect } from 'react'
import { ActionType, Situation } from 'service/visualized_sop/visualized_sop_type'
import { SopEdit } from './sopEdit'
import { WhatsappTemplate } from 'service/message_handler/ycloud/template_type'

export function NewSop({
  tag,
  topic,
  getConditionJudgeKeys,
  getCustomKeys,
  getLinkSourceVariableTagKeys,
  getVariableMapKeys,
  saveSop,
  getYCloudTemplateList,
  enableActionType
}: {
  tag:string
  topic:string
  getConditionJudgeKeys(): Promise<string[]>
  getCustomKeys(): Promise<string[]>
  getVariableMapKeys(): Promise<string[]>
  getLinkSourceVariableTagKeys(): Promise<string[]>
  saveSop(sop: Omit<Sop, 'id'>): Promise<void>
  getYCloudTemplateList?:()=>Promise<WhatsappTemplate[]>
  enableActionType:ActionType[]
}) {
  const [situations, setSituations] = useState<Situation[]>([
    {
      conditions: [],
      action: [],
    },
  ])
  const [conditionJudgeKeys, setConditionJudgeKeys] = useState<string[]>([])
  const [customKeys, setCustomKeys] = useState<string[]>([])
  const [variableKeys, setVariableKeys] = useState<string[]>([])
  const [linkSourceVariableTagKeys, setLinkSourceVariableTagKeys] = useState<
    string[]
  >([])
  const [title, setTitle] = useState<string>('')
  const [week, setWeek] = useState<number>(0)
  const [day, setDay] = useState<number>(1)
  const [time, setTime] = useState<string>('')
  const [whatsappTemplate, setWhatsappTemplate] = useState<WhatsappTemplate[]>([])
  useEffect(() => {
    getConditionJudgeKeys().then((keys) => {
      setConditionJudgeKeys(keys)
    })
    getCustomKeys().then((keys) => {
      setCustomKeys(keys)
    })
    getVariableMapKeys().then((keys) => {
      setVariableKeys(keys)
    })
    getLinkSourceVariableTagKeys().then((keys) => {
      setLinkSourceVariableTagKeys(keys)
    })
    if (getYCloudTemplateList) {
      getYCloudTemplateList().then((res) => setWhatsappTemplate(res))
    }
  }, [])
  return (
    <div className="m-2">
      <SopEdit
        situations={situations}
        setSituations={setSituations}
        conditionJudgeKeys={conditionJudgeKeys}
        customKeys={customKeys}
        variableKeys={variableKeys}
        linkSourceVariableTagKeys={linkSourceVariableTagKeys}
        title={title}
        setTitle={setTitle}
        week={week}
        setWeek={setWeek}
        day={day}
        setDay={setDay}
        time={time}
        setTime={setTime}
        tag={tag}
        topic={topic}
        saveSop={saveSop}
        hint={{
          success: 'create success',
          pending: 'create pending',
          error: 'create error',
        }}
        reset={true}
        enableActionType={enableActionType}
        whatsAppTemplate={whatsappTemplate}
      />
    </div>
  )

}