'use client'
import { useState, useEffect } from 'react'
import { GroupSituation } from 'service/visualized_sop/visualized_sop_type'
import { GroupSopEdit } from './groupSopEdit'
import { GroupSop } from '@/app/type/group_sop'

export function NewGroupSop({
  tag,
  topic,
  getConditionJudgeKeys,
  getCustomKeys,
  getLinkSourceVariableTagKeys,
  getVariableMapKeys,
  saveGroupSop
}: {
  tag:string
  topic:string
  getConditionJudgeKeys(): Promise<string[]>
  getCustomKeys(): Promise<string[]>
  getVariableMapKeys(): Promise<string[]>
  getLinkSourceVariableTagKeys(): Promise<string[]>
  saveGroupSop(sop: Omit<GroupSop, 'id'>): Promise<void>
}) {
  const [situations, setSituations] = useState<GroupSituation[]>([
    {
      conditions: [],
      action: [],
    },
  ])
  const [conditionJudgeKeys, setConditionJudgeKeys] = useState<string[]>([])
  const [customKeys, setCustomKeys] = useState<string[]>([])
  const [variableKeys, setVariableKeys] = useState<string[]>([])
  const [linkSourceVariableTagKeys, setLinkSourceVariableTagKeys] = useState<
    string[]
  >([])
  const [title, setTitle] = useState<string>('')
  const [week, setWeek] = useState<number>(0)
  const [day, setDay] = useState<number>(1)
  const [time, setTime] = useState<string>('')
  useEffect(() => {
    getConditionJudgeKeys().then((keys) => {
      setConditionJudgeKeys(keys)
    })
    getCustomKeys().then((keys) => {
      setCustomKeys(keys)
    })
    getVariableMapKeys().then((keys) => {
      setVariableKeys(keys)
    })
    getLinkSourceVariableTagKeys().then((keys) => {
      setLinkSourceVariableTagKeys(keys)
    })
  }, [])
  return (
    <div className="m-2">
      <GroupSopEdit
        situations={situations}
        setSituations={setSituations}
        conditionJudgeKeys={conditionJudgeKeys}
        customKeys={customKeys}
        variableKeys={variableKeys}
        linkSourceVariableTagKeys={linkSourceVariableTagKeys}
        title={title}
        setTitle={setTitle}
        week={week}
        setWeek={setWeek}
        day={day}
        setDay={setDay}
        time={time}
        setTime={setTime}
        tag={tag}
        topic={topic}
        saveSop={saveGroupSop}
        hint={{
          success: 'create success',
          pending: 'create pending',
          error: 'create error',
        }}
        reset={true}
      />
    </div>
  )

}