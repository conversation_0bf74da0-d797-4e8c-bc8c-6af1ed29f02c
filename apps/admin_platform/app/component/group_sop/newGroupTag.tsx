'use client'
import { Group } from '@/app/type/group'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'

export function NewGroupTag({
  queryGroups,
  createGroupTag
}:{
queryGroups(): Promise<Group[]>
createGroupTag(name: string, enableGroups: string[]): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(true)
  const [groups, setGroups] = useState<Group[]>([])
  const router = useRouter()

  useEffect(() => {
    toast.promise(queryGroups, {
      pending:'query pending',
      success:'query success',
      error: 'query error'
    }).then((configs) => {
      setGroups(configs)
    }).finally(() => {
      setLoading(false)
    })
  }, [])

  return <div className="flex min-h-screen justify-center pt-30">
    <form action={(form) => {
      const groups = form.getAll('groups') as string[]
      const name = (form.get('name') ?? '') as string
      setLoading(true)
      toast.promise(createGroupTag(name, groups), {
        pending:'create pending',
        success: 'create success',
        error: 'create error'
      }).then(() => {
        router.push('.')
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">new tags</legend>

        <label className="label">name</label>
        <input type="text" className="input" name='name' required placeholder="name" disabled={loading} />

        <label className="label">accounts</label>
        {groups.map((item) => {
          return <div className='flex gap-2 items-center' key={item.id}>
            <input type='checkbox' className='checkbox' name='groups' disabled={loading} value={item.group_id}/><label>{item.name}</label>
          </div>
        })}

        <button className="btn btn-neutral mt-4 disabled:btn-disabled" disabled={loading}>create</button>
      </fieldset>
    </form>
  </div>

}