'use client'
import Link from 'next/link'
import { FaPlus } from 'react-icons/fa6'
import { GroupTagShow } from './groupTagShow'
import { GroupSopTag } from '@/app/type/group_sop_tag'
import { Group } from '@/app/type/group'
import { GroupTools } from './groupTools'

export function GroupSopTags({
  tags,
  groups,
  tagLinkPrefix,
  validateGroupSops,
  updateGroupSopMq,
  updateRedisGroupSop,
  changeGroupTagEnable,
  deleteGroupTag,
  updateGroupTagEnableGroup
}:{
  tags:GroupSopTag[]
  groups: Group[]
  tagLinkPrefix:string
  validateGroupSops(): Promise<string>
  updateGroupSopMq(): Promise<void>
  updateRedisGroupSop(): Promise<void>
  changeGroupTagEnable(tag_id: string, enable: boolean): Promise<void>,
  deleteGroupTag(id: string): Promise<void>
  updateGroupTagEnableGroup(tag_id: string, enableGroups: string[]): Promise<void>
}) {
  return (
    <div className='p-4'>
      <div className='flex justify-between mb-2'>
        <GroupTools updateMq={updateGroupSopMq} updateRedisSop={updateRedisGroupSop} validateSops={validateGroupSops}/>
        <Link href="./group_sop/new_tag"><button className='btn btn-square btn-primary'><FaPlus/></button></Link>
      </div>
      <div className="grid grid-cols-3 gap-8">
        {tags.map((item) => {
          return <GroupTagShow
            key={item.id}
            tag={item}
            groups={groups}
            tagLinkPrefix={tagLinkPrefix}
            deleteGroupTag={deleteGroupTag}
            changeGroupTagEnable={changeGroupTagEnable}
            updateGroupTagEnableGroup={updateGroupTagEnableGroup}
          />
        })}
      </div>
    </div>
  )
}