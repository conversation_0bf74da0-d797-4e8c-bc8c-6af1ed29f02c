'use client'
import { toast } from 'react-toastify'
import { useRef, useState } from 'react'

export function GroupTools({
  validateSops,
  updateMq,
  updateRedisSop
}:{
  validateSops(): Promise<string>
  updateMq(): Promise<void>
  updateRedisSop(): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [validateError, setValidateError] = useState<string>('')
  const validateModalRef = useRef<HTMLDialogElement>(null)
  return <div className='gap-2 flex'>
    <dialog ref={validateModalRef} className="modal">
      <div className="modal-box max-w-max text-wrap whitespace-pre-line">
        <h3 className="font-bold text-lg">Hello!</h3>
        <p className="py-4">
          {validateError}
        </p>
        <div className="modal-action">
          <form method="dialog">
            <button className="btn">Close</button>
          </form>
        </div>
      </div>
    </dialog>
    <button
      disabled={loading}
      className="btn btn-neutral disabled:btn-disabled"
      onClick={() => {
        setLoading(true)
        toast
          .promise(validateSops(), {
            pending: 'validate pending',
            success: 'validate success',
            error: 'validate error',
          })
          .then((error) => {
            if (error) {
              setValidateError(error)
              validateModalRef.current?.showModal()
            }
          })
          .finally(() => {
            setLoading(false)
          })
      } }
    >
        检测sop是否合法
    </button><button
      disabled={loading}
      className="btn btn-neutral disabled:btn-disabled"
      onClick={() => {
        setLoading(true)
        toast
          .promise(updateMq(), {
            pending: 'update pending',
            success: 'update success',
            error: 'update error',
          })
          .finally(() => {
            setLoading(false)
          })
      } }
    >
            update mq
    </button><button
      disabled={loading}
      className="btn btn-neutral disabled:btn-disabled"
      onClick={() => {
        setLoading(true)
        toast
          .promise(updateRedisSop(), {
            pending: 'update pending',
            success: 'update success',
            error: 'update error',
          })
          .finally(() => {
            setLoading(false)
          })
      } }
    >
            update redis
    </button>
  </div>
}