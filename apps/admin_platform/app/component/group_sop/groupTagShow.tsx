'use client'

import Link from 'next/link'
import { useRef, useState } from 'react'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'
import { IoSettingsSharp } from 'react-icons/io5'
import { GroupSopTag } from '@/app/type/group_sop_tag'
import { Group } from '@/app/type/group'

export function GroupTagShow({
  tag,
  groups,
  tagLinkPrefix,
  changeGroupTagEnable,
  deleteGroupTag,
  updateGroupTagEnableGroup
}:{
  tag:GroupSopTag
  groups: Group[]
  tagLinkPrefix:string
  changeGroupTagEnable(tag_id: string, enable: boolean): Promise<void>,
  deleteGroupTag(id: string): Promise<void>
  updateGroupTagEnableGroup(tag_id: string, enableGroups: string[]): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [enable, setEnable] = useState<boolean>(tag.enable)
  const [name, setName] = useState<string>(tag.name)
  const [enableGroup, setEnableGroup] = useState<string[]>([...tag.enable_group])
  const [willUpdateName, setWillUpdateName] = useState<string>(name)
  const [willUpdateEnableGroup, setWillUpdateEnableGroup] = useState<string[]>([...enableGroup])
  const ensureDeleteDialogRef = useRef<HTMLDialogElement>(null)
  const modifyEnableGroupsDialogRef = useRef<HTMLDialogElement>(null)
  const groupsMap = new Map<string, string>()
  const router = useRouter()
  for (const group of groups) {
    groupsMap.set(group.group_id, group.name)
  }
  return <div className='border border-base-300 p-4 shadow-sm rounded-md flex flex-col min-h-56'>
    <div className='text-lg font-semibold'>{name}</div>
    <div className='mb-2'>
      <span>enable accounts:</span>
      <button className='btn btn-circle btn-ghost btn-xs btn-neutral' onClick={() => {
        modifyEnableGroupsDialogRef.current?.showModal()
      }}>
        <IoSettingsSharp size={16}/>
      </button>
    </div>
    <div className='flex gap-1 flex-wrap'>
      {enableGroup.map((item, index) => {
        return <span key={index} className='badge badge-outline badge-info'>{groupsMap.get(item)}</span>
      })}
    </div>
    <div className='flex flex-1 items-end gap-2 justify-between'>
      <input type="checkbox" checked={enable} disabled={loading} className="toggle toggle-success" onChange={(e) => {
        setLoading(true)
        const value = e.currentTarget.checked
        toast.promise(async() => {
          await changeGroupTagEnable(tag.id, value)
        }, {
          pending:'update pending',
          success:'update success',
          error: 'update error'
        }).then(() => {
          setEnable(value)
        }).finally(() => {
          setLoading(false)
        })
      }}/>
      <div className='flex gap-2'>
        <button className='btn btn-error disabled:btn-disabled' disabled={loading} onClick={() => {
          ensureDeleteDialogRef.current?.showModal()
        }}>delete</button>
        <Link href={`${tagLinkPrefix}${tag.name}`}><button className='btn btn-info'>detail</button></Link>
      </div>
    </div>
    <dialog ref={ensureDeleteDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">Are you sure delete {tag.name} ?</h3>
        <p className="py-4">warning!!!</p>
        <div className="modal-action">
          <button className="btn btn-error disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            toast.promise(async () => {
              await deleteGroupTag(tag.id)
            }, {
              pending:'delete pending',
              success:'delete success',
              error: 'delete error'
            }).then(() => {
              router.refresh()
            }).finally(() => {
              setLoading(false)
            })
          }}>delete!!</button>
          <button className="btn" onClick={() => {
            ensureDeleteDialogRef.current?.close()
          }}>cancel</button>
        </div>
      </div>
    </dialog>
    <dialog ref={modifyEnableGroupsDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">修改启用群</h3>
        <div className='flex flex-col gap-1'>
          {groups.map((item) => {
            return <div className='flex gap-2 items-center' key={item.group_id}>
              <input type='checkbox' className='checkbox' checked={willUpdateEnableGroup.filter((group) => group == item.group_id).length > 0} disabled={loading} onChange={(e) => {
                const checked = e.currentTarget.checked
                if (checked) {
                  setWillUpdateEnableGroup([...willUpdateEnableGroup.filter((group) => group != item.group_id), item.group_id])
                } else {
                  setWillUpdateEnableGroup([...willUpdateEnableGroup.filter((group) => group != item.group_id)])
                }
              }}/><label>{item.name}</label>
            </div>
          })}
        </div>
        <div className="modal-action">
          <button className="btn disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            toast.promise(async () => {
              await updateGroupTagEnableGroup(tag.id, willUpdateEnableGroup)
            }, {
              pending:'update group pending',
              success: 'update group success',
              error: 'update group error'
            }).then(() => {
              setEnableGroup([...willUpdateEnableGroup])
              modifyEnableGroupsDialogRef.current?.close()
            }).finally(() => {
              setLoading(false)
            })
          }}>save</button>
          <button className="btn" onClick={() => {
            modifyEnableGroupsDialogRef.current?.close()
          }}>Close</button>
        </div>
      </div>
    </dialog>
  </div>
}