import Link from 'next/link'
import { FaPlus } from 'react-icons/fa6'
import { GroupSopTopics } from './groupSopTopics'
import { GroupSopTag } from '@/app/type/group_sop_tag'
import { GroupSopTopic } from '@/app/type/group_sop_topic'

export async function GroupSopTopicShow({ tag,
  querySopTopicByTag,
  changeSopTopicEnable,
  copyTopicToTag,
  renameTopic,
  queryAllTags,
  deleteTopic
}:{
  tag:string,
  querySopTopicByTag(tag:string): Promise<GroupSopTopic[]>
  changeSopTopicEnable(id:string, enable:boolean): Promise<void>
  copyTopicToTag(topicId:string, tagName:string): Promise<void>
  renameTopic(topicId:string, name:string): Promise<void>
  queryAllTags(): Promise<GroupSopTag[]>
  deleteTopic(id: string): Promise<void>
}) {
  'use server'
  const sopTopics = await querySopTopicByTag(tag)
  return <div className='p-4'>
    <div className='flex items-center justify-between'>
      <div className='text-2xl font-semibold mb-2'>{tag}</div>
      <Link href={`./${tag}/new_topic`}>
        <button className='btn btn-neutral btn-square'>
          <FaPlus/>
        </button>
      </Link>
    </div>
    <GroupSopTopics
      initialTopics={sopTopics}
      changeSopTopicEnable={changeSopTopicEnable}
      tag={tag}
      queryAllTags={queryAllTags}
      copyTopicToTag={copyTopicToTag}
      renameTopic={renameTopic}
      deleteTopic={deleteTopic}
    />
  </div>
}
