'use client'

import Link from 'next/link'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { IoSettingsSharp } from 'react-icons/io5'
import { GroupSop } from '@/app/type/group_sop'

export const testSopGroupIdKey = 'test_sop_group_id'
export function GroupSopShow({
  sops,
  setSops,
  deleteGroupSop,
  testGroupAction,
  updateGroupSop,
  selectedSop,
  setSelectedSop,
}: {
  sops: GroupSop[]
  setSops: Dispatch<SetStateAction<GroupSop[]>>
  deleteGroupSop(id: string): Promise<void>
  testGroupAction(id: string): Promise<void>
  updateGroupSop(sop_id: string, sop: Partial<GroupSop>): Promise<void>
  selectedSop: string[]
  setSelectedSop: Dispatch<SetStateAction<string[]>>
}) {
  const [loading, setLoading] = useState(false)
  const [testSopChatId, setTestSopChatId] = useState('')
  useEffect(() => {
    setTestSopChatId(localStorage.getItem(testSopGroupIdKey) ?? '')
  }, [])
  return (
    <div>
      <table className="table-sm table">
        <thead>
          <tr>
            <th><input className='checkbox' type='checkbox' checked={selectedSop.length == sops.length} onChange={(e) => {
              if (e.currentTarget.checked) {
                setSelectedSop(sops.map((item) => item.id))
              } else {
                setSelectedSop([])
              }
            }}/></th>
            <th>标题</th>
            <th>周</th>
            <th>日</th>
            <th className="text-center">时间</th>
            <th className="text-center">tag</th>
            <th className="text-center">action</th>
            <th>enable</th>
          </tr>
        </thead>
        <tbody>
          {sops.map((item, index) => {
            return (
              <tr key={item.id}>
                <th><input type='checkbox' className='checkbox' checked={selectedSop.includes(item.id)} onChange={(e) => {
                  if (e.currentTarget.checked && !selectedSop.includes(item.id)) {
                    setSelectedSop([...selectedSop, item.id])
                  } else {
                    setSelectedSop(selectedSop.filter((sop) => sop != item.id))
                  }
                }} /></th>
                <td>{item.title}</td>
                <td>{item.week}</td>
                <td>{item.day}</td>
                <td className="text-center">{item.time}</td>
                <td className="text-center">{item.tag}</td>
                <td className="flex justify-center gap-2">
                  <button
                    disabled={loading}
                    className="btn btn-neutral disabled:btn-disabled"
                    onClick={() => {
                      setLoading(true)
                      toast
                        .promise(deleteGroupSop(item.id), {
                          pending: 'delete pending',
                          error: 'delete error',
                          success: 'delete success',
                        })
                        .then(() => {
                          setSops((sops) =>
                            sops.filter((info) => info.id != item.id),
                          )
                        })
                        .finally(() => {
                          setLoading(false)
                        })
                    }}
                  >
                    delete
                  </button>
                  <Link href={`../../${item.id}`}>
                    <button className="btn btn-neutral">more</button>
                  </Link>
                  <button
                    disabled={loading}
                    className="btn btn-neutral disabled:btn-disabled"
                    onClick={() => {
                      setLoading(true)
                      toast
                        .promise(testGroupAction(item.id), {
                          pending: 'test pending',
                          success: 'test success',
                          error: {
                            render({ data }) {
                              return `${data}`
                            },
                          },
                        })
                        .finally(() => {
                          setLoading(false)
                        })
                    }}
                  >
                    test
                  </button>
                </td>
                <td>
                  <input type="checkbox" className="toggle toggle-success disabled:" checked={item.enable} disabled={loading} onChange={(e) => {
                    const willEnable = e.currentTarget.checked
                    setLoading(true)
                    toast.promise(async() => {
                      await updateGroupSop(item.id, {
                        enable:willEnable
                      })
                    }, {
                      pending:'enable change pending',
                      error:'enable change error',
                      success:'enable change success'
                    }).then(() => {
                      const newSops = [...sops]
                      for (const sop of newSops) {
                        if (sop.id == item.id) {
                          sop.enable = willEnable
                        }
                      }
                      setSops(newSops)
                    }).finally(() => {
                      setLoading(false)
                    })
                  }}/>
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
      <div className="dropdown dropdown-left dropdown-end fixed right-10 bottom-10">
        <div tabIndex={0} role="button" className="btn m-1 btn-circle btn-neutral"><IoSettingsSharp size={26}/></div>
        <div tabIndex={0} className="dropdown-content bg-base-100 rounded-box z-1 p-8 shadow-md">
          <form onSubmit={(e) => {
            e.preventDefault()
            const form = new FormData(e.currentTarget)
            const id = form.get('id') as string
            window.localStorage.setItem(testSopGroupIdKey, id)
            toast.success('save success')
          }} className='flex items-center gap-2'>
            <label className='label'>测试group_id</label>
            <input type="text" name='id' className='input focus-within:outline-0 w-96' defaultValue={testSopChatId}/>
            <input type="submit" value={'save'} className='btn btn-info btn-soft'/>
          </form>
        </div>
      </div>
    </div>
  )
}
