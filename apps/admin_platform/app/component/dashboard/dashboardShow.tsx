'use client'
import { DashboardDataWithChatHistory } from '@/app/type/dashboard_data'
import { DashboardTag } from '@/app/type/dashboard_tag'
import dayjs from 'dayjs'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { RxCross2 } from 'react-icons/rx'
import { FaPlus, FaCheck, FaTimes } from 'react-icons/fa'
import { HiPencilAlt, HiOutlinePencilAlt } from 'react-icons/hi'
import { toast } from 'react-toastify'
import { TagSelector } from './tagSelector'

export function DashboardShow({
  initialDashboard,
  availableTags,
  deleteDashboardData,
  deleteDashboardTag,
  addDashboardTag,
  updateDashboardDescription
}:{
  initialDashboard:DashboardDataWithChatHistory[]
  availableTags:DashboardTag[]
  deleteDashboardData(id:string): Promise<void>
  deleteDashboardTag(id:string, tagId:string): Promise<void>
  addDashboardTag(id:string, tagName:string): Promise<{ success: boolean, tag?: any }>
  updateDashboardDescription(id:string, description:string): Promise<void>
}) {
  const [dashboard, setDashboard] = useState<DashboardDataWithChatHistory[]>(initialDashboard)
  useEffect(() => {
    setDashboard(initialDashboard)
  }, [initialDashboard])
  return <div>
    <div className='flex flex-col gap-4'>
      {dashboard.map((item) => <DashboardDetail
        key={item.id}
        dashboard={item}
        availableTags={availableTags}
        deleteDashboardData={() => deleteDashboardData(item.id)}
        deleteDashboardTag={(tagId) => {
          toast.promise(deleteDashboardTag(item.id, tagId), {
            pending: 'delete pending',
            success: 'delete success',
            error: 'delete error'
          }).then(() => {
            setDashboard((data) => {
              return data.map((dataItem) => {
                if (item.id == dataItem.id) {
                  return {
                    ...dataItem,
                    tag_ids: dataItem.tag_ids.filter((dataItemTagId) => dataItemTagId != tagId),
                    tags: dataItem.tags.filter((tag) => tag.id != tagId)
                  }
                } else {
                  return dataItem
                }
              })
            })
          })
        }}
        addDashboardTag={async (tagName) => {
          try {
            const result = await toast.promise(addDashboardTag(item.id, tagName), {
              pending: 'add tag pending',
              success: 'add tag success',
              error: 'add tag error'
            })

            // 只更新当前项目的标签，不刷新整个页面
            if (result.success && result.tag) {
              setDashboard((data) => {
                return data.map((dataItem) => {
                  if (item.id === dataItem.id) {
                    // 检查标签是否已存在，避免重复添加
                    const tagExists = dataItem.tags.some((tag) => tag.id === result.tag.id)
                    if (!tagExists) {
                      return {
                        ...dataItem,
                        tag_ids: [...dataItem.tag_ids, result.tag.id],
                        tags: [...dataItem.tags, result.tag]
                      }
                    }
                    return dataItem
                  } else {
                    return dataItem
                  }
                })
              })
            }
          } catch (error) {
            console.error('添加标签失败:', error)
          }
        }}
        updateDashboardDescription={(description) => {
          toast.promise(updateDashboardDescription(item.id, description), {
            pending: 'update description pending',
            success: 'update description success',
            error: 'update description error'
          }).then(() => {
            setDashboard((data) => {
              return data.map((dataItem) => {
                if (item.id == dataItem.id) {
                  return {
                    ...dataItem,
                    description: description
                  }
                } else {
                  return dataItem
                }
              })
            })
          })
        }}
      />)}
    </div>
  </div>
}

function DashboardDetail({
  dashboard,
  availableTags,
  deleteDashboardData,
  deleteDashboardTag,
  addDashboardTag,
  updateDashboardDescription
}:{
  dashboard:DashboardDataWithChatHistory
  availableTags:DashboardTag[]
  deleteDashboardData(): Promise<void>
  deleteDashboardTag(tagId:string): void
  addDashboardTag(tagName:string): Promise<void>
  updateDashboardDescription(description:string): void
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [editingDescription, setEditingDescription] = useState<boolean>(false)
  const [newDescription, setNewDescription] = useState<string>(dashboard.description)
  const [addingTag, setAddingTag] = useState<boolean>(false)
  const [newTag, setNewTag] = useState<string>('')
  const router = useRouter()
  return <div className='p-4 border shadow-md rounded-md border-gray-300'>
    <div className='flex justify-between'>
      <div className='grow-0 basis-8/12'>
        <h2 className='text-xl flex gap-1 items-center flex-wrap'>
          <span>Tag:</span>
          {dashboard.tags && dashboard.tags.length > 0 ? dashboard.tags.map((tag) =>
            <div
              key={tag.id}
              className="flex items-center gap-1 px-2 py-1 rounded-full border border-gray-300"
              style={{ backgroundColor: tag.color || '#E3F2FD' }}
            >
              <div
                className="w-2 h-2 rounded-full border border-gray-400"
                style={{ backgroundColor: tag.color || '#E3F2FD' }}
              ></div>
              <span className="text-gray-700 text-sm">{tag.name}</span>
              <button
                onClick={() => { deleteDashboardTag(tag.id) }}
                className='btn btn-ghost btn-circle h-4 w-4 ml-1 hover:bg-red-100'
              >
                <RxCross2 className="text-gray-600 hover:text-red-600" />
              </button>
            </div>
          ) : <span className="text-gray-500 text-sm">无标签</span>}
          {addingTag ? (
            <div className="relative">
              <TagSelector
                availableTags={availableTags}
                onTagSelect={(tagName) => {
                  addDashboardTag(tagName)
                  setAddingTag(false)
                }}
                onCancel={() => setAddingTag(false)}
              />
            </div>
          ) : (
            <button
              onClick={() => setAddingTag(true)}
              className="btn btn-xs btn-ghost hover:bg-green-50 hover:text-green-600 transition-colors duration-200"
              title="添加标签"
            >
              <FaPlus className="w-3 h-3" />
            </button>
          )}
        </h2>
        <div className='flex items-center gap-2 mb-2'>
          {editingDescription ? (
            <div className="flex gap-2 items-start flex-1">
              <textarea
                value={newDescription}
                onChange={(e) => setNewDescription(e.target.value)}
                className="textarea textarea-bordered flex-1 resize-none text-sm"
                placeholder="输入描述..."
                rows={newDescription.split('\n').length || 1}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    updateDashboardDescription(newDescription)
                    setEditingDescription(false)
                  } else if (e.key === 'Escape') {
                    setNewDescription(dashboard.description)
                    setEditingDescription(false)
                  }
                }}
                autoFocus
              />
              <div className="flex flex-col gap-1">
                <button
                  onClick={() => {
                    updateDashboardDescription(newDescription)
                    setEditingDescription(false)
                  }}
                  className="btn btn-sm btn-success hover:btn-success-focus"
                  title="保存 (Enter)"
                >
                  <FaCheck className="w-4 h-4" />
                </button>
                <button
                  onClick={() => {
                    setNewDescription(dashboard.description)
                    setEditingDescription(false)
                  }}
                  className="btn btn-sm btn-error hover:btn-error-focus"
                  title="取消 (Esc)"
                >
                  <FaTimes className="w-4 h-4" />
                </button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2 flex-1">
              <p className='text-sm text-gray-800 flex-1 whitespace-pre-line'>{dashboard.description}</p>
              <button
                onClick={() => setEditingDescription(true)}
                className="btn btn-xs btn-ghost hover:bg-blue-50 hover:text-blue-600"
                title="编辑描述"
              >
                <HiOutlinePencilAlt className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
        <span className='text-sm text-gray-400'>{dayjs(dashboard.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
        {dashboard.chat_history.map((item) => {
          return <div key={item.id} className='text-sm whitespace-pre-wrap'>{`[${dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}] ${item.role}:${item.content}`}</div>
        })}
        {dashboard.chat_history.length === 7 && (
          <div className='text-xs text-gray-400 italic mt-2'>
            * 仅显示7条聊天记录, 点击 link 查看详情
          </div>
        )}
      </div>
      <div className='flex gap-2'>
        <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={(e) => {
          setLoading(true)
          toast.promise(deleteDashboardData(), {
            pending:'delete pending',
            success:'delete success',
            error:'delete error'
          }).then(() => {
            router.refresh()
          }).finally(() => {
            setLoading(false)
          })
        }}>delete</button>
        <Link href={`/yuhe/user/chat/${dashboard.chat_id}#${dashboard?.chat_history[0]?.id ?? ''}`} target="_blank" rel="noopener noreferrer"><button className='btn btn-neutral'>link</button></Link>
      </div>
    </div>
  </div>
}