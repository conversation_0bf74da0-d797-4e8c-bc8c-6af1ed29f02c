'use client'

import { DashboardDataWithChatHistory } from '@/app/type/dashboard_data'
import { DashboardTag } from '@/app/type/dashboard_tag'
import { DashboardShow } from './dashboardShow'
import { TagFilter } from './tagFilter'
import { useRouter, useSearchParams } from 'next/navigation'

interface DashboardWithFilterProps {
  page: number
  pageSize: number
  selectedTagIds: string[]
  dashboardData: DashboardDataWithChatHistory[]
  count: number
  allTags: DashboardTag[]
  deleteDashboardData(id:string): Promise<void>
  deleteDashboardTag(id:string, tagId:string): Promise<void>
  addDashboardTag(id:string, tagName:string): Promise<{ success: boolean, tag?: any }>
  updateDashboardDescription(id:string, description:string): Promise<void>
}

export function DashboardWithFilter({
  page,
  pageSize,
  selectedTagIds,
  dashboardData,
  count,
  allTags,
  deleteDashboardData,
  deleteDashboardTag,
  addDashboardTag,
  updateDashboardDescription
}: DashboardWithFilterProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleTagToggle = (tagId: string) => {
    const newParams = new URLSearchParams(searchParams.toString())
    const currentTags = selectedTagIds

    if (currentTags.includes(tagId)) {
      // 移除标签
      const newTags = currentTags.filter((id) => id !== tagId)
      if (newTags.length > 0) {
        newParams.set('tags', newTags.join(','))
      } else {
        newParams.delete('tags')
      }
    } else {
      // 添加标签
      const newTags = [...currentTags, tagId]
      newParams.set('tags', newTags.join(','))
    }

    // 重置到第一页
    newParams.set('page', '1')

    router.push(`?${newParams.toString()}`)
  }

  const handleClearAllTags = () => {
    const newParams = new URLSearchParams(searchParams.toString())
    newParams.delete('tags')
    newParams.set('page', '1')
    router.push(`?${newParams.toString()}`)
  }

  const buildPageUrl = (targetPage: number) => {
    const newParams = new URLSearchParams(searchParams.toString())
    newParams.set('page', targetPage.toString())
    return `?${newParams.toString()}`
  }

  return (
    <div className='p-4'>
      <TagFilter
        allTags={allTags}
        selectedTagIds={selectedTagIds}
        onTagToggle={handleTagToggle}
        onClearAll={handleClearAllTags}
      />

      <div className='text-sm text-base-content text-right mb-2'>
        count: {count}
        {selectedTagIds.length > 0 && (
          <span className="ml-2 text-blue-600">
            (已筛选 {selectedTagIds.length} 个标签)
          </span>
        )}
      </div>

      <DashboardShow
        initialDashboard={dashboardData}
        availableTags={allTags}
        deleteDashboardData={deleteDashboardData}
        deleteDashboardTag={deleteDashboardTag}
        addDashboardTag={addDashboardTag}
        updateDashboardDescription={updateDashboardDescription}
      />

      <div className='flex justify-center mt-12'>
        <div className="join">
          <a
            href={buildPageUrl(page - 1)}
            className={`join-item btn ${page <= 1 ? 'btn-disabled' : ''}`}
          >
            «
          </a>
          <button className="join-item btn">Page {page}</button>
          <a
            href={buildPageUrl(page + 1)}
            className={`join-item btn ${page >= Math.ceil(count / pageSize) ? 'btn-disabled' : ''}`}
          >
            »
          </a>
        </div>
      </div>
    </div>
  )
}
