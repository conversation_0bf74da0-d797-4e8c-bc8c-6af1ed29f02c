'use server'

import { DashboardDataWithChatHistory } from '@/app/type/dashboard_data'
import { DashboardTag } from '@/app/type/dashboard_tag'
import { DashboardWithFilter } from './dashboardWithFilter'

export async function Dashboard({
  page,
  pageSize,
  selectedTagIds,
  queryDashboardData,
  queryDashboardDataCount,
  queryAllTags,
  deleteDashboardData,
  deleteDashboardTag,
  addDashboardTag,
  updateDashboardDescription
}:{
  page: number,
  pageSize:number,
  selectedTagIds?: string[],
  queryDashboardData(page:number, pageSize:number, tagIds?: string[]):Promise<DashboardDataWithChatHistory[]>
  queryDashboardDataCount(tagIds?: string[]): Promise<number>
  queryAllTags(): Promise<DashboardTag[]>
  deleteDashboardData(id:string): Promise<void>
  deleteDashboardTag(id:string, tagId:string): Promise<void>
  addDashboardTag(id:string, tagName:string): Promise<{ success: boolean, tag?: any }>
  updateDashboardDescription(id:string, description:string): Promise<void>
}) {
  const dashboardData = await queryDashboardData(page, pageSize, selectedTagIds)
  const count = await queryDashboardDataCount(selectedTagIds)
  const allTags = await queryAllTags()

  return <DashboardWithFilter
    page={page}
    pageSize={pageSize}
    selectedTagIds={selectedTagIds || []}
    dashboardData={dashboardData}
    count={count}
    allTags={allTags}
    deleteDashboardData={deleteDashboardData}
    deleteDashboardTag={deleteDashboardTag}
    addDashboardTag={addDashboardTag}
    updateDashboardDescription={updateDashboardDescription}
  />
}
