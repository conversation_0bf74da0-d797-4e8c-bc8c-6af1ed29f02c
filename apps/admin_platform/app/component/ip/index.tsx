import { AccountData } from '@/app/type/account'
import { InsertIp } from './insert'

export async function Ip({
  queryAccounts,
  insertIpTable
}:{
  queryAccounts():Promise<AccountData[]>
  insertIpTable(account:string, startCourseNo:number, endCourseNo:number, ip:string): Promise<void>
}) {
  const accounts = await queryAccounts()
  return <div>
    <InsertIp accounts={accounts} insertIpTable={insertIpTable}/>
  </div>
}