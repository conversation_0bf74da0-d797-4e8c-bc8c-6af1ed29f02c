'use client'
import { AccountData } from '@/app/type/account'
import { useState } from 'react'
import { toast } from 'react-toastify'

export function InsertIp({
  accounts,
  insertIpTable
}:{
  accounts:AccountData[]
  insertIpTable(account:string, startCourseNo:number, endCourseNo:number, ip:string): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  return <div>
    <form action={(form) => {
      const account = form.get('account') as string
      const startCourseNo = Number(form.get('start_course_no') as string)
      const endCourseNo = Number(form.get('end_course_no') as string)
      const ip = form.get('ip') as string
      setLoading(true)
      toast.promise(insertIpTable(account, startCourseNo, endCourseNo, ip), {
        pending:'insert pending',
        success:'insert success',
        error: 'insert error'
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">insert details</legend>

        <label className="label">账号</label>
        <select name="account" className='select focus-within:outline-0'>
          {accounts.map((item) => {
            return <option key={item.id} value={item.wechatId}>{item.accountName}</option>
          })}
        </select>

        <label className="label">start course no</label>
        <input type="number" name="start_course_no" className='input focus-within:outline-0'/>

        <label className="label">end course no</label>
        <input type="number" className="input focus-within:outline-0" name="end_course_no" />

        <label className="label">ip</label>
        <input type="text" className="input focus-within:outline-0" name="ip" />

        <button className='btn btn-neutral disabled:btn-disabled' disabled={loading}>submit</button>
      </fieldset>
    </form>
  </div>
}