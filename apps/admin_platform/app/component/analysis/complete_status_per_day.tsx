'use client'
import { AnalysisData } from '@/app/type/analysis'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { useEffect, useRef } from 'react'

export function CompleteStatusPerDay({ data }:{data:AnalysisData[]}) {
  const ref = useRef<HTMLDivElement>(null)
  const initialized = useRef<boolean>(false)
  useEffect(() => {
    if (ref.current == null || initialized.current) {
      return
    }
    const myChart = echarts.init(ref.current)

    let minDate = Infinity
    let maxDate = -Infinity
    for (const item of data) {
      if (item.courseNoOri < minDate) {
        minDate = item.courseNoOri
      }
      if (item.courseNoOri > maxDate) {
        maxDate = item.courseNoOri
      }
    }
    const dates:number[] = []

    for (let date = minDate; date <= maxDate; date = Number(dayjs(String(date), 'YYYYMMDD').add(1, 'day').format('YYYYMMDD'))) {
      dates.push(date)
      if (date == maxDate) {
        dates.push(Number(dayjs(String(date), 'YYYYMMDD').add(1, 'day').format('YYYYMMDD')))
        dates.push(Number(dayjs(String(date), 'YYYYMMDD').add(2, 'day').format('YYYYMMDD')))
        dates.push(Number(dayjs(String(date), 'YYYYMMDD').add(3, 'day').format('YYYYMMDD')))
        dates.push(Number(dayjs(String(date), 'YYYYMMDD').add(4, 'day').format('YYYYMMDD')))
      }
    }

    const status:Record<number, Status> = {}

    for (const date of dates) {
      status[date] = {
        day1Number:0,
        attendCourseDay1Number:0,
        completeCourseDay1Number:0,
        day2Number:0,
        attendCourseDay2Number:0,
        completeCourseDay2Number:0,
        day3Number:0,
        attendCourseDay3Number:0,
        completeCourseDay3Number:0,
        day4Number:0,
        attendCourseDay4Number:0,
        completeCourseDay4Number:0,
      }
    }

    for (const item of data) {
      const day1 = Number(dayjs(String(item.courseNoOri), 'YYYYMMDD').add(1, 'day').format('YYYYMMDD'))
      const day2 = Number(dayjs(String(item.courseNoOri), 'YYYYMMDD').add(2, 'day').format('YYYYMMDD'))
      const day3 = Number(dayjs(String(item.courseNoOri), 'YYYYMMDD').add(3, 'day').format('YYYYMMDD'))
      const day4 = Number(dayjs(String(item.courseNoOri), 'YYYYMMDD').add(4, 'day').format('YYYYMMDD'))

      status[day1].day1Number += 1
      status[day2].day2Number += 1
      status[day3].day3Number += 1
      status[day4].day4Number += 1

      if (item.isAttendCourseDay1) {
        status[day1].attendCourseDay1Number += 1
      }
      if (item.isAttendCourseDay2) {
        status[day2].attendCourseDay2Number += 1
      }
      if (item.isAttendCourseDay3) {
        status[day3].attendCourseDay3Number += 1
      }
      if (item.isAttendCourseDay4) {
        status[day4].attendCourseDay4Number += 1
      }

      if (item.isCompleteCourseDay1) {
        status[day1].completeCourseDay1Number += 1
      }
      if (item.isCompleteCourseDay2) {
        status[day2].completeCourseDay2Number += 1
      }
      if (item.isCompleteCourseDay3) {
        status[day3].completeCourseDay3Number += 1
      }
      if (item.isCompleteCourseDay4) {
        status[day4].completeCourseDay4Number += 1
      }
    }

    const info = Object.entries(status).sort((a, b) => Number(a[0]) - Number(b[0]))

    const option = {
      xAxis: {
        type: 'category',
        data: info.map((item) => item[0])
      },
      tooltip: {
        trigger: 'axis'
      },
      legend:{
        data: ['Day1 到课率', 'Day2 到课率', 'Day3 到课率', 'Day4 到课率', 'Day1 完课率', 'Day2 完课率', 'Day3 完课率', 'Day4 完课率',]
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name:'Day1 到课率',
          data: info.map((item) => Number(item[1].attendCourseDay1Number / item[1].day1Number || 0).toFixed(2)),
          type: 'line'
        },
        {
          name:'Day2 到课率',
          data: info.map((item) => Number(item[1].attendCourseDay2Number / item[1].day2Number || 0).toFixed(2)),
          type: 'line'
        },
        {
          name:'Day3 到课率',
          data: info.map((item) => Number(item[1].attendCourseDay3Number / item[1].day3Number || 0).toFixed(2)),
          type: 'line'
        },
        {
          name:'Day4 到课率',
          data: info.map((item) => Number(item[1].attendCourseDay4Number / item[1].day4Number || 0).toFixed(2)),
          type: 'line'
        },
        {
          name:'Day1 完课率',
          data: info.map((item) => Number(item[1].completeCourseDay1Number / item[1].day1Number || 0).toFixed(2)),
          type: 'line'
        },
        {
          name:'Day2 完课率',
          data: info.map((item) => Number(item[1].completeCourseDay2Number / item[1].day2Number || 0).toFixed(2)),
          type: 'line'
        },
        {
          name:'Day3 完课率',
          data: info.map((item) => Number(item[1].completeCourseDay3Number / item[1].day3Number || 0).toFixed(2)),
          type: 'line'
        },
        {
          name:'Day4 完课率',
          data: info.map((item) => Number(item[1].completeCourseDay4Number / item[1].day4Number || 0).toFixed(2)),
          type: 'line'
        },
      ]
    }
    myChart.setOption(option)
    const ro = new ResizeObserver(() => {
      myChart.resize()
    })
    ro.observe(ref.current)
    initialized.current = true
  }, [])

  return <div>
    <div ref={ref} className='h-[30rem] w-full'></div>

  </div>
}

type Status = {
  day1Number:number
  attendCourseDay1Number:number
  completeCourseDay1Number:number
  day2Number:number
  attendCourseDay2Number:number
  completeCourseDay2Number:number
  day3Number:number
  attendCourseDay3Number:number
  completeCourseDay3Number:number
  day4Number:number
  attendCourseDay4Number:number
  completeCourseDay4Number:number
}