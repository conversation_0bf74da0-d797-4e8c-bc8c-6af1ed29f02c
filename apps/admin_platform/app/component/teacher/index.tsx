'use client'
import { AccountData, AccountTeacherData, GroupTeacher } from '@/app/type/account'
import { useRouter } from 'next/navigation'
import { useRef, useState } from 'react'
import { toast } from 'react-toastify'

export function Teacher({
  accounts,
  groupTeachers,
  getTeacherByName,
  updateGroupTeacher,
  inviteGroup
}:{
  accounts:AccountData[],
  groupTeachers:GroupTeacher[],
  getTeacherByName(imBotId: string, userName: string):Promise<AccountTeacherData[]>
  updateGroupTeacher(accountWechatId: string, userData: AccountTeacherData):Promise<void>
  inviteGroup(accountId:string): Promise<void>
}) {
  return <div className='m-2'>
    <h3 className='text-3xl'>account info</h3>
    <div className='flex flex-col gap-4'>
      {accounts.map((item) => <Account key={item.id} account={item} groupTeacher={groupTeachers.find((teacher) => teacher.accountWechatId == item.wechatId)} getTeacherByName={getTeacherByName} updateGroupTeacher={updateGroupTeacher} inviteGroup={inviteGroup}/>)}
    </div>
  </div>
}

function Account({
  account,
  groupTeacher,
  getTeacherByName,
  updateGroupTeacher,
  inviteGroup
}:{
  account:AccountData,
  groupTeacher?: GroupTeacher
  getTeacherByName(imBotId: string, userName: string):Promise<AccountTeacherData[]>
  updateGroupTeacher(accountWechatId: string, userData: AccountTeacherData):Promise<void>
  inviteGroup(accountId:string): Promise<void>
}) {
  return <div className='m-4 p-4 border border-base-300 rounded-md shadow flex justify-between items-center'>
    <div className='flex gap-2'>
      <div className='text-lg'>{account.accountName}</div>
      <div className=''>teacher: {groupTeacher?.name ?? '未设置'}</div>
    </div>
    <ChangeTeacherButtonAndDialog account={account} getTeacherByName={getTeacherByName} updateGroupTeacher={updateGroupTeacher} inviteGroup={inviteGroup}/>
  </div>
}

function ChangeTeacherButtonAndDialog({
  account,
  getTeacherByName,
  updateGroupTeacher,
  inviteGroup
}:{
  account: AccountData,
  getTeacherByName(imBotId: string, userName: string):Promise<AccountTeacherData[]>
  updateGroupTeacher(accountWechatId: string, userData: AccountTeacherData):Promise<void>
  inviteGroup(accountId:string): Promise<void>
}) {
  const dialogRef = useRef<HTMLDialogElement>(null)
  const router = useRouter()
  const [loading, setLoading] = useState<boolean>(false)
  const [searchTeacherStr, setSearchTeacherStr] = useState<string>('')
  const [selectedTeacher, setSelectedTeacher] = useState<string>('')
  const [candidateTeachers, setCandidateTeachers] = useState<AccountTeacherData[]>([])
  return <>
    <dialog ref={dialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">change teacher?</h3>
        <fieldset className="fieldset">
          <legend className="fieldset-legend">What is new teacher?</legend>
          <div className='flex gap-2'>
            <input type="text" className='input focus-within:outline-0' value={searchTeacherStr} onChange={(e) => { setSearchTeacherStr(e.currentTarget.value) }} />
            <button className='btn btn-soft btn-success disabled:btn-disabled' disabled={loading} onClick={() => {
              setLoading(true)
              toast.promise(getTeacherByName(account.wechatId, searchTeacherStr), {
                pending:'search pending',
                success:'search success',
                error:'search error'
              }).then((res) => {
                setCandidateTeachers(res)
                if (res.length > 0) {
                  setSelectedTeacher(res[0].imContactId)
                }
              }).finally(() => {
                setLoading(false)
              })
            }}>search</button>
          </div>
          <select className='select focus-within:outline-0' value={selectedTeacher} onChange={(e) => { setSelectedTeacher(e.currentTarget.value) }}>
            {candidateTeachers.map((teacher) => {
              return <option key={teacher.imContactId} value={teacher.imContactId}>{teacher.name}</option>
            })}
          </select>
        </fieldset>
        <div className="modal-action">
          <button className="btn btn-error btn-soft disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            const selectedTeacherInfo = candidateTeachers.find((teacher) => teacher.imContactId == selectedTeacher)
            if (!selectedTeacherInfo) {
              toast.error('没有选择的老师')
              setLoading(false)
              return
            }
            toast.promise(updateGroupTeacher(account.wechatId, selectedTeacherInfo), {
              pending:'update pending',
              success:'update success',
              error: 'update error'
            }).then(() => {
              router.refresh()
              window.location.reload()
            }).finally(() => {
              setLoading(false)
            })
          }}>change</button>
          <button className="btn" onClick={() => {
            dialogRef.current?.close()
          }}>cancel</button>
        </div>
      </div>
    </dialog>
    <div className='flex gap-2'>
      <button className='btn btn-warning btn-soft' onClick={() => {
        setLoading(true)
        toast.promise(inviteGroup(account.id), {
          pending:'invite pending',
          success:'invite success',
          error: 'invite error'
        }).finally(() => {
          setLoading(false)
        })
      }}>invite group</button>
      <button className='btn btn-soft btn-accent' onClick={() => {
        dialogRef.current?.showModal()
        setSearchTeacherStr('')
      }}>change teacher</button>

    </div>
  </>
}