import { Group } from '@/app/type/group'
import Link from 'next/link'

export async function GroupList({
  queryAllGroup
}:{
  queryAllGroup():Promise<Group[]>
}) {
  const groups = await queryAllGroup()
  return <div className='p-4'>
    <h2 className='text-2xl font-semibold mb-2'>group</h2>
    <div className='flex justify-start gap-6 flex-wrap'>
      {groups.map((group) => {
        return <div key={group.id} className='border max-w-[30rem] p-2 min-w-[30rem] min-h-32 rounded-lg '>
          <div className='flex justify-between'>
            <div className='text-lg'>{group.name}</div>
            <Link href={`./group/${group.group_id}/sop`}><button className='btn btn-neutral'>sop</button></Link>
          </div>
        </div>
      })}
    </div>
  </div>
}