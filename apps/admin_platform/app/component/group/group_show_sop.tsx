import dayjs from 'dayjs'
import { IGroupTask } from 'service/visualized_sop/visualized_sop_type'
import Link from 'next/link'

export async function GroupShowSop({
  groupId,
  listGroupSopTask
}:{
  groupId:string
  listGroupSopTask(groupId:string): Promise<IGroupTask[]>
}) {
  const tasks = (await listGroupSopTask(groupId)).filter((item) => item.groupId == groupId).sort((a, b) => {
    if (a.scheduleTime.week != b.scheduleTime.week) {
      return a.scheduleTime.week < b.scheduleTime.week ? -1 : 1
    } else if (a.scheduleTime.day < b.scheduleTime.day) {
      return a.scheduleTime.day < b.scheduleTime.day ? -1 : 1
    } else {
      return a.scheduleTime.time < b.scheduleTime.time ? -1 : 1
    }
  })
  return <div>
    <div className="overflow-x-auto">
      <table className="table">
        {/* head */}
        <thead>
          <tr>
            <th></th>
            <th>name</th>
            <th>group_id</th>
            <th>week</th>
            <th>day</th>
            <th>time</th>
            <th>sendTime</th>
            <th>action</th>
          </tr>
        </thead>
        <tbody>
          {tasks.map((task, index) => {
            return <tr key={index}>
              <th>{index + 1}</th>
              <td>{task.name}</td>
              <td>{task.groupId}</td>
              <td>{task.scheduleTime.week}</td>
              <td>{task.scheduleTime.day}</td>
              <td>{task.scheduleTime.time}</td>
              <td>{task.sendTime ? dayjs(task.sendTime).format('YYYY-MM-DD HH:mm:ss') : ''}</td>
              <td><Link href={`../../group_sop/${task.name}`} className='btn btn-info btn-soft'>detail</Link></td>
            </tr>
          })}
        </tbody>
      </table>
    </div>
  </div>
}