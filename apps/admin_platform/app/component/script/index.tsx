'use client'

import { download } from '@/app/lib/download'
import dayjs from 'dayjs'
import { useState } from 'react'
import { toast } from 'react-toastify'

export function Script({
  setUserWithoutPhoneStageIntoPhoneQuery,
  exportUserSlots,
  addBoardcastWhiteListByCourseNoIntoCourseNo
}:{
  setUserWithoutPhoneStageIntoPhoneQuery(courseNo: number): Promise<void>
  exportUserSlots(startCourseNo: number, endCourseNo: number): Promise<string>
  addBoardcastWhiteListByCourseNoIntoCourseNo(userCourseNo:number, destinationCourseNo:number):Promise<void>
}) {
  return <div className='flex flex-col gap-2'>
    <SetUserWithoutPhoneIntoPhoneQueryStage setUserWithoutPhoneStageIntoPhoneQuery={setUserWithoutPhoneStageIntoPhoneQuery}/>
    <ExportUserSlots exportUserSlots={exportUserSlots}/>
    <ImportBoardcastWhiteListByCourseNoIntoCourseNo addBoardcastWhiteListByCourseNoIntoCourseNo={addBoardcastWhiteListByCourseNoIntoCourseNo}/>
  </div>
}

function SetUserWithoutPhoneIntoPhoneQueryStage({ setUserWithoutPhoneStageIntoPhoneQuery }:{
  setUserWithoutPhoneStageIntoPhoneQuery(courseNo: number): Promise<void>
}) {
  const [courseNo, setCourseNo] = useState<number>(2025)
  return <div>
    <h2 className='font-semibold'>set user without phone into phone query stage, course_no:</h2>
    <div>
      <input className='input focus-within:outline-0' type='number' value={courseNo} onChange={(e) => { setCourseNo(e.currentTarget.valueAsNumber) }}/>
      <button className='btn btn-neutral' onClick={() => {
        toast.promise(setUserWithoutPhoneStageIntoPhoneQuery(courseNo), {
          pending: 'update pending',
          success: 'update success',
          error: 'update error',
        })
      }}>update</button>
    </div>
  </div>
}

function ExportUserSlots({ exportUserSlots }:{
  exportUserSlots(startCourseNo: number, endCourseNo: number): Promise<string>
}) {
  const [startCourseNo, setStartCourseNo] = useState<number>(Number(dayjs().subtract(7, 'day').format('YYYYMMDD')))
  const [endCourseNo, setEndCourseNo] = useState<number>(Number(dayjs().format('YYYYMMDD')))
  return <div className='flex flex-col gap-2'>
    <h2 className='font-semibold'>提取客户画像</h2>
    <div className='flex gap-2 items-center'>
      <span className='w-30 inline-block'>start course no:</span>
      <input className='input focus-within:outline-0' type='number' value={startCourseNo} onChange={(e) => { setStartCourseNo(e.currentTarget.valueAsNumber) }}/>
    </div>
    <div className='flex gap-2 items-center'>
      <span className='w-30 inline-block'>end course no:</span>
      <input className='input focus-within:outline-0' type='number' value={endCourseNo} onChange={(e) => { setEndCourseNo(e.currentTarget.valueAsNumber) }}/>
      <button className='btn btn-neutral' onClick={() => {
        toast.promise(async() => {
          const data = await exportUserSlots(startCourseNo, endCourseNo)
          download(data, 'user_slots.csv')
        }, {
          pending: 'export pending',
          success: 'export success',
          error: 'export error',
        })
      }}>export</button>
    </div>
  </div>
}

function ImportBoardcastWhiteListByCourseNoIntoCourseNo({
  addBoardcastWhiteListByCourseNoIntoCourseNo
}:{
  addBoardcastWhiteListByCourseNoIntoCourseNo(userCourseNo:number, destinationCourseNo:number):Promise<void>
}) {
  const [userCourseNo, setUserCourseNo] = useState<number>(Number(dayjs().format('YYYYMMDD')))
  const [destinationCourseNo, setDestinationCourseNo] = useState<number>(Number(dayjs().format('YYYYMMDD')))
  return <div>
    <h2 className='font-semibold'>添加白名单</h2>
    <div className='flex gap-2 items-center'>
      <span className='w-30 inline-block'>user course no:</span>
      <input className='input focus-within:outline-0' type='number' value={userCourseNo} onChange={(e) => { setUserCourseNo(e.currentTarget.valueAsNumber) }}/>
    </div>
    <div className='flex gap-2 items-center'>
      <span className='w-30 inline-block'>destination course no:</span>
      <input className='input focus-within:outline-0' type='number' value={destinationCourseNo} onChange={(e) => { setDestinationCourseNo(e.currentTarget.valueAsNumber) }}/>
      <button className='btn btn-neutral' onClick={() => {
        toast.promise(async() => {
          await addBoardcastWhiteListByCourseNoIntoCourseNo(userCourseNo, destinationCourseNo)
        }, {
          pending: 'export pending',
          success: 'export success',
          error: 'export error',
        })
      }}>export</button>
    </div>
  </div>
}