'use client'

import { useState } from 'react'
import { toast } from 'react-toastify'
import { FilePond } from 'react-filepond'
import { ActualFileObject, registerPlugin } from 'filepond'
import FilePondPluginImagePreview from 'filepond-plugin-image-preview'

import 'filepond/dist/filepond.min.css'
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css'

registerPlugin(FilePondPluginImagePreview)

export default function AddRagSale({
  add,
}: {
  add: (param: {
    industry: string;
    description: string;
    caseClass: string;
    formData: FormData;
  }) => Promise<void>;
}) {
  const [files, setFiles] = useState<ActualFileObject[]>([])
  return (
    <div>
      <form
        action={async (form: FormData) => {
          for (const file of files) {
            form.append('file', file)
          }
          const industry = form.get('industry') as string
          const description = form.get('description') as string
          const caseClass = form.get('classification') as string
          await toast
            .promise(add({
              formData: form,
              industry: industry,
              description: description,
              caseClass: caseClass
            }), {
              pending: 'submit is pending',
              success: 'submit resolved 👌',
              error: 'submit rejected 🤯',
            })
            .then(() => {
              setFiles([])
            })
        }}
      >
        <div className="flex justify-between">
          <div>
            <div className="p-2 text-xl">信息:</div>
            <fieldset className="fieldset w-96">
              <label className="label">客户行业</label>
              <input type="text" className="input focus-within:outline-0" name='industry' required placeholder="industry" />
              <label className='label'>描述</label>
              <textarea className="textarea focus-within:outline-0" name='description' placeholder="My awesome page" rows={10} />
              <label className="label">类别</label>
              <input type="text" className="input focus-within:outline-0" name='classification' placeholder="classification" />
            </fieldset>
          </div>
          <button type="submit" className="btn btn-neutral ml-auto">
            提交
          </button>
        </div>
        <div className="my-4 border-b border-gray-100"></div>
        <div className="p-2 text-xl">图片:</div>
        <fieldset className="fieldset">
          <legend className="fieldset-legend">Pick some file</legend>
          <div className='flex justify-center items-center'>
            <div className='w-[clamp(40rem,50dvw,100%)]'>
              <FilePond
                files={files}
                onupdatefiles={(file) => {
                  setFiles(file.map((item) => item.file))
                }}
                allowMultiple={true}
                acceptedFileTypes={['image/*']}
                credits={false}
                labelIdle='拖拽文件或 <span class="filepond--label-action">点击选择</span>'
              />
            </div>
          </div>
        </fieldset>
      </form>
    </div>
  )
}
