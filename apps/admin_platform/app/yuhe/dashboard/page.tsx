import { Dashboard } from '@/app/component/dashboard'
import { deleteDashboardData, deleteDashboardTag, addDashboardTag, updateDashboardDescription, queryDashboardData, queryDashboardDataCount } from '../api/dashboard_data'
import { queryAllDashboardTags } from '../api/dashboard_tag'

export default async function Page({ searchParams }:{
  searchParams: Promise<{ page?:string, pageSize?:string, tags?:string }>
}) {
  const searchParam = await searchParams
  const selectedTagIds = searchParam.tags ? searchParam.tags.split(',').filter(Boolean) : []

  return <Dashboard
    page={Number(searchParam.page ?? 1)}
    pageSize={Number(searchParam.pageSize ?? 20)}
    selectedTagIds={selectedTagIds}
    queryDashboardData={queryDashboardData}
    queryDashboardDataCount={queryDashboardDataCount}
    queryAllTags={queryAllDashboardTags}
    deleteDashboardData={deleteDashboardData}
    deleteDashboardTag={deleteDashboardTag}
    addDashboardTag={addDashboardTag}
    updateDashboardDescription={updateDashboardDescription}
  />
}