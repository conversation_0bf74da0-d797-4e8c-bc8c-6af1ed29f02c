'use client'
import { use } from 'react'
import { queryChatHistoryByChatId } from '../../../api/chat_history'
import { queryLogByChatId } from '../../../api/log_store'
import { ChatHistory } from '@/app/component/user/chat_history'
import { createManyDashboardData, queryDashboardDataByChatId } from '@/app/yuhe/api/dashboard_data'
import { queryAllDashboardTags, findOrCreateDashboardTag } from '@/app/yuhe/api/dashboard_tag'
import { queryChatById } from '@/app/yuhe/api/chat'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  return <ChatHistory
    queryDashboardDataByChatId={queryDashboardDataByChatId}
    createManyDashboardData={createManyDashboardData}
    queryAllDashboardTags={queryAllDashboardTags}
    findOrCreateDashboardTag={findOrCreateDashboardTag}
    id={id}
    queryChatHistoryByChatId={queryChatHistoryByChatId}
    queryLogByChatId={queryLogByChatId}
    queryChatById={queryChatById}
    langsmithProjectId='06e01395-e9a2-41e9-8fea-ba3fed9aa3f0'
  />
}