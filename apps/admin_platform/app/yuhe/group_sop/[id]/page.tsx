'use client'

import { use } from 'react'
import { GroupSopDetail } from '@/app/component/group_sop/groupSopDetail'
import { copyGroupSop, queryGroupSopById, testGroupAction, testGroupSop } from '../../api/group_sop'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = use(params)
  return <GroupSopDetail
    id={param.id}
    queryGroupSopById={queryGroupSopById}
    copyGroupSop={copyGroupSop}
    testGroupAction={testGroupAction}
    testGroupSop={testGroupSop}
  />
}
