'use client'
import { use } from 'react'
import { EditGroupSop } from '@/app/component/group_sop/editGroupSop'
import { getGroupConditionJudgeKeys, getGroupCustomKeys, getGroupLinkSourceVariableTagKeys, getGroupVariableMapKeys, queryGroupSopById, updateGroupSop } from '@/app/yuhe/api/group_sop'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = use(params)
  return <EditGroupSop id={param.id}
    getConditionJudgeKeys={getGroupConditionJudgeKeys}
    getCustomKeys={getGroupCustomKeys}
    getLinkSourceVariableTagKeys={getGroupLinkSourceVariableTagKeys}
    getVariableMapKeys={getGroupVariableMapKeys}
    queryGroupSopById={queryGroupSopById}
    updateGroupSop={updateGroupSop}
  />
}
