import { GroupSopTags } from '@/app/component/group_sop/groupSopTags'
import { changeGroupTagEnable, deleteGroupTag, queryGroupTags, updateGroupTagEnableGroup } from '../api/group_sop_tag'
import { queryAllGroup } from '../api/group'
import { updateGroupSopMq, updateRedisGroupSop, validateGroupSops } from '../api/group_sop'

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{
    page?: string;
    pageSize?: string ;
  }>;
}) {
  const searchParam = await searchParams
  const page = Number(searchParam.page ?? 1)
  const pageSize = Number(searchParam.pageSize ?? 20)
  const tags = await queryGroupTags({ page, pageSize })
  const groups = await queryAllGroup()
  return <GroupSopTags
    tags={tags}
    groups={groups}
    tagLinkPrefix='./group_sop/tag/'
    validateGroupSops={validateGroupSops}
    updateGroupSopMq={updateGroupSopMq}
    updateGroupTagEnableGroup={updateGroupTagEnableGroup}
    changeGroupTagEnable={changeGroupTagEnable}
    deleteGroupTag={deleteGroupTag}
    updateRedisGroupSop={updateRedisGroupSop}
  />
}
