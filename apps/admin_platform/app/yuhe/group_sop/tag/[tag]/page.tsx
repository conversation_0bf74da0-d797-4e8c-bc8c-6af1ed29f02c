import { GroupSopTopicShow } from '@/app/component/group_sop/groupSopTopicShow'
import { queryAllGroupTags } from '@/app/yuhe/api/group_sop_tag'
import { changeGroupSopTopicEnable, copyGroupTopicToTag, deleteGroupTopic, queryGroupSopTopicByTag, renameGroupTopic } from '@/app/yuhe/api/group_sop_topic'
import { queryAllTags } from '@/app/yuhe/api/sop_tag'

export default async function Page({ params }:{
  params: Promise<{ tag: string }>
}) {
  const { tag } = await params
  const decodeTag = decodeURIComponent(tag)
  return <div>
    <GroupSopTopicShow
      tag={decodeTag}
      querySopTopicByTag={queryGroupSopTopicByTag}
      changeSopTopicEnable={changeGroupSopTopicEnable}
      copyTopicToTag={copyGroupTopicToTag}
      deleteTopic={deleteGroupTopic}
      renameTopic={renameGroupTopic}
      queryAllTags={queryAllGroupTags}
    />
  </div>
}