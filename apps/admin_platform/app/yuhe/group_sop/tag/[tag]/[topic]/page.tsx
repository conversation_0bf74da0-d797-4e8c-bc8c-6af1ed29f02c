'use client'
import { use } from 'react'
import { GroupTagPagination } from '@/app/component/group_sop/groupTagPagination'
import { deleteGroupSop, queryGroupSop, queryGroupSopCount, testAllGroupSop, testGroupAction, updateGroupSop } from '@/app/yuhe/api/group_sop'
import { importGroupSop } from '@/app/yuhe/api/group_sop_topic'
export default function Page({
  searchParams,
  params
}: {
  searchParams: Promise<{
    page: string | undefined;
    pageSize: string | undefined;
  }>;
  params: Promise<{ tag: string, topic:string }>
}) {
  const searchParam = use(searchParams)
  const param = use(params)
  const page = Number(searchParam.page ?? 1)
  const pageSize = Number(searchParam.pageSize ?? 20)
  const tag = decodeURIComponent(param.tag)
  const topic = decodeURIComponent(param.topic)
  return <GroupTagPagination
    page={page}
    pageSize={pageSize}
    tag={tag}
    topic={topic}
    deleteGroupSop={deleteGroupSop}
    queryGroupSop={queryGroupSop}
    queryGroupSopCount={queryGroupSopCount}
    testGroupAction={testGroupAction}
    testAllGroupSop={testAllGroupSop}
    updateGroupSop={updateGroupSop}
    importGroupSop={importGroupSop}
  />
}
