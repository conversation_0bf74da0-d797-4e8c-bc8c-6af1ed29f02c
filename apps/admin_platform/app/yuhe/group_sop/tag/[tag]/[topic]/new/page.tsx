'use client'

import { use } from 'react'
import { NewGroupSop } from '@/app/component/group_sop/newGroupSop'
import { getGroupConditionJudgeKeys, getGroupCustomKeys, getGroupLinkSourceVariableTagKeys, getGroupVariableMapKeys, saveGroupSop } from '@/app/yuhe/api/group_sop'
export default function Page({ params }:{params: Promise<{ tag: string, topic:string }>}) {
  const param = use(params)
  const tag = decodeURIComponent(param.tag)
  const topic = decodeURIComponent(param.topic)
  return <NewGroupSop
    tag={tag}
    topic={topic}
    getConditionJudgeKeys={getGroupConditionJudgeKeys}
    getCustomKeys={getGroupCustomKeys}
    getLinkSourceVariableTagKeys={getGroupLinkSourceVariableTagKeys}
    getVariableMapKeys={getGroupVariableMapKeys}
    saveGroupSop={saveGroupSop}
  />
}
