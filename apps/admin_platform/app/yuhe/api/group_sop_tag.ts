'use server'

import { GroupSopTag } from '@/app/type/group_sop_tag'
import { AdminPrismaMongoClient } from '@/lib/prisma'


export async function queryGroupTags({ page, pageSize }:{ page:number, pageSize:number}):Promise<GroupSopTag[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const tags = await mongoClient.group_sop_tag.findMany({ take:pageSize, skip:pageSize * (page - 1) })
  return tags
}
export async function createGroupTag(name:string, enableGroups:string[]) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.group_sop_tag.create({ data:{ name, enable:false, enable_group:enableGroups } })
}

export async function deleteGroupTag(id: string) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const tag = await mongoClient.group_sop_tag.findFirst({ where:{ id } })
  if (!tag) {
    throw (`没有找到${id}`)
  }
  await mongoClient.group_sop_tag.delete({ where:{ id } })
  await mongoClient.group_sop_topic.deleteMany({ where:{ tag:tag.name } })
  await mongoClient.group_sop.deleteMany({ where:{ tag:tag.name } })
}

export async function changeGroupTagEnable(tag_id: string, enable:boolean) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.group_sop_tag.update({ where:{ id:tag_id }, data:{ enable } })
}

export async function updateGroupTagEnableGroup(tag_id:string, enableGroup:string[]) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.group_sop_tag.update({ where:{ id:tag_id }, data:{ enable_group:enableGroup } })
}

export async function queryAllGroupTags():Promise<GroupSopTag[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const tags = await mongoClient.group_sop_tag.findMany()
  return tags
}