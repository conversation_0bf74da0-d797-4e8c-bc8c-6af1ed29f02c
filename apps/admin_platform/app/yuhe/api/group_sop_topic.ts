'use server'
import { SopTopic } from '@/app/type/sop_topic'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import { Sop } from '@/app/type/sop'
import { GroupSopTopic } from '@/app/type/group_sop_topic'
import { GroupSop } from '@/app/type/group_sop'

export async function queryGroupSopTopicByTag(tag:string): Promise<GroupSopTopic[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const result = await mongoClient.group_sop_topic.findMany({ where:{ tag } })
  return result
}

export async function changeGroupSopTopicEnable(id:string, enable:boolean): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.group_sop_topic.update({ where:{ id }, data:{ enable } })
}

export async function createGroupTopic(tag:string, topic:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.group_sop_topic.create({ data:{
    name:topic,
    enable:false,
    tag
  } })
}

export async function deleteGroupTopic(id: string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const topicInfo = await mongoClient.group_sop_topic.findFirst({ where:{ id } })
  if (!topicInfo) {
    throw (`没有找到topic ${id}`)
  }
  await mongoClient.group_sop_topic.delete({ where:{ id } })
  await mongoClient.group_sop.deleteMany({ where:{ tag:topicInfo.tag, topic:topicInfo.name } })
}

export async function queryAllGroupSopTopics(): Promise<GroupSopTopic[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  return await mongoClient.group_sop_topic.findMany()
}

export async function copyGroupTopicToTag(topicId:string, tagName:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()

  // copy topic
  const topicInfo = await mongoClient.group_sop_topic.findFirst({ where:{ id:topicId } })
  if (!topicInfo) {
    throw (`没有找到对应的topic, topic_id:${topicId}`)
  }
  const newTopicName = `${topicInfo.name}_copy`
  await mongoClient.group_sop_topic.create({ data:{ tag: tagName, name:newTopicName, enable:false } })
  const sopInfo = await mongoClient.group_sop.findMany({ where: { tag:topicInfo.tag, topic:topicInfo.name } })
  await mongoClient.group_sop.createMany({ data:sopInfo.map((item) => ({ ...item, id:undefined, tag:tagName, topic:newTopicName } as unknown as Sop)) })
}

export async function renameGroupTopic(topicId:string, name:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const topicInfo = await mongoClient.group_sop_topic.findFirst({ where:{ id:topicId } })
  if (!topicInfo) {
    throw (`没有找到对应的topic, topic_id:${topicId}`)
  }

  await mongoClient.group_sop_topic.update({ where:{ id:topicId }, data:{ name:name } })
  await mongoClient.group_sop.updateMany({ where:{ tag: topicInfo.tag, topic: topicInfo.name }, data:{ topic: name } })
}

export async function importGroupSop(tag:string, topic:string, sop:Omit<GroupSop, 'id'>[]):Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const sopWithNewTagAndTopic = sop.map((item) => ({ ...item, id:undefined, tag, topic }))
  await mongoClient.group_sop.createMany({ data:sopWithNewTagAndTopic })
}