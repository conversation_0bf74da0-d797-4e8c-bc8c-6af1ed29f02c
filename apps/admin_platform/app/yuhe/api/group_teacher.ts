'use server'
import { AccountTeacherData } from '@/app/type/account'
import { JuZiWecomClient } from 'model/juzi/client'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import axios from 'axios'


export async function getTeacherByName(imBotId: string, userName: string):Promise<AccountTeacherData[]> {

  let currentPage = 0
  const res: AccountTeacherData[] = []
  const set = new Set<string>()

  while (true) {
    currentPage += 1
    const url = 'v2/customer/list'
    const client = new JuZiWecomClient()
    const param = {
      current: currentPage,
      pageSize: 1000,
      imBotId,
      coworker: true
    }
    const response = await client.get(url, param)

    const datas = (response.data as any).data as (any[]|undefined)
    if (!datas || datas.length === 0) break

    for (const data of datas) {
      if (!data.name) continue
      if (data.name.includes(userName) && !set.has(data.imContactId)) {
        set.add(data.imContactId)
        res.push({
          imContactId: data.imContactId,
          name: `${data.name}(${data.imInfo.externalUserId})`,
          wecomUserId: data.imInfo.externalUserId
        })
      }
    }
  }

  return res
}

export async function updateGroupTeacher(accountWechatId: string, userData: AccountTeacherData):Promise<void> {
  await AdminPrismaMongoClient.getYuheInstance().group_teacher.upsert({
    where: {
      accountWechatId:accountWechatId
    },
    update: {
      imContactId:  userData.imContactId,
      name: userData.name,
      wecomUserId: userData.wecomUserId
    },
    create:{
      accountWechatId:accountWechatId,
      imContactId:  userData.imContactId,
      name: userData.name,
      wecomUserId: userData.wecomUserId ?? ''
    }
  })
}

export async function inviteGroup(accountId:string): Promise<void> {
  const mongoConfigClient = AdminPrismaMongoClient.getConfigInstance()
  const accountInfo = await mongoConfigClient.config.findFirst({ where:{ id:accountId } })
  if (!accountInfo) {
    throw (`没有这个id ${accountId}`)
  }
  await axios.post(`${accountInfo.address}/test/event/handle_invite_group_fail`)
}