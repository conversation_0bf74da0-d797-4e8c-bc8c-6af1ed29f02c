'use server'

import dayjs from 'dayjs'
import { SopRecord } from '@/app/type/sop_record'
import { AdminPrismaMongoClient } from '@/lib/prisma'

export async function analysisSop(courseNo:number) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const usersCurrent = (await mongoClient.chat.findMany({ where:{ course_no:courseNo }, select:{ id:true, course_no:true, course_no_ori:true } })).filter((item) => {
    if (!item.course_no_ori) {
      return item.course_no == courseNo
    } else {
      return item.course_no_ori == courseNo
    }
  }).map((item) => item.id)
  const usersOri = (await mongoClient.chat.findMany({ where:{ course_no_ori:courseNo }, select:{ id:true, } })).map((item) => item.id)
  const users = [...new Set([...usersCurrent, ...usersOri])]
  const sopRecord:Record<string, {
    sent:number
    reply30Min:number
    reply60Min:number
  }> = {}
  for (const user of users) {
    const alreadyCountSop:Record<string, boolean> = {}
    const chatHistory = await mongoClient.chat_history.findMany({ where:{ chat_id:user }, orderBy:{ created_at:'asc' }, select:{ sop_id:true, created_at:true, role:true } })
    for (const history of chatHistory) {
      if (!history.sop_id) continue
      if (alreadyCountSop[history.sop_id]) continue
      alreadyCountSop[history.sop_id] = true
      if (!sopRecord[history.sop_id]) {
        sopRecord[history.sop_id] = {
          sent:0,
          reply30Min:0,
          reply60Min:0
        }
      }
      sopRecord[history.sop_id].sent += 1
      const historyTime = dayjs(history.created_at)
      let isWithIn30Min = false
      let isWithIn60Min = false
      for (const anotherHistory of chatHistory) {
        if (anotherHistory.role != 'user') continue
        const anotherHistoryTime = dayjs(anotherHistory.created_at)
        if (anotherHistoryTime.isAfter(historyTime) && anotherHistoryTime.diff(historyTime, 'minute') <= 30) {
          isWithIn30Min = true
        }
        if (anotherHistoryTime.isAfter(historyTime) && anotherHistoryTime.diff(historyTime, 'minute') <= 60) {
          isWithIn60Min = true
        }
      }
      if (isWithIn30Min) {
        sopRecord[history.sop_id].reply30Min += 1
      }
      if (isWithIn60Min) {
        sopRecord[history.sop_id].reply60Min += 1
      }
    }
  }
  await mongoClient.sop_record.deleteMany({ where:{ course_no:courseNo } })
  if (Object.entries(sopRecord).length == 0) {
    return
  }
  await mongoClient.sop_record.createMany({ data:Object.entries(sopRecord).map(([key, value]) => (<Omit<SopRecord, 'id'>>{
    course_no:courseNo,
    sop_id:key,
    sent_count:value.sent,
    reply_count_30min:value.reply30Min,
    reply_count_60min:value.reply60Min
  })) })
}

export async function getSopRecordsByCourseNo(startCourseNo:number, endCourseNo:number):Promise<SopRecord[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const result = await mongoClient.sop_record.findMany({ where:{ course_no:{ gte:startCourseNo, lte:endCourseNo } } })
  return result
}