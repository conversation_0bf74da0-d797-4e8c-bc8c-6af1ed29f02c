'use server'
import { AdminPrismaMongoClient } from '@/lib/prisma'

export async function insertIpTable(account:string, startCourseNo:number, endCourseNo:number, ip:string): Promise<void> {
  const mongoClienet = AdminPrismaMongoClient.getYuheInstance()
  await mongoClienet.ip_table.deleteMany({ where:{ account, start_course_no:{ gte:startCourseNo, lte:endCourseNo }, end_course_no:{ gte:startCourseNo, lte:endCourseNo } } })

  // 原来的有的一个包裹住现在要插入的
  const wrap = await mongoClienet.ip_table.findFirst({ where:{ account, start_course_no:{ lte:startCourseNo }, end_course_no:{ gte:endCourseNo } } })
  if (wrap) {
    if (wrap.ip == ip) {
      return
    }
    await mongoClienet.ip_table.delete({ where:{ id:wrap.id } })
    if (wrap.start_course_no < startCourseNo) {
      await mongoClienet.ip_table.create({ data:{
        account,
        start_course_no:wrap.start_course_no,
        end_course_no:startCourseNo - 1,
        ip:wrap.ip
      } })
    }
    if (wrap.end_course_no > endCourseNo) {
      await mongoClienet.ip_table.create({ data:{
        account,
        start_course_no:endCourseNo + 1,
        end_course_no:wrap.end_course_no,
        ip:wrap.ip
      } })
    }
  }

  const lt = await mongoClienet.ip_table.findFirst({ where:{ account, start_course_no:{ lt:startCourseNo }, end_course_no:{ gte:startCourseNo, lte:endCourseNo } } })
  if (lt) {
    await mongoClienet.ip_table.delete({ where:{ id:lt.id } })
    await mongoClienet.ip_table.create({ data:{ account, start_course_no:lt.start_course_no, end_course_no:startCourseNo - 1, ip:lt.ip } })
  }

  const gt = await mongoClienet.ip_table.findFirst({ where:{ account, start_course_no:{ gte:startCourseNo, lte:endCourseNo }, end_course_no:{ gt:endCourseNo } } })
  if (gt) {
    await mongoClienet.ip_table.delete({ where:{ id:gt.id } })
    await mongoClienet.ip_table.create({ data:{ account, start_course_no:endCourseNo + 1, end_course_no:gt.end_course_no, ip:gt.ip } })
  }

  await mongoClienet.ip_table.create({
    data:{
      account,
      start_course_no:startCourseNo,
      end_course_no:endCourseNo,
      ip
    }
  })
}