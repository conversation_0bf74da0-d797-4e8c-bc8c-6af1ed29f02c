import { Document } from 'langchain/document'
import ElasticSearchService from 'model/elastic_search/elastic_search'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import { FreeSpiritOss } from 'model/oss/oss'
import { RAGHelper } from 'service/rag/rag_helper'

const salesCaseRagIndex = 'yuhe_sales_case'

const ossPrefix = 'http://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/'

export async function insertSalesCase(
  topic: string,
  description: string,
  type: string,
  images: string[],
) {
  await RAGHelper.addDocuments(salesCaseRagIndex, [
    new Document({
      pageContent: topic,
      metadata: {
        topic: topic,
        description: description,
        type: type,
        images: JSON.stringify(images),
      },
    }),
  ])
}

export async function listSalesCase(
  topic: string,
  number: number,
  minScore: number,
) {
  return await ElasticSearchService.embeddingSearch(
    salesCaseRagIndex,
    topic,
    number,
    minScore,
  )
}

export async function updateSalesCase() {

}

export async function deleteSalesCase(docId: string, images:  string[]) {
  //删除图片
  if (images.length > 0) {
    const res = await AdminPrismaMongoClient.getYuheInstance().rag_map_oss.findMany({
      where: {
        rag_resource_name: {
          in: images,
        },
      },
    })
    const bucket = new FreeSpiritOss('static')
    const ossNameList = res.map((item) =>
      item.oss_url.substring(ossPrefix.length),
    )
    if (ossNameList.length > 0) {
      await bucket.deleteObjects(ossNameList)
    }
    await AdminPrismaMongoClient.getYuheInstance().rag_map_oss.deleteMany({
      where: {
        rag_resource_name: {
          in: images,
        },
      },
    })
  }
  await ElasticSearchService.deleteDocuments(salesCaseRagIndex, [docId])
}