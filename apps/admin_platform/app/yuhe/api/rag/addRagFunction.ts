import { v4 } from 'uuid'
import { Document } from 'langchain/document'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import { Rag, RagType } from '@/app/type/rag'
import { RagIndex } from './ragType'
import { putObjectStream } from '@/app/lib/aliyunoss'
import { RAGHelper } from 'service/rag/rag_helper'

export async function addRag({
  question,
  doc,
  rag,
  formData,
}: {
  question: string
  doc: string
  rag: Rag[]
  formData: FormData
}) {
  'use server'
  const ansList: string[] = []

  for (const info of rag) {
    const detail = formData.get(info.key)
    if (info.type === RagType.text) {
      ansList.push(detail as string)
    } else if (info.type === RagType.file) {
      const file = detail as File
      const splitedFileName = file.name.split('.')
      if (splitedFileName.length < 2) {
        throw `no extension, file name is: ${file.name}`
      }
      const extension = splitedFileName[splitedFileName.length - 1]
      const description = formData.get(`${info.key}_description`) as string
      const resourceName = getResourceName(extension, description)
      ansList.push(`[${resourceName}]`)
      const ossUrl = await putObjectStream(resourceName, file)
      await AdminPrismaMongoClient.getYuheInstance().rag_map_oss.create({
        data: {
          rag_resource_name: resourceName,
          oss_url: ossUrl,
        }
      })
    } else {
      throw `unknown type: ${info.type}`
    }
  }

  const ans = ansList.join('\n')

  const toInsert: QA = {
    q: question,
    a: ans,
    chunk: '',
    doc: doc,
  }

  await RAGHelper.addDocuments(RagIndex, [
    new Document({
      pageContent: toInsert.q,
      metadata: toInsert,
    }),
  ])
}

function getResourceName(extension: string, description: string): string {
  const shortUUId = v4().slice(0, 4)

  return `${description}_${shortUUId}.${extension}`
}
interface QA {
  q: string // 问题
  a: string // 答案
  chunk: string // 来源文本段
  doc: string // 文档tag，用于通过时间过滤
}