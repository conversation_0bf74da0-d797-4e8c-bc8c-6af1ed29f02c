'use client'

import { use } from 'react'
import { getConditionJudgeKeys, getCustom<PERSON>eys, getVariableMapKeys, getLinkSourceVariableTagKeys, SaveSop } from '@/app/yuhe/api/sop'
import { NewSop } from '@/app/component/sop/newSop'
import { ActionType } from 'service/visualized_sop/visualized_sop_type'
export default function Page({ params }:{params: Promise<{ tag: string, topic:string }>}) {
  const param = use(params)
  const tag = decodeURIComponent(param.tag)
  const topic = decodeURIComponent(param.topic)
  return <NewSop
    tag={tag}
    topic={topic}
    getConditionJudgeKeys={getConditionJudgeKeys}
    getCustomKeys={getCustomKeys}
    getLinkSourceVariableTagKeys={getLinkSourceVariableTagKeys}
    getVariableMapKeys={getVariableMapKeys}
    saveSop={SaveSop}
    enableActionType={[ActionType.text, ActionType.dynamicPrompt, ActionType.image, ActionType.video, ActionType.voice, ActionType.file, ActionType.custom, ActionType.link, ActionType.videoChannel]}
  />
}
