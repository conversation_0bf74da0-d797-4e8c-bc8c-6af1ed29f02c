'use client'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { ChatEdit } from './edit'
import { YuheUserData } from '../../type/user'

export default function UserEdit<T>({
  id,
  changeCourseNo,
  changeNextStage,
  changePhone,
  queryChatById,
  clearCache,
  updateIsPaid,
  updateIsInviteGroupAfterPayment,
  updateIp,
  updatePaymentNumber,
  updatePayTime,
  resetSop,
  stageOption
}: {
  id:string
  queryChatById(id: string): Promise<YuheUserData | null>
  changeNextStage(chatId: string, stage: T): Promise<void>
  changePhone(chatId: string, phone: string): Promise<void>
  changeCourseNo(chatId: string, courseNo: number): Promise<void>
  clearCache(chatId:string):Promise<void>
  updateIsPaid(chatId:string, isPaid:boolean): Promise<void>
  updateIsInviteGroupAfterPayment(chatId:string, isInviteGroupFailAfterPayment:boolean): Promise<void>
  updateIp(chatId:string, ip:string):Promise<void>
  resetSop(chatId:string): Promise<void>
  updatePayTime(chatId: string, time: string): Promise<void>
  updatePaymentNumber(chatId:string, number:number):Promise<void>
  stageOption:string[]
}) {
  const [chat, setChat] = useState<YuheUserData | null>(null)
  useEffect(() => {
    toast.promise(queryChatById(id).then((result) => {
      setChat(result)
    }), {
      pending:'query pending',
      error: 'query error',
      success: 'query success'
    })
  }, [id])
  if (chat == null) {
    return <span className="loading loading-dots loading-xl"></span>
  } else {
    return <ChatEdit
      chat={chat}
      changeCourseNo={changeCourseNo}
      changeNextStage={changeNextStage}
      changePhone={changePhone} stageOption={stageOption}
      clearCache={clearCache} updateIsPaid={updateIsPaid}
      updateIsInviteGroupAfterPayment={updateIsInviteGroupAfterPayment}
      updateIp={updateIp}
      updatePayTime={updatePayTime}
      updatePaymentNumber={updatePaymentNumber}
      resetSop={resetSop}
    />
  }
}