import { Teacher } from '@/app/component/teacher'
import { queryAccounts, queryGroupTeacherData } from '../api/account'
import { getTeacherByName, inviteGroup, updateGroupTeacher } from '../api/group_teacher'

export const dynamic = 'force-dynamic'

export default async function Page() {
  const accountTeachers = await queryGroupTeacherData()
  const accounts = await queryAccounts()
  return <Teacher
    accounts={accounts}
    groupTeachers={accountTeachers}
    getTeacherByName={getTeacherByName}
    updateGroupTeacher={updateGroupTeacher}
    inviteGroup={inviteGroup}
  />
}