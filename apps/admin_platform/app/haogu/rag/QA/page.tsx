'use server'

import { Rag } from '@/app/component/rag/QA'
import { addRag } from '../../api/rag/addRagFunction'
import { deleteRag, searchRag, updateRag } from '../../api/rag/searchRagFunction'
import { ragDoc } from '../../api/rag/ragType'

export default async function Page() {
  return <Rag
    ragDoc={ragDoc}
    addRag={addRag}
    searchRag={searchRag}
    updateRag={updateRag}
    deleteRag={deleteRag}
    industryTag={{}}
  />
}
