'use server'
import { Document } from 'langchain/document'
import { RagIndex } from './ragType'
import { AliyunCredentials } from 'lib/cer'
import ElasticSearchService, { ElasticSearchClient } from 'model/elastic_search/elastic_search'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import { FreeSpiritOss } from 'model/oss/oss'
import { RAGHelper } from 'service/rag/rag_helper'

const ossPrefix = 'http://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/'

AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})
export async function searchRag({
  question,
  number,
  minScore,
}: {
  question: string
  number: number
  minScore: number
}) {
  'use server'
  const res = await ElasticSearchService.embeddingSearch(
    RagIndex,
    question,
    number,
    minScore,
  )
  return res
}

export async function deleteRag(id: string, resourceNames: string[]) {
  'use server'
  if (resourceNames.length > 0) {
    const res = await AdminPrismaMongoClient.getHaoguCommonInstance().rag_map_oss.findMany({
      where: {
        rag_resource_name: {
          in: resourceNames,
        },
      },
    })
    const bucket = new FreeSpiritOss('static')
    const ossNameList = res.map((item) =>
      item.oss_url.substring(ossPrefix.length),
    )
    if (ossNameList.length > 0) {
      await bucket.deleteObjects(ossNameList)
    }
    await AdminPrismaMongoClient.getHaoguCommonInstance().rag_map_oss.deleteMany({
      where: {
        rag_resource_name: {
          in: resourceNames,
        },
      },
    })
  }
  await ElasticSearchClient.getInstance().delete({
    id,
    index:RagIndex,
  })
}

export async function updateRag({
  question,
  answer,
  tag,
  doc,
  id,
}: {
  question: string
  answer: string
  tag: string
  doc: string
  id: string
}) {
  const document = new Document({
    metadata: {
      q: question,
      a: answer,
      tag: tag,
      doc: doc,
    },
    pageContent: question,
  })

  await RAGHelper.addDocumentsWithIds(RagIndex, [document], [id])
}

