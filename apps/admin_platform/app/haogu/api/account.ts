'use server'

import { Account<PERSON><PERSON>, GroupTeacher } from '@/app/type/account'
import { AdminPrismaMongoClient } from '@/lib/prisma'

export async function queryAccounts():Promise<AccountData[]> {
  const mongoClient = AdminPrismaMongoClient.getConfigInstance()
  const result = await mongoClient.config.findMany({ where:{ enterpriseName:'haogu' }, select:{ id:true, accountName:true, wechatId:true } })
  return result
}
