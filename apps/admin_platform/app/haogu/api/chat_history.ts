'use server'
import { ChatHistory } from '@/app/type/chat_history'
import { AdminPrismaMongoClient } from '@/lib/prisma'

export async function queryChatHistoryByChatId(chatId:string):Promise<ChatHistory[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const history = await mongoClient.chat_history.findMany({ where:{ chat_id:chatId }, orderBy:[{ created_at:'asc' }] })
  return history as ChatHistory[]
}