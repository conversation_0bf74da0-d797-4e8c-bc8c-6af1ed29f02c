'use server'

import { DashboardData, DashboardDataWithChatHistory, SimpleChatHistory } from '@/app/type/dashboard_data'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import { getDashboardTagsByIds, findOrCreateDashboardTag } from './dashboard_tag'

export async function createManyDashboardData(dashboardData:Omit<DashboardData, 'id' | 'created_at'>[]):Promise<void> {
  try {
    const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
    console.log('准备创建dashboard数据:', dashboardData)
    await mongoClient.dashboard_data.createMany({ data:dashboardData })
    console.log('dashboard数据创建成功')
  } catch (error) {
    console.error('createManyDashboardData 错误:', error)
    throw error
  }
}

export async function queryDashboardData(page:number, pageSize:number, tagIds?: string[]): Promise<DashboardDataWithChatHistory[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()

  const whereClause: any = {}
  if (tagIds && tagIds.length > 0) {
    whereClause.tag_ids = {
      hasSome: tagIds
    }
  }

  const dashboard = await mongoClient.dashboard_data.findMany({
    where: whereClause,
    orderBy:{ created_at:'desc' },
    take:pageSize,
    skip:(page - 1) * pageSize
  })
  return await mergeDashboardDataWithChatHistory(dashboard)
}

export async function queryDashboardDataByChatId(chatId:string): Promise<DashboardDataWithChatHistory[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const dashboard = await mongoClient.dashboard_data.findMany({ where:{ chat_id:chatId } })
  return await mergeDashboardDataWithChatHistory(dashboard)
}

export async function mergeDashboardDataWithChatHistory(dashboard:DashboardData[]):Promise<DashboardDataWithChatHistory[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const chatHistoryIdSet:Set<string> = new Set<string>()
  const tagIdSet:Set<string> = new Set<string>()

  for (const item of dashboard) {
    for (const id of item.chat_history_id) {
      chatHistoryIdSet.add(id)
    }
    for (const tagId of item.tag_ids) {
      tagIdSet.add(tagId)
    }
  }

  const chatHistory = await mongoClient.chat_history.findMany({ where:{ id:{ in:[...chatHistoryIdSet] } }, select:{ id:true, chat_id:true, content:true, created_at:true, role:true } })
  const chatHistoryMap = new Map<string, SimpleChatHistory>()
  for (const item of chatHistory) {
    chatHistoryMap.set(item.id, item)
  }

  const tags = await getDashboardTagsByIds([...tagIdSet])
  const tagMap = new Map()
  for (const tag of tags) {
    tagMap.set(tag.id, tag)
  }

  return dashboard.map((item) => {
    // 获取当前项目的聊天记录并按时间排序
    const itemChatHistory = item.chat_history_id.map((singleChatHistory) => {
      return chatHistoryMap.get(singleChatHistory)
    }).filter((singleChatHistory) => singleChatHistory != undefined)
      .sort((a, b) => a.created_at.getTime() - b.created_at.getTime())

    // 限制最多显示7条聊天记录
    const limitedChatHistory = itemChatHistory.slice(0, 7)

    return {
      ...item,
      chat_history: limitedChatHistory,
      tags: item.tag_ids.map((tagId) => tagMap.get(tagId)).filter((tag) => tag != undefined)
    }
  })
}

export async function queryDashboardDataCount(tagIds?: string[]): Promise<number> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()

  const whereClause: any = {}
  if (tagIds && tagIds.length > 0) {
    whereClause.tag_ids = {
      hasSome: tagIds
    }
  }

  return await mongoClient.dashboard_data.count({ where: whereClause })
}

export async function deleteDashboardData(id:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.dashboard_data.delete({ where:{ id } })
}

export async function deleteDashboardTag(id:string, tagId:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const info = await mongoClient.dashboard_data.findFirst({ where:{ id } })
  if (!info?.tag_ids) {
    return
  }
  const newTagIds = info.tag_ids.filter((item) => item != tagId)
  await mongoClient.dashboard_data.update({ where:{ id }, data:{ tag_ids:newTagIds } })
}

export async function addDashboardTag(id:string, tagName:string): Promise<{ success: boolean, tag?: any }> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const info = await mongoClient.dashboard_data.findFirst({ where:{ id } })
  if (!info) {
    return { success: false }
  }

  // 查找或创建标签
  const tag = await findOrCreateDashboardTag(tagName)

  // Check if tag already exists
  if (info.tag_ids.includes(tag.id)) {
    return { success: true, tag } // 标签已存在，但仍返回标签信息
  }

  const newTagIds = [...info.tag_ids, tag.id]
  await mongoClient.dashboard_data.update({ where:{ id }, data:{ tag_ids:newTagIds } })

  return { success: true, tag }
}

export async function updateDashboardDescription(id:string, description:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.dashboard_data.update({ where:{ id }, data:{ description } })
}