'use server'

import { AdminPrismaMongoClient } from '@/lib/prisma'
import { MaterialManager } from 'haogu/src/workflow/helper/material_manager'
import { ContentItem } from 'model/haogu/crm/type'

export interface Material {
  id: string
  source_id: string
  type: number
  title: string
  description: string
  main_category: string
  sub_category: string
  doc: string
  data: any
  enable: boolean
  es_id: string
  created_at?: Date
}

export interface MaterialQueryParams {
  page?: number
  pageSize?: number
  type?: number
  main_category?: string
  sub_category?: string
  doc?: string
}

export async function queryMaterials(params: MaterialQueryParams = {}) {
  const { page = 1, pageSize = 20, type, main_category, sub_category, doc } = params
  const skip = (page - 1) * pageSize

  try {
    const whereCondition: any = {}
    if (type !== undefined) {
      whereCondition.type = type
    }
    if (main_category) {
      whereCondition.main_category = main_category
    }
    if (sub_category) {
      whereCondition.sub_category = sub_category
    }
    if (doc) {
      whereCondition.doc = doc
    }

    const [materials, total] = await Promise.all([
      AdminPrismaMongoClient.getHaoguInstance().material.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy: {
          id: 'desc'  // 使用id排序代替created_at
        }
      }),
      AdminPrismaMongoClient.getHaoguInstance().material.count({
        where: whereCondition
      })
    ])

    return {
      materials,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  } catch (error) {
    console.error('Error querying materials:', error)
    throw error
  }
}

export async function updateMaterial(id: string, updates: {
  title?: string;
  description?: string;
  doc?: string;
  main_category?: string;
  sub_category?: string;
}) {
  try {
    const materialManager = new MaterialManager()
    const material = await materialManager.updateMaterialContent(id, updates)
    return material
  } catch (error) {
    console.error('Error updating material:', error)
    throw error
  }
}

export async function toggleMaterialEnable(id: string, enable: boolean) {
  try {
    const materialManager = new MaterialManager()

    if (enable) {
      // 启用素材：添加到ES
      await materialManager.enableMaterial(id)
    } else {
      // 禁用素材：从ES删除并清空es_id
      await materialManager.disableMaterial(id)
    }

    // 更新MongoDB中的enable状态
    const material = await AdminPrismaMongoClient.getHaoguInstance().material.update({
      where: { id },
      data: { enable }
    })

    return material
  } catch (error) {
    console.error('Error toggling material enable:', error)
    throw error
  }
}

export async function searchHaoGuMaterials(page: number, pageSize: number, type: number, mediaType?: number) {
  try {
    const materialManager = new MaterialManager()
    const result = await materialManager.searchMaterialFromHaoGu(page, pageSize, type, mediaType)
    if (result == null) {
      throw ('result is null')
    }
    return result
  } catch (error) {
    console.error('Error searching HaoGu materials:', error)
    throw error
  }
}

export async function importMaterialsFromHaoGu(mainCategory:string, subCategory:string, doc: string, contentItems: ContentItem[]) {
  try {
    const materialManager = new MaterialManager()
    const results: Array<{ id: string | number; status: 'imported' | 'already_exists' }> = []

    for (const item of contentItems) {
      const existingMaterial = await materialManager.searchMaterialById(item.id.toString())
      if (!existingMaterial) {
        await materialManager.saveMaterial(mainCategory, subCategory, doc, item)
        results.push({ id: item.id, status: 'imported' })
      } else {
        results.push({ id: item.id, status: 'already_exists' })
      }
    }

    return results
  } catch (error) {
    console.error('Error importing materials from HaoGu:', error)
    throw error
  }
}

export async function getMaterialTypes() {
  try {
    const materials = await AdminPrismaMongoClient.getHaoguInstance().material.groupBy({
      by: ['type'],
      _count: {
        type: true
      }
    })

    return materials.map((item) => ({
      type: item.type,
      count: item._count.type
    }))
  } catch (error) {
    console.error('Error getting material types:', error)
    throw error
  }
}

export async function getMainCategories() {
  try {
    const materials = await AdminPrismaMongoClient.getHaoguInstance().material.groupBy({
      by: ['main_category'],
      _count: {
        main_category: true
      }
    })

    return materials.map((item) => ({
      main_category: item.main_category,
      count: item._count.main_category
    })).sort((a, b) => b.count - a.count)
  } catch (error) {
    console.error('Error getting main categories:', error)
    throw error
  }
}

export async function getSubCategories(mainCategory?: string) {
  try {
    const whereCondition: any = {}
    if (mainCategory) {
      whereCondition.main_category = mainCategory
    }

    const materials = await AdminPrismaMongoClient.getHaoguInstance().material.groupBy({
      by: ['sub_category'],
      where: whereCondition,
      _count: {
        sub_category: true
      }
    })

    return materials.map((item) => ({
      sub_category: item.sub_category,
      count: item._count.sub_category
    })).sort((a, b) => b.count - a.count) // 按使用频率排序
  } catch (error) {
    console.error('Error getting sub categories:', error)
    throw error
  }
}

export async function deleteMaterial(id: string) {
  try {
    const materialManager = new MaterialManager()
    await materialManager.deleteMaterial(id)
  } catch (error) {
    console.error('Error deleting material:', error)
    throw error
  }
}

export async function getDocOptions() {
  try {
    const materials = await AdminPrismaMongoClient.getHaoguInstance().material.groupBy({
      by: ['doc'],
      _count: {
        doc: true
      }
    })

    return materials.map((item) => ({
      doc: item.doc,
      count: item._count.doc
    })).sort((a, b) => b.count - a.count)
  } catch (error) {
    console.error('Error getting doc options:', error)
    throw error
  }
}

export async function checkMaterialsExistence(sourceIds: string[]) {
  try {
    const results: Record<string, boolean> = {}

    // 批量查询已存在的素材
    const existingMaterials = await AdminPrismaMongoClient.getHaoguInstance().material.findMany({
      where: {
        source_id: {
          in: sourceIds
        }
      },
      select: {
        source_id: true
      }
    })

    // 构建存在状态映射
    const existingSourceIds = new Set(existingMaterials.map((m) => m.source_id))
    sourceIds.forEach((sourceId) => {
      results[sourceId] = existingSourceIds.has(sourceId)
    })

    return results
  } catch (error) {
    console.error('Error checking materials existence:', error)
    throw error
  }
}