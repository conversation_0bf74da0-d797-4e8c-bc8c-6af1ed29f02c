'use client'
import { use } from 'react'
import { queryChatHistoryByChatId } from '../../../api/chat_history'
import { queryLogByChatId } from '../../../api/log_store'
import { ChatHistory } from '@/app/component/user/chat_history'
import { queryChatById } from '@/app/haogu/api/chat'
import { queryDashboardDataByChatId, createManyDashboardData } from '@/app/haogu/api/dashboard_data'
import { queryAllDashboardTags, findOrCreateDashboardTag } from '@/app/haogu/api/dashboard_tag'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  return <ChatHistory
    queryDashboardDataByChatId={queryDashboardDataByChatId}
    createManyDashboardData={createManyDashboardData}
    queryAllDashboardTags={queryAllDashboardTags}
    findOrCreateDashboardTag={findOrCreateDashboardTag}
    id={id}
    queryChatHistoryByChatId={queryChatHistoryByChatId}
    queryLogByChatId={queryLogByChatId}
    queryChatById={queryChatById}
    langsmithProjectId='017a6931-76f9-43d7-bb91-587efd383d83'
  />
}