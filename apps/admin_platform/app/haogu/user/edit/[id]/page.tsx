import {
  changeCourseNo,
  changeNextStage,
  clearCache,
  freeKick,
  getOffset,
  queryChatById,
  sendFinishHomeworkEvent,
  sendOrderEvent,
  setOffset,
  startBigPlanner,
  updateBooleanField,
  updateIsAttendCourse,
  updateIsCompleteCourse,
  updateIsPaid,
  updateWatchRatio
} from '@/app/haogu/api/chat'
import UserEdit from '@/app/haogu/component/edit'
import { resetSop } from '@/app/haogu/api/sop'

import { Node } from 'service/agent/workflow'

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = await params
  return <UserEdit
    id={param.id}
    queryChatById={queryChatById}
    changeCourseNo={changeCourseNo}
    changeNextStage={changeNextStage}
    stageOption={Object.values(Node)}
    updateIsPaid={updateIsPaid}
    clearCache={clearCache}
    resetSop={resetSop}
    sendFinishHomeworkEvent={sendFinishHomeworkEvent}
    sendOrderEvent={sendOrderEvent}
    updateIsAttendCourse={updateIsAttendCourse}
    updateIsCompleteCourse={updateIsCompleteCourse}
    setOffset={setOffset}
    getOffset={getOffset}
    startBigPlanner={startBigPlanner}
    updateBooleanField={updateBooleanField}
    updateWatchRatio={updateWatchRatio}
    freeKick={freeKick}
  />
}
