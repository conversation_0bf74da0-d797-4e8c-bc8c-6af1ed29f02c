import { User } from '@/app/component/user'
import { queryAccounts } from '../api/account'
import { changeIsHumanInvolved, changeIsStopGroupPush, queryChats, queryChatsWithoutAi, queryChatsWithoutPhone, queryDefaultChats } from '../api/chat'

export default async function Page({ searchParams }:{searchParams: Promise<{ name_or_phone: string | undefined, course_no:string|undefined, tool:string|undefined}>}) {
  const searchParam = await searchParams
  return <User
    name_or_phone={searchParam.name_or_phone ?? ''}
    course_no={searchParam.course_no ?? ''}
    tool={searchParam.tool}
    queryAccounts={queryAccounts}
    queryChats={queryChats}
    queryChatsWithoutAi={queryChatsWithoutAi}
    queryChatsWithoutPhone={queryChatsWithoutPhone}
    queryDefaultChats={queryDefaultChats}
    changeIsHumanInvolved={changeIsHumanInvolved}
    changeIsStopGroupPush={changeIsStopGroupPush}
  />
}
