'use client'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { ChatEdit } from './edit'
import { HaoguUserData } from '../../type/user'

export default function UserEdit<T>({
  id,
  changeCourseNo,
  changeNextStage,
  queryChatById,
  clearCache,
  updateIsPaid,
  resetSop,
  updateIsAttendCourse,
  updateIsCompleteCourse,
  sendOrderEvent,
  sendFinishHomeworkEvent,
  setOffset,
  getOffset,
  startBigPlanner,
  updateBooleanField,
  updateWatchRatio,
  freeKick,
  stageOption
}: {
  id:string
  queryChatById(id: string): Promise<HaoguUserData | null>
  changeNextStage(chatId: string, stage: T): Promise<void>
  changeCourseNo(chatId: string, courseNo: number): Promise<void>
  clearCache(chatId:string):Promise<void>
  updateIsPaid(chatId:string, isPaid:boolean): Promise<void>
  resetSop(chatId:string): Promise<void>
  updateIsAttendCourse(chatId: string, day: number, isAttend: boolean): Promise<void>
  updateIsCompleteCourse(chatId: string, day: number, isAttend: boolean): Promise<void>
  sendOrderEvent(chatId: string): Promise<void>
  sendFinishHomeworkEvent(chatId: string, day: number, score: number): Promise<void>
  setOffset(chatId: string, offset: number): Promise<void>
  getOffset(chatId:string):Promise<number>
  startBigPlanner(chatId:string):Promise<void>
  updateBooleanField(chatId: string, fieldName: string, value: boolean): Promise<void>
  updateWatchRatio(chatId: string, day: number, ratio: number): Promise<void>
  freeKick(chatId: string, task: string): Promise<void>
  stageOption:string[]
}) {
  const [chat, setChat] = useState<HaoguUserData | null>(null)
  useEffect(() => {
    toast.promise(queryChatById(id).then((result) => {
      setChat(result)
    }), {
      pending:'query pending',
      error: 'query error',
      success: 'query success'
    })
  }, [id])
  if (chat == null) {
    return <span className="loading loading-dots loading-xl"></span>
  } else {
    return <ChatEdit
      chat={chat}
      changeCourseNo={changeCourseNo}
      changeNextStage={changeNextStage}
      stageOption={stageOption}
      clearCache={clearCache}
      updateIsPaid={updateIsPaid}
      resetSop={resetSop}
      updateIsAttendCourse={updateIsAttendCourse}
      updateIsCompleteCourse={updateIsCompleteCourse}
      sendFinishHomeworkEvent={sendFinishHomeworkEvent}
      sendOrderEvent={sendOrderEvent}
      setOffset={setOffset}
      getOffset={getOffset}
      startBigPlanner={startBigPlanner}
      updateBooleanField={updateBooleanField}
      updateWatchRatio={updateWatchRatio}
      freeKick={freeKick}
    />
  }
}
