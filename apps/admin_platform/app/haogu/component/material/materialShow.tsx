'use client'

import { useEffect, useState } from 'react'
import { Material, MaterialQueryParams } from '@/app/haogu/api/material'
import MaterialPreview from './materialPreview'
import AdvancedPagination from '../common/AdvancedPagination'
import { toast } from 'react-toastify'

interface MaterialType {
  type: number
  count: number
}

interface MainCategory {
  main_category: string
  count: number
}

interface SubCategory {
  sub_category: string
  count: number
}

interface DocOption {
  doc: string
  count: number
}

interface MaterialShowProps {
  queryMaterials: (params: MaterialQueryParams) => Promise<{
    materials: Material[]
    totalPages: number
    total: number
  }>
  getMaterialTypes: () => Promise<MaterialType[]>
  updateMaterial: (id: string, updates: {
    title?: string
    description?: string
    doc?: string
    main_category?: string
    sub_category?: string
  }) => Promise<Material>
  getMainCategories: () => Promise<MainCategory[]>
  getSubCategories: (mainCategory?: string) => Promise<SubCategory[]>
  deleteMaterial: (id: string) => Promise<void>
  toggleMaterialEnable: (id: string, enable: boolean) => Promise<Material>
  getDocOptions: () => Promise<DocOption[]>
  getPreviewContent: (material: Material, handlePreview: (material: Material) => void) => React.ReactNode
  renderPreviewContent: (material: Material) => React.ReactNode
  MATERIAL_TYPES: Record<number, string>
  DOC_OPTIONS: string[]
}

export default function MaterialShow({
  queryMaterials,
  getMaterialTypes,
  updateMaterial,
  getMainCategories,
  getSubCategories,
  deleteMaterial,
  toggleMaterialEnable,
  getDocOptions,
  getPreviewContent,
  renderPreviewContent,
  MATERIAL_TYPES,
  DOC_OPTIONS
}: MaterialShowProps) {
  const [materials, setMaterials] = useState<Material[]>([])
  const [materialTypes, setMaterialTypes] = useState<MaterialType[]>([])
  const [mainCategories, setMainCategories] = useState<MainCategory[]>([])
  const [subCategories, setSubCategories] = useState<SubCategory[]>([])
  const [docOptions, setDocOptions] = useState<DocOption[]>([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [total, setTotal] = useState(0)
  const [selectedType, setSelectedType] = useState<number | undefined>(undefined)
  const [selectedMainCategory, setSelectedMainCategory] = useState<string | undefined>(undefined)
  const [selectedSubCategory, setSelectedSubCategory] = useState<string | undefined>(undefined)
  const [selectedDoc, setSelectedDoc] = useState<string | undefined>(undefined)
  const [previewMaterial, setPreviewMaterial] = useState<Material | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editingTitle, setEditingTitle] = useState('')
  const [editingDescription, setEditingDescription] = useState('')
  const [editingDoc, setEditingDoc] = useState('')
  const [editingMainCategory, setEditingMainCategory] = useState('')
  const [editingSubCategory, setEditingSubCategory] = useState('')
  const pageSize = 20

  const loadMaterials = async (params: MaterialQueryParams = {}) => {
    setLoading(true)
    try {
      console.log('开始加载素材...', {
        page: currentPage,
        pageSize,
        type: selectedType,
        main_category: selectedMainCategory,
        sub_category: selectedSubCategory,
        doc: selectedDoc,
        ...params
      })
      const result = await queryMaterials({
        page: currentPage,
        pageSize,
        type: selectedType,
        main_category: selectedMainCategory,
        sub_category: selectedSubCategory,
        doc: selectedDoc,
        ...params
      })
      console.log('加载素材成功:', result)
      setMaterials(result.materials)
      setTotalPages(result.totalPages)
      setTotal(result.total)
    } catch (error) {
      console.error('加载素材失败:', error)
      // 显示错误提示
      alert(`加载素材失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setLoading(false)
    }
  }

  const loadMaterialTypes = async () => {
    try {
      console.log('开始加载素材类型...')
      const types = await getMaterialTypes()
      console.log('加载素材类型成功:', types)
      setMaterialTypes(types)
    } catch (error) {
      console.error('加载素材类型失败:', error)
      alert(`加载素材类型失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const loadMainCategories = async () => {
    try {
      const categories = await getMainCategories()
      setMainCategories(categories)
    } catch (error) {
      console.error('加载主分类失败:', error)
    }
  }

  const loadSubCategories = async (mainCategory?: string) => {
    try {
      const categories = await getSubCategories(mainCategory)
      setSubCategories(categories)
    } catch (error) {
      console.error('加载子分类失败:', error)
      setSubCategories([])
    }
  }

  const loadDocOptions = async () => {
    try {
      const options = await getDocOptions()
      setDocOptions(options)
    } catch (error) {
      console.error('加载Doc选项失败:', error)
    }
  }

  useEffect(() => {
    loadMaterials({
      page: currentPage,
      type: selectedType,
      main_category: selectedMainCategory,
      sub_category: selectedSubCategory,
      doc: selectedDoc
    })
  }, [currentPage, selectedType, selectedMainCategory, selectedSubCategory, selectedDoc])

  useEffect(() => {
    loadMaterialTypes()
    loadMainCategories()
    loadDocOptions()
  }, [])

  useEffect(() => {
    if (selectedMainCategory) {
      loadSubCategories(selectedMainCategory)
    } else {
      loadSubCategories()
    }
  }, [selectedMainCategory])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleTypeChange = (type: number | undefined) => {
    setSelectedType(type)
    setCurrentPage(1)
  }

  const handleMainCategoryChange = (mainCategory: string | undefined) => {
    setSelectedMainCategory(mainCategory)
    setSelectedSubCategory(undefined) // 清空子分类选择
    setCurrentPage(1)
  }

  const handleSubCategoryChange = (subCategory: string | undefined) => {
    setSelectedSubCategory(subCategory)
    setCurrentPage(1)
  }

  const handleDocChange = (doc: string | undefined) => {
    setSelectedDoc(doc)
    setCurrentPage(1)
  }


  const getObjectIdDate = (objectId: string) => {
    try {
      if (objectId && objectId.length === 24) {
        const timestamp = parseInt(objectId.substring(0, 8), 16) * 1000
        return new Date(timestamp).toLocaleString('zh-CN')
      }
      return '未知'
    } catch (error) {
      return '未知'
    }
  }

  const handlePreview = (material: Material) => {
    setPreviewMaterial(material)
    setIsPreviewOpen(true)
  }

  const handleClosePreview = () => {
    setIsPreviewOpen(false)
    setPreviewMaterial(null)
  }

  const handleEdit = async (material: Material) => {
    setEditingId(material.id)
    setEditingTitle(material.title)
    setEditingDescription(material.description)
    setEditingDoc(material.doc)
    setEditingMainCategory(material.main_category)
    setEditingSubCategory(material.sub_category)

    // 如果有主分类，加载对应的子分类选项
    if (material.main_category) {
      try {
        const subCats = await getSubCategories(material.main_category)
        setSubCategories(subCats)
      } catch (error) {
        console.error('加载子分类失败:', error)
      }
    }
  }

  const handleCancel = () => {
    setEditingId(null)
    setEditingTitle('')
    setEditingDescription('')
    setEditingDoc('')
    setEditingMainCategory('')
    setEditingSubCategory('')
  }

  const handleSave = async (id: string) => {
    const materialTitle = editingTitle || '素材'

    // 创建保存操作的Promise
    const savePromise = updateMaterial(id, {
      title: editingTitle,
      description: editingDescription,
      doc: editingDoc,
      main_category: editingMainCategory,
      sub_category: editingSubCategory
    }).then(() => {
      // 更新本地状态
      setMaterials((prevMaterials) =>
        prevMaterials.map((material) =>
          material.id === id
            ? {
              ...material,
              title: editingTitle,
              description: editingDescription,
              doc: editingDoc,
              main_category: editingMainCategory,
              sub_category: editingSubCategory
            }
            : material
        )
      )
      setEditingId(null)
      setEditingTitle('')
      setEditingDescription('')
      setEditingDoc('')
      setEditingMainCategory('')
      setEditingSubCategory('')
    })

    // 使用toast.promise显示保存进度
    toast.promise(
      savePromise,
      {
        pending: `正在保存素材 "${materialTitle}"...`,
        success: `素材 "${materialTitle}" 保存成功`,
        error: `保存素材 "${materialTitle}" 失败`
      }
    )
  }


  const handleDelete = async (id: string, title: string) => {
    if (!confirm(`确定要删除素材"${title}"吗？\n注意：这将同时删除ElasticSearch和MongoDB中的数据，操作不可撤销！`)) {
      return
    }

    // 创建删除操作的Promise
    const deletePromise = deleteMaterial(id).then(async () => {
      // 从本地状态中移除已删除的素材
      setMaterials((prevMaterials) => prevMaterials.filter((material) => material.id !== id))

      // 同步更新总数
      setTotal((prevTotal) => prevTotal - 1)

      // 刷新所有统计数据
      await Promise.all([
        loadMaterialTypes(),
        loadMainCategories(),
        loadDocOptions(),
        // 如果当前选择了主分类，需要重新加载子分类数据
        selectedMainCategory ? loadSubCategories(selectedMainCategory) : loadSubCategories()
      ])
    })

    // 使用toast.promise显示删除进度
    toast.promise(
      deletePromise,
      {
        pending: `正在删除素材 "${title}"...`,
        success: `素材 "${title}" 删除成功`,
        error: `删除素材 "${title}" 失败`
      }
    )
  }

  const handleToggleEnable = async (id: string, currentEnable: boolean, title: string) => {
    const newEnable = !currentEnable
    const action = newEnable ? '启用' : '禁用'

    // 创建启用/禁用操作的Promise
    const togglePromise = toggleMaterialEnable(id, newEnable).then(() => {
      // 更新本地状态
      setMaterials((prevMaterials) =>
        prevMaterials.map((material) =>
          material.id === id
            ? { ...material, enable: newEnable }
            : material
        )
      )
    })

    // 使用toast.promise显示操作进度
    toast.promise(
      togglePromise,
      {
        pending: `正在${action}素材 "${title}"...`,
        success: `素材 "${title}" ${action}成功`,
        error: `${action}素材 "${title}" 失败`
      }
    )
  }


  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">素材管理</h1>

      {/* 筛选条件 */}
      <div className="mb-6 space-y-4">
        {/* 类型筛选 */}
        <div>
          <label className="label">
            <span className="label-text font-medium">素材类型</span>
          </label>
          <div className="flex flex-wrap gap-2">
            <button
              className={`btn btn-sm ${selectedType === undefined ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => handleTypeChange(undefined)}
            >
              全部 ({total})
            </button>
            {materialTypes.map((typeInfo) => (
              <button
                key={typeInfo.type}
                className={`btn btn-sm ${selectedType === typeInfo.type ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleTypeChange(typeInfo.type)}
              >
                {MATERIAL_TYPES[typeInfo.type as keyof typeof MATERIAL_TYPES] || `类型${typeInfo.type}`} ({typeInfo.count})
              </button>
            ))}
          </div>
        </div>

        {/* 主分类筛选 */}
        <div>
          <label className="label">
            <span className="label-text font-medium">主分类</span>
          </label>
          <div className="flex flex-wrap gap-2">
            <button
              className={`btn btn-sm ${selectedMainCategory === undefined ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => handleMainCategoryChange(undefined)}
            >
              全部主分类
            </button>
            {mainCategories.map((category) => (
              <button
                key={category.main_category}
                className={`btn btn-sm ${selectedMainCategory === category.main_category ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleMainCategoryChange(category.main_category)}
              >
                {category.main_category} ({category.count})
              </button>
            ))}
          </div>
        </div>

        {/* 子分类筛选 */}
        {subCategories.length > 0 && (
          <div>
            <label className="label">
              <span className="label-text font-medium">子分类</span>
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                className={`btn btn-sm ${selectedSubCategory === undefined ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleSubCategoryChange(undefined)}
              >
                全部子分类
              </button>
              {subCategories.map((category) => (
                <button
                  key={category.sub_category}
                  className={`btn btn-sm ${selectedSubCategory === category.sub_category ? 'btn-primary' : 'btn-outline'}`}
                  onClick={() => handleSubCategoryChange(category.sub_category)}
                >
                  {category.sub_category} ({category.count})
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Doc筛选 */}
        {docOptions.length > 0 && (
          <div>
            <label className="label">
              <span className="label-text font-medium">Doc选项</span>
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                className={`btn btn-sm ${selectedDoc === undefined ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleDocChange(undefined)}
              >
                全部Doc
              </button>
              {docOptions.map((option) => (
                <button
                  key={option.doc}
                  className={`btn btn-sm ${selectedDoc === option.doc ? 'btn-primary' : 'btn-outline'}`}
                  onClick={() => handleDocChange(option.doc)}
                >
                  {option.doc || '未设置'} ({option.count})
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="flex justify-center py-8">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      )}

      {/* 素材列表 */}
      {!loading && (
        <div className="overflow-x-auto">
          <table className="table table-zebra w-full">
            <thead>
              <tr>
                <th>预览</th>
                <th>标题</th>
                <th>描述</th>
                <th>类型</th>
                <th>主分类</th>
                <th>子分类</th>
                <th>Doc</th>
                <th>状态</th>
                <th>上传时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {materials.map((material) => (
                <tr key={material.id}>
                  <td>
                    <div className="w-16 h-16 flex items-center justify-center">
                      {getPreviewContent(material, handlePreview) || (
                        <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center cursor-pointer"
                          onClick={() => handlePreview(material)}>
                          <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </td>
                  <td>
                    {editingId === material.id ? (
                      <input
                        type="text"
                        className="input input-sm w-full"
                        value={editingTitle}
                        onChange={(e) => setEditingTitle(e.target.value)}
                        placeholder="标题"
                      />
                    ) : (
                      <div className="font-medium cursor-pointer hover:text-primary" onClick={() => handlePreview(material)}>
                        {material.title || '无标题'}
                      </div>
                    )}
                  </td>
                  <td>
                    {editingId === material.id ? (
                      <textarea
                        className="textarea textarea-sm w-full max-w-xs"
                        value={editingDescription}
                        onChange={(e) => setEditingDescription(e.target.value)}
                        placeholder="描述"
                        rows={2}
                      />
                    ) : (
                      <div className="max-w-xs truncate">
                        {material.description || '无描述'}
                      </div>
                    )}
                  </td>
                  <td>
                    <span className="badge badge-outline">
                      {MATERIAL_TYPES[material.type as keyof typeof MATERIAL_TYPES] || `类型${material.type}`}
                    </span>
                  </td>
                  <td>
                    {editingId === material.id ? (
                      <select
                        className="select select-sm select-bordered w-full max-w-xs"
                        value={editingMainCategory}
                        onChange={(e) => {
                          setEditingMainCategory(e.target.value)
                          // 主分类改变时，清空子分类并重新加载子分类选项
                          setEditingSubCategory('')
                          if (e.target.value) {
                            getSubCategories(e.target.value).then(setSubCategories)
                          }
                        }}
                      >
                        <option value="">请选择主分类</option>
                        {mainCategories.map((category) => (
                          <option key={category.main_category} value={category.main_category}>
                            {category.main_category}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <span className="badge badge-secondary">
                        {material.main_category || '未分类'}
                      </span>
                    )}
                  </td>
                  <td>
                    {editingId === material.id ? (
                      <select
                        className="select select-sm select-bordered w-full max-w-xs"
                        value={editingSubCategory}
                        onChange={(e) => setEditingSubCategory(e.target.value)}
                        disabled={!editingMainCategory}
                      >
                        <option value="">请选择子分类</option>
                        {subCategories
                          .filter((category) => !editingMainCategory || category.sub_category)
                          .map((category) => (
                            <option key={category.sub_category} value={category.sub_category}>
                              {category.sub_category}
                            </option>
                          ))}
                      </select>
                    ) : (
                      <span className="badge badge-accent">
                        {material.sub_category || '未分类'}
                      </span>
                    )}
                  </td>
                  <td>
                    {editingId === material.id ? (
                      <select
                        className="select select-sm select-bordered w-full max-w-xs"
                        value={editingDoc}
                        onChange={(e) => setEditingDoc(e.target.value)}
                      >
                        <option value="">请选择Doc</option>
                        {DOC_OPTIONS.map((option) => (
                          <option key={option} value={option}>
                            {option}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <span className="badge badge-info">
                        {material.doc || '未设置'}
                      </span>
                    )}
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <span className={`badge ${material.enable ? 'badge-success' : 'badge-ghost'}`}>
                        {material.enable ? '已启用' : '已禁用'}
                      </span>
                      <button
                        className={`btn btn-xs ${material.enable ? 'btn-warning' : 'btn-success'}`}
                        onClick={() => handleToggleEnable(material.id, material.enable, material.title || '未知素材')}
                        title={material.enable ? '点击禁用素材' : '点击启用素材'}
                      >
                        {material.enable ? '禁用' : '启用'}
                      </button>
                    </div>
                  </td>
                  <td>
                    <span className="text-sm text-gray-500">
                      {getObjectIdDate(material.id)}
                    </span>
                  </td>
                  <td>
                    <div className="flex gap-2">
                      {editingId === material.id ? (
                        <>
                          <button
                            className="btn btn-xs btn-success"
                            onClick={() => handleSave(material.id)}
                          >
                            保存
                          </button>
                          <button
                            className="btn btn-xs btn-outline"
                            onClick={handleCancel}
                          >
                            取消
                          </button>
                        </>
                      ) : (
                        <>
                          <button
                            className="btn btn-xs btn-outline btn-primary"
                            onClick={() => handlePreview(material)}
                          >
                            预览
                          </button>
                          <button
                            className="btn btn-xs btn-outline btn-secondary"
                            onClick={() => handleEdit(material)}
                          >
                            编辑
                          </button>
                          <button
                            className="btn btn-xs btn-outline btn-error"
                            onClick={() => handleDelete(material.id, material.title || '未知素材')}
                          >
                            删除
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {materials.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              暂无素材数据
            </div>
          )}
        </div>
      )}

      {/* 分页 */}
      <AdvancedPagination
        currentPage={currentPage}
        totalPages={totalPages}
        total={total}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        showQuickJumper={true}
        showPageSizeSelector={false}
      />

      {/* 预览模态框 */}
      <MaterialPreview
        material={previewMaterial}
        isOpen={isPreviewOpen}
        onClose={handleClosePreview}
        renderPreviewContent={renderPreviewContent}
        MATERIAL_TYPES={MATERIAL_TYPES}
      />
    </div>
  )
}