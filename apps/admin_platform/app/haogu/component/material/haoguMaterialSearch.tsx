'use client'

import { useEffect, useState } from 'react'
import { searchHaoGuMaterials, importMaterialsFromHaoGu, getSubCategories, checkMaterialsExistence } from '@/app/haogu/api/material'
import { ContentItem, ContentItemMediaType, ContentItemType, ContentListRes } from 'model/haogu/crm/type'
import AdvancedPagination from '../common/AdvancedPagination'
import { toast } from 'react-toastify'

const CONTENT_TYPES = {
  [ContentItemType.Article]: '文章',
  [ContentItemType.File]: '文件',
  [ContentItemType.Link]: '链接',
  [ContentItemType.Poster]: '海报',
  [ContentItemType.Media]: '多媒体',
  [ContentItemType.Channels]: '视频号',
  [ContentItemType.Text]: '文本'
}

const MEDIA_TYPES = {
  [ContentItemMediaType.Image]: '图片',
  [ContentItemMediaType.Audio]: '音频',
  [ContentItemMediaType.Video]: '视频',
  [ContentItemMediaType.Voice]: '语音'
}

export const MAIN_CATEGORY = ['公司相关', '6天课程相关', '交易体系相关', '工具相关', '3360实战班相关', '下单相关', '客户案例相关', '其他']

const DOC_OPTIONS = ['全局', '售前', '售中', '售后']

const formatCreateTime = (createTime?: number) => {
  if (!createTime) return '-'
  const date = new Date(createTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function HaoGuMaterialSearch() {
  const [materials, setMaterials] = useState<ContentItem[]>([])
  const [loading, setLoading] = useState(false)
  const [importing, setImporting] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [total, setTotal] = useState(0)
  const [selectedType, setSelectedType] = useState<ContentItemType>(ContentItemType.Article)
  const [selectedMediaType, setSelectedMediaType] = useState<ContentItemMediaType | undefined>(undefined)
  const [selectedMaterials, setSelectedMaterials] = useState<Set<string>>(new Set())
  const [mainCategory, setMainCategory] = useState<string>('')
  const [subCategory, setSubCategory] = useState<string>('')
  const [selectedDoc, setSelectedDoc] = useState<string>('')
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importEnabled, setImportEnabled] = useState(false)
  const [subCategories, setSubCategories] = useState<Array<{ sub_category: string; count: number }>>([])
  const [loadingSubCategories, setLoadingSubCategories] = useState(false)
  const [previewMaterial, setPreviewMaterial] = useState<ContentItem | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [materialExistence, setMaterialExistence] = useState<Record<string, boolean>>({})
  const [checkingExistence, setCheckingExistence] = useState(false)
  const pageSize = 20

  const loadMaterials = async () => {
    setLoading(true)
    try {
      const result = await searchHaoGuMaterials(currentPage, pageSize, selectedType, selectedMediaType) as ContentListRes
      console.log(result)
      setMaterials(result.records)
      setTotalPages(result.pages)
      setTotal(result.total)

      // 检查素材是否已存在于本地
      await checkMaterialsExistenceStatus(result.records)
    } catch (error) {
      toast.error(`搜索素材失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setLoading(false)
    }
  }

  const checkMaterialsExistenceStatus = async (materials: ContentItem[]) => {
    if (materials.length === 0) return

    setCheckingExistence(true)
    try {
      const sourceIds = materials.map((material) => material.id.toString())
      const existence = await checkMaterialsExistence(sourceIds)
      setMaterialExistence(existence)
    } catch (error) {
      console.error('检查素材存在状态失败:', error)
      setMaterialExistence({})
    } finally {
      setCheckingExistence(false)
    }
  }

  useEffect(() => {
    loadMaterials()
  }, [currentPage, selectedType, selectedMediaType])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleTypeChange = (type: ContentItemType) => {
    setSelectedType(type)
    setSelectedMediaType(undefined)
    setCurrentPage(1)
    setSelectedMaterials(new Set())
  }

  const handleMediaTypeChange = (mediaType: ContentItemMediaType | undefined) => {
    setSelectedMediaType(mediaType)
    setCurrentPage(1)
    setSelectedMaterials(new Set())
  }

  const handleSelectMaterial = (materialId: string) => {
    const newSelected = new Set(selectedMaterials)
    if (newSelected.has(materialId)) {
      newSelected.delete(materialId)
    } else {
      newSelected.add(materialId)
    }
    setSelectedMaterials(newSelected)
  }

  const handleSelectAll = () => {
    // 只选择未导入的素材
    const selectableMaterials = materials.filter((m) => !materialExistence[m.id.toString()])
    const selectableIds = selectableMaterials.map((m) => m.id.toString())

    if (selectedMaterials.size === selectableIds.length) {
      setSelectedMaterials(new Set())
    } else {
      setSelectedMaterials(new Set(selectableIds))
    }
  }

  const handleImportClick = () => {
    if (selectedMaterials.size === 0) {
      alert('请先选择要导入的素材')
      return
    }
    setShowImportDialog(true)
  }

  const handleImportConfirm = async () => {
    if (!mainCategory) {
      alert('请选择主分类')
      return
    }
    if (!subCategory) {
      alert('请输入子分类')
      return
    }
    if (!selectedDoc) {
      alert('请选择doc选项')
      return
    }

    setImporting(true)
    try {
      const selectedItems = materials.filter((m) => selectedMaterials.has(m.id.toString()))
      const results = await importMaterialsFromHaoGu(mainCategory, subCategory, selectedDoc, selectedItems)

      const importedCount = results.filter((r) => r.status === 'imported').length
      const existingCount = results.filter((r) => r.status === 'already_exists').length

      alert(`导入完成！新导入 ${importedCount} 个素材，${existingCount} 个素材已存在`)
      setSelectedMaterials(new Set())
      setShowImportDialog(false)
      setMainCategory('')
      setSubCategory('')
      setSelectedDoc('')
      setImportEnabled(false)
      setSubCategories([])

      // 更新素材存在状态
      const updatedExistence = { ...materialExistence }
      selectedItems.forEach((item) => {
        updatedExistence[item.id.toString()] = true
      })
      setMaterialExistence(updatedExistence)
    } catch (error) {
      toast.error(`导入素材失败:${error}`)
      alert(`导入素材失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setImporting(false)
    }
  }

  const handleDialogClose = () => {
    setShowImportDialog(false)
    setMainCategory('')
    setSubCategory('')
    setSelectedDoc('')
    setImportEnabled(false)
    setSubCategories([])
  }

  const loadSubCategories = async (mainCategory?: string) => {
    setLoadingSubCategories(true)
    try {
      const result = await getSubCategories(mainCategory)
      setSubCategories(result)
    } catch (error) {
      console.error('加载子分类失败:', error)
      setSubCategories([])
    } finally {
      setLoadingSubCategories(false)
    }
  }

  const handleMainCategoryChange = async (category: string) => {
    setMainCategory(category)
    setSubCategory('') // 清空子分类选择
    updateImportEnabled(category, '', selectedDoc)
    await loadSubCategories(category) // 加载对应主分类的子分类
  }

  const handleSubCategoryChange = (category: string) => {
    setSubCategory(category)
    updateImportEnabled(mainCategory, category, selectedDoc)
  }

  const handleDocChange = (doc: string) => {
    setSelectedDoc(doc)
    updateImportEnabled(mainCategory, subCategory, doc)
  }

  const updateImportEnabled = (main: string, sub: string, doc: string) => {
    setImportEnabled(main !== '' && sub !== '' && doc !== '')
  }

  const handlePreview = (material: ContentItem) => {
    setPreviewMaterial(material)
    setIsPreviewOpen(true)
  }

  const handleClosePreview = () => {
    setIsPreviewOpen(false)
    setPreviewMaterial(null)
  }

  const getPreviewContent = (material: ContentItem) => {
    const materialData = material as any

    switch (material.type) {
      case ContentItemType.Media:
        if (material.media_type === ContentItemMediaType.Image) {
          // 使用link_url作为图片地址
          const imageUrl = materialData.link_url || material.img
          if (imageUrl) {
            return (
              <img
                src={imageUrl}
                alt="预览"
                className="w-16 h-16 object-cover rounded cursor-pointer hover:opacity-80"
                onClick={() => handlePreview(material)}
              />
            )
          }
        }
        if (material.media_type === ContentItemMediaType.Video) {
          // 视频使用video_img或img作为封面
          const videoImg = materialData.video_img || material.img
          if (videoImg) {
            return (
              <div className="relative cursor-pointer hover:opacity-80" onClick={() => handlePreview(material)}>
                <img
                  src={videoImg}
                  alt="视频封面"
                  className="w-16 h-16 object-cover rounded"
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8 5v10l8-5-8-5z"/>
                  </svg>
                </div>
              </div>
            )
          }
        }
        if (material.media_type === ContentItemMediaType.Voice) {
          // 语音显示音频图标
          const audioUrl = materialData.link_url
          if (audioUrl) {
            return (
              <div className="w-16 h-16 bg-blue-50 rounded flex items-center justify-center cursor-pointer hover:bg-blue-100" onClick={() => handlePreview(material)}>
                <svg className="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.816L4.518 14H2a1 1 0 01-1-1V7a1 1 0 011-1h2.518l3.865-2.816a1 1 0 011.617.816zM6 8.5L4.5 7H3v6h1.5L6 11.5v-3z" clipRule="evenodd" />
                  <path d="M12.293 7.293a1 1 0 011.414 0A5.5 5.5 0 0116 12a5.5 5.5 0 01-2.293 4.707 1 1 0 11-1.414-1.414A3.5 3.5 0 0014 12a3.5 3.5 0 00-1.707-3.293 1 1 0 010-1.414z" />
                </svg>
              </div>
            )
          }
        }
        if (material.media_type === ContentItemMediaType.Audio) {
          // 音频显示音频图标（与语音稍有区别的颜色）
          const audioUrl = materialData.link_url
          if (audioUrl) {
            return (
              <div className="w-16 h-16 bg-green-50 rounded flex items-center justify-center cursor-pointer hover:bg-green-100" onClick={() => handlePreview(material)}>
                <svg className="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.816L4.518 14H2a1 1 0 01-1-1V7a1 1 0 011-1h2.518l3.865-2.816a1 1 0 011.617.816zM6 8.5L4.5 7H3v6h1.5L6 11.5v-3z" clipRule="evenodd" />
                  <path d="M12.293 7.293a1 1 0 011.414 0A5.5 5.5 0 0116 12a5.5 5.5 0 01-2.293 4.707 1 1 0 11-1.414-1.414A3.5 3.5 0 0014 12a3.5 3.5 0 00-1.707-3.293 1 1 0 010-1.414z" />
                </svg>
              </div>
            )
          }
        }
        break
      default:
        return (
          <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center cursor-pointer hover:bg-gray-200" onClick={() => handlePreview(material)}>
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        )
    }
    return null
  }

  const getTypeDisplay = (material: ContentItem) => {
    const typeText = CONTENT_TYPES[material.type || ContentItemType.Text] || `类型${material.type}`
    if (material.type === ContentItemType.Media && material.media_type) {
      const mediaText = MEDIA_TYPES[material.media_type] || `媒体${material.media_type}`
      return `${typeText}-${mediaText}`
    }
    return typeText
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">HaoGu素材搜索</h1>

      {/* 类型筛选 */}
      <div className="mb-6">
        <div className="mb-4">
          <label className="label">
            <span className="label-text">素材类型</span>
          </label>
          <div className="flex flex-wrap gap-2">
            {Object.entries(CONTENT_TYPES).map(([typeValue, typeName]) => (
              <button
                key={typeValue}
                className={`btn btn-sm ${selectedType === Number(typeValue) ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleTypeChange(Number(typeValue) as ContentItemType)}
              >
                {typeName}
              </button>
            ))}
          </div>
        </div>

        {/* 多媒体子类型筛选 */}
        {selectedType === ContentItemType.Media && (
          <div className="mb-4">
            <label className="label">
              <span className="label-text">媒体类型</span>
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                className={`btn btn-sm ${selectedMediaType === undefined ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleMediaTypeChange(undefined)}
              >
                全部
              </button>
              {Object.entries(MEDIA_TYPES).map(([mediaValue, mediaName]) => (
                <button
                  key={mediaValue}
                  className={`btn btn-sm ${selectedMediaType === Number(mediaValue) ? 'btn-primary' : 'btn-outline'}`}
                  onClick={() => handleMediaTypeChange(Number(mediaValue) as ContentItemMediaType)}
                >
                  {mediaName}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 批量操作 */}
      <div className="mb-4 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <label className="cursor-pointer label">
            <input
              type="checkbox"
              className="checkbox"
              checked={selectedMaterials.size > 0 && selectedMaterials.size === materials.filter((m) => !materialExistence[m.id.toString()]).length}
              onChange={handleSelectAll}
            />
            <span className="label-text ml-2">全选（仅未导入）</span>
          </label>
          <div className="text-sm text-gray-500">
            <div>已选择 {selectedMaterials.size} 个素材</div>
            <div className="text-xs">
              已导入: {Object.values(materialExistence).filter(Boolean).length} /
              未导入: {Object.values(materialExistence).filter((exists) => !exists).length}
            </div>
          </div>
        </div>
        <button
          className="btn btn-primary"
          onClick={handleImportClick}
          disabled={selectedMaterials.size === 0 || importing}
        >
          {importing ? (
            <>
              <span className="loading loading-spinner loading-sm"></span>
              导入中...
            </>
          ) : (
            `导入选中素材 (${selectedMaterials.size})`
          )}
        </button>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="flex justify-center py-8">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      )}

      {/* 素材列表 */}
      {!loading && (
        <div className="overflow-x-auto">
          <table className="table table-zebra w-full">
            <thead>
              <tr>
                <th>选择</th>
                <th>预览</th>
                <th>标题</th>
                <th>描述</th>
                <th>类型</th>
                <th>创建时间</th>
                <th>状态</th>
                <th>ID</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {materials.map((material) => (
                <tr key={material.id}>
                  <td>
                    <input
                      type="checkbox"
                      className={`checkbox ${materialExistence[material.id.toString()] ? 'checkbox-disabled opacity-50' : ''}`}
                      checked={selectedMaterials.has(material.id.toString())}
                      onChange={() => handleSelectMaterial(material.id.toString())}
                      disabled={materialExistence[material.id.toString()]}
                      title={materialExistence[material.id.toString()] ? '该素材已存在于本地库中' : ''}
                    />
                  </td>
                  <td>
                    <div className="w-16 h-16 flex items-center justify-center">
                      {getPreviewContent(material)}
                    </div>
                  </td>
                  <td>
                    <div className="font-medium">
                      {material.title || '无标题'}
                    </div>
                  </td>
                  <td>
                    <div className="max-w-xs truncate">
                      {material.desc || material.sphfeed_desc || material.content || '无描述'}
                    </div>
                  </td>
                  <td>
                    <span className="badge badge-outline">
                      {getTypeDisplay(material)}
                    </span>
                  </td>
                  <td>
                    <span className="text-sm text-gray-500">
                      {formatCreateTime(material.create_time)}
                    </span>
                  </td>
                  <td>
                    {checkingExistence ? (
                      <span className="loading loading-spinner loading-xs"></span>
                    ) : materialExistence[material.id.toString()] ? (
                      <span className="badge badge-success">已导入</span>
                    ) : (
                      <span className="badge badge-ghost">未导入</span>
                    )}
                  </td>
                  <td>
                    <span className="text-sm text-gray-500">
                      {material.id}
                    </span>
                  </td>
                  <td>
                    <button
                      className="btn btn-xs btn-outline btn-primary"
                      onClick={() => handlePreview(material)}
                    >
                      详细预览
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {materials.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              暂无素材数据
            </div>
          )}
        </div>
      )}

      {/* 分页 */}
      <AdvancedPagination
        currentPage={currentPage}
        totalPages={totalPages}
        total={total}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        showQuickJumper={true}
        showPageSizeSelector={false}
      />

      {/* 导入素材对话框 */}
      {showImportDialog && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg mb-4">导入素材设置</h3>
            {/* 主分类选择 */}
            <div className="form-control mb-4">
              <label className="label">
                <span className="label-text">主分类 *</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                {MAIN_CATEGORY.map((category) => (
                  <label key={category} className="cursor-pointer">
                    <input
                      type="radio"
                      name="mainCategory"
                      value={category}
                      checked={mainCategory === category}
                      onChange={(e) => handleMainCategoryChange(e.target.value)}
                      className="radio radio-primary mr-2"
                    />
                    <span className="text-sm">{category}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* 子分类选择 */}
            <div className="form-control mb-4">
              <label className="label">
                <span className="label-text">子分类 *</span>
              </label>
              {/* 已有子分类 */}
              {subCategories.length > 0 && (
                <div className="mb-2">
                  <div className="text-xs text-gray-500 mb-1">
                    已有分类 {loadingSubCategories && <span className="loading loading-spinner loading-xs"></span>}:
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {subCategories.map((item) => (
                      <button
                        key={item.sub_category}
                        type="button"
                        className={`btn btn-xs ${subCategory === item.sub_category ? 'btn-primary' : 'btn-outline'}`}
                        onClick={() => handleSubCategoryChange(item.sub_category)}
                        title={`使用次数: ${item.count}`}
                      >
                        {item.sub_category}
                        <span className="badge badge-xs ml-1">{item.count}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* 自定义输入 */}
              <input
                type="text"
                placeholder="或输入自定义子分类"
                className="input input-bordered w-full"
                value={subCategory}
                onChange={(e) => handleSubCategoryChange(e.target.value)}
              />
            </div>

            {/* Doc选项选择 */}
            <div className="form-control mb-4">
              <label className="label">
                <span className="label-text">Doc选项 *</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                {DOC_OPTIONS.map((doc) => (
                  <label key={doc} className="cursor-pointer">
                    <input
                      type="radio"
                      name="docOption"
                      value={doc}
                      checked={selectedDoc === doc}
                      onChange={(e) => handleDocChange(e.target.value)}
                      className="radio radio-primary mr-2"
                    />
                    <span className="text-sm">{doc}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="modal-action">
              <button
                className="btn btn-ghost"
                onClick={handleDialogClose}
                disabled={importing}
              >
                取消
              </button>
              <button
                className={`btn btn-primary ${!importEnabled ? 'btn-disabled' : ''}`}
                onClick={handleImportConfirm}
                disabled={!importEnabled || importing}
              >
                {importing ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    导入中...
                  </>
                ) : (
                  `导入 ${selectedMaterials.size} 个素材`
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 素材预览对话框 */}
      {isPreviewOpen && previewMaterial && (
        <div className="modal modal-open">
          <div className="modal-box w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="font-bold text-lg">
                素材预览 - {getTypeDisplay(previewMaterial)}
              </h2>
              <button
                className="btn btn-sm btn-circle btn-ghost"
                onClick={handleClosePreview}
              >
                ✕
              </button>
            </div>

            <div className="mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <span className="font-medium">标题：</span>
                  <span className="ml-2">{previewMaterial.title || '无标题'}</span>
                </div>
                <div>
                  <span className="font-medium">ID：</span>
                  <span className="ml-2">{previewMaterial.id}</span>
                </div>
              </div>

              {previewMaterial.desc && (
                <div className="mb-4">
                  <span className="font-medium">描述：</span>
                  <div className="ml-2 mt-1">{previewMaterial.desc}</div>
                </div>
              )}

              {previewMaterial.sphfeed_desc && previewMaterial.sphfeed_desc !== previewMaterial.desc && (
                <div className="mb-4">
                  <span className="font-medium">视频号描述：</span>
                  <div className="ml-2 mt-1">{previewMaterial.sphfeed_desc}</div>
                </div>
              )}
            </div>

            <div className="mb-6">
              {previewMaterial.type === ContentItemType.Media && previewMaterial.media_type === ContentItemMediaType.Image && (
                <div className="bg-base-100 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">图片预览：</h3>
                  <div className="flex justify-center">
                    <img
                      src={(previewMaterial as any).link_url || previewMaterial.img}
                      alt={previewMaterial.title || '图片'}
                      className="max-w-full max-h-96 object-contain rounded"
                      onError={(e) => {
                        // 如果link_url失败，尝试使用img字段
                        if ((e.target as HTMLImageElement).src !== previewMaterial.img) {
                          (e.target as HTMLImageElement).src = previewMaterial.img || ''
                        }
                      }}
                    />
                  </div>
                  {previewMaterial.image_width && previewMaterial.image_height && (
                    <p className="text-sm text-gray-500 mt-2 text-center">
                      尺寸: {previewMaterial.image_width} × {previewMaterial.image_height}
                    </p>
                  )}
                </div>
              )}

              {previewMaterial.type === ContentItemType.Media && previewMaterial.media_type === ContentItemMediaType.Video && (
                <div className="bg-base-100 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">视频预览：</h3>
                  {previewMaterial.img && (
                    <div className="mb-4 flex justify-center">
                      <img
                        src={previewMaterial.img}
                        alt="视频封面"
                        className="max-w-full max-h-48 object-contain rounded"
                      />
                    </div>
                  )}
                  {previewMaterial.voice_time && (
                    <p className="text-sm text-gray-500">
                      时长: {Math.floor(previewMaterial.voice_time / 60)}:{(previewMaterial.voice_time % 60).toString().padStart(2, '0')}
                    </p>
                  )}
                </div>
              )}

              {previewMaterial.type === ContentItemType.Media && previewMaterial.media_type === ContentItemMediaType.Voice && (
                <div className="bg-base-100 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">语音预览：</h3>
                  {(previewMaterial as any).link_url ? (
                    <div>
                      <audio controls className="w-full mb-2">
                        <source src={(previewMaterial as any).link_url} type="audio/mpeg" />
                        <source src={(previewMaterial as any).link_url} type="audio/wav" />
                        <source src={(previewMaterial as any).link_url} type="audio/mp3" />
                        您的浏览器不支持音频播放
                      </audio>
                      {previewMaterial.voice_time && (
                        <p className="text-sm text-gray-500">
                          语音时长: {previewMaterial.voice_time}秒
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500">无语音内容</p>
                  )}
                </div>
              )}

              {previewMaterial.type === ContentItemType.Media && previewMaterial.media_type === ContentItemMediaType.Audio && (
                <div className="bg-base-100 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">音频预览：</h3>
                  {(previewMaterial as any).link_url ? (
                    <div>
                      <audio controls className="w-full mb-2">
                        <source src={(previewMaterial as any).link_url} type="audio/mpeg" />
                        <source src={(previewMaterial as any).link_url} type="audio/wav" />
                        <source src={(previewMaterial as any).link_url} type="audio/mp3" />
                        您的浏览器不支持音频播放
                      </audio>
                      {previewMaterial.voice_time && (
                        <p className="text-sm text-gray-500">
                          音频时长: {Math.floor(previewMaterial.voice_time / 60)}:{(previewMaterial.voice_time % 60).toString().padStart(2, '0')}
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500">无音频内容</p>
                  )}
                </div>
              )}

              {previewMaterial.type === ContentItemType.Text && (
                <div className="bg-base-100 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">文本内容：</h3>
                  <div className="whitespace-pre-wrap text-sm">
                    {previewMaterial.content || '无内容'}
                  </div>
                </div>
              )}

              {previewMaterial.type === ContentItemType.Article && (
                <div className="bg-base-100 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">文章预览：</h3>
                  {previewMaterial.h5_Url ? (
                    <div>
                      <p className="mb-2 text-sm text-gray-600">H5链接：</p>
                      <a
                        href={previewMaterial.h5_Url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="link link-primary"
                      >
                        {previewMaterial.h5_Url}
                      </a>
                      <div className="mt-4">
                        <iframe
                          src={previewMaterial.h5_Url}
                          className="w-full h-96 border rounded"
                          title="文章预览"
                        />
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-500">无预览内容</p>
                  )}
                </div>
              )}

              {previewMaterial.type === ContentItemType.File && (
                <div className="bg-base-100 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">文件信息：</h3>
                  <div className="space-y-2">
                    {previewMaterial.h5_Url && (
                      <div>
                        <span className="font-medium">下载链接：</span>
                        <a
                          href={previewMaterial.h5_Url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="link link-primary ml-2"
                        >
                          下载文件
                        </a>
                      </div>
                    )}
                    {previewMaterial.file_size && (
                      <div>
                        <span className="font-medium">文件大小：</span>
                        <span className="ml-2">{(previewMaterial.file_size / 1024 / 1024).toFixed(2)} MB</span>
                      </div>
                    )}
                    {previewMaterial.files_type && (
                      <div>
                        <span className="font-medium">文件类型：</span>
                        <span className="ml-2">
                          {previewMaterial.files_type === 1 ? 'Excel' :
                            previewMaterial.files_type === 2 ? 'PDF' :
                              previewMaterial.files_type === 3 ? 'Word' :
                                previewMaterial.files_type === 4 ? 'PPT' : '未知'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 显示原始数据 */}
              <div className="bg-base-100 p-4 rounded-lg mt-4">
                <h3 className="font-bold mb-2">原始数据：</h3>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-96">
                  {JSON.stringify(previewMaterial, null, 2)}
                </pre>
              </div>
            </div>

            <div className="modal-action">
              <button className="btn" onClick={handleClosePreview}>关闭</button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}