'use client'

import { Material } from '@/app/haogu/api/material'
import { useEffect, useRef } from 'react'

interface MaterialPreviewProps {
  material: Material | null
  isOpen: boolean
  onClose: () => void
  renderPreviewContent: (material: Material) => React.ReactNode
  MATERIAL_TYPES: Record<number, string>
}


export default function MaterialPreview({ material, isOpen, onClose, renderPreviewContent, MATERIAL_TYPES }: MaterialPreviewProps) {
  const dialogRef = useRef<HTMLDialogElement>(null)

  useEffect(() => {
    const dialog = dialogRef.current
    if (!dialog) return

    if (isOpen) {
      dialog.showModal()
    } else {
      dialog.close()
    }
  }, [isOpen])

  if (!material) return null


  return (
    <dialog
      ref={dialogRef}
      className="modal"
      onClick={(e) => {
        if (e.target === dialogRef.current) {
          onClose()
        }
      }}
    >
      <div className="modal-box w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="font-bold text-lg">
            素材预览 - {MATERIAL_TYPES[material.type as keyof typeof MATERIAL_TYPES] || `类型${material.type}`}
          </h2>
          <button
            className="btn btn-sm btn-circle btn-ghost"
            onClick={onClose}
          >
            ✕
          </button>
        </div>

        <div className="mb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <span className="font-medium">标题：</span>
              <span className="ml-2">{material.title || '无标题'}</span>
            </div>
            <div>
              <span className="font-medium">来源ID：</span>
              <span className="ml-2">{material.source_id}</span>
            </div>
          </div>

          {material.description && (
            <div className="mb-4">
              <span className="font-medium">描述：</span>
              <div className="ml-2 mt-1">{material.description}</div>
            </div>
          )}

          {/* 显示原始数据中的标题，如果与数据库标题不同 */}
          {(material.data as any).title && (material.data as any).title !== material.title && (
            <div className="mb-2">
              <span className="font-medium text-sm text-gray-600">原始标题：</span>
              <span className="ml-2 text-sm">{(material.data as any).title}</span>
            </div>
          )}
        </div>

        <div className="mb-6">
          {renderPreviewContent(material)}
        </div>

        <div className="modal-action">
          <button className="btn" onClick={onClose}>关闭</button>
        </div>
      </div>
    </dialog>
  )
}