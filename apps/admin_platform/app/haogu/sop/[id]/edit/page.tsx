'use client'
import { use } from 'react'
import { EditSop } from '@/app/component/sop/editSop'
import { ActionType } from 'service/visualized_sop/visualized_sop_type'
import { getConditionJudgeKeys, getCustomKeys, getLinkSourceVariableTagKeys, getVariableMapKeys, querySopById, updateSop } from '@/app/haogu/api/sop'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = use(params)
  return <EditSop id={param.id}
    getConditionJudgeKeys={getConditionJudgeKeys}
    getCustomKeys={getCustomKeys}
    getLinkSourceVariableTagKeys={getLinkSourceVariableTagKeys}
    getVariableMapKeys={getVariableMapKeys}
    querySopById={querySopById}
    updateSop={updateSop}
    enableActionType={[ActionType.text, ActionType.dynamicPrompt, ActionType.custom, ActionType.material]}
    getYCloudTemplateList={async() => []}
  />
}
