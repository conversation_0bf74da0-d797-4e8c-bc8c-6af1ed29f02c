import { SopTopicShow } from '@/app/component/sop/sopTopicShow'
import { getConditionJudgeKeys } from '@/app/haogu/api/sop'
import { queryAllTags } from '@/app/haogu/api/sop_tag'
import { changeSopTopicEnable, copyTopicToTag, deleteTopic, modifySopTopicCondition, querySopTopicByTag, renameTopic } from '@/app/haogu/api/sop_topic'

export default async function Page({ params }:{
  params: Promise<{ tag: string }>
}) {
  const { tag } = await params
  const decodeTag = decodeURIComponent(tag)
  return <div>
    <SopTopicShow
      tag={decodeTag}
      querySopTopicByTag={querySopTopicByTag}
      changeSopTopicEnable={changeSopTopicEnable}
      getConditionJudgeKeys={getConditionJudgeKeys}
      copyTopicToTag={copyTopicToTag}
      deleteTopic={deleteTopic}
      renameTopic={renameTopic}
      queryAllTags={queryAllTags}
      modifySopTopicCondition={modifySopTopicCondition}
    />
  </div>
}