'use client'

import { useState } from 'react'
import MaterialShow from '../component/material/materialShow'
import HaoGuMaterialSearch from '../component/material/haoguMaterialSearch'
import { Material, queryMaterials, getMaterialTypes, updateMaterial, getMainCategories, getSubCategories, deleteMaterial, toggleMaterialEnable, getDocOptions } from '@/app/haogu/api/material'

const MATERIAL_TYPES = {
  1: '文本',
  2: '文章',
  3: '文件',
  4: '链接',
  5: '海报',
  6: '图片',
  7: '音频',
  8: '视频',
  9: '语音',
  10: '视频号'
}

const DOC_OPTIONS = ['全局', '售前', '售中', '售后']

export default function MaterialPage() {
  const [activeTab, setActiveTab] = useState<'local' | 'search'>('local')

  const getPreviewContent = (material: Material, handlePreview: (material: Material) => void) => {
    const contentData = material.data as any
    switch (material.type) {
      case 6: // 图片
        return contentData.link_url ? (
          <img
            src={contentData.link_url}
            alt="预览"
            className="w-16 h-16 object-cover rounded cursor-pointer"
            onClick={() => handlePreview(material)}
          />
        ) : null
      case 8: // 视频
        return contentData.video_img ? (
          <div className="relative cursor-pointer" onClick={() => handlePreview(material)}>
            <img
              src={contentData.video_img}
              alt="视频封面"
              className="w-16 h-16 object-cover rounded"
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 5v10l8-5-8-5z"/>
              </svg>
            </div>
          </div>
        ) : null
      default:
        return null
    }
  }

  const renderPreviewContent = (material: Material) => {
    const contentData = material.data as any
    switch (material.type) {
      case 1: // 文本
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">文本内容：</h3>
            <div className="whitespace-pre-wrap text-sm">
              {contentData.content || '无内容'}
            </div>
          </div>
        )

      case 2: // 文章
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">文章预览：</h3>
            {contentData.h5_Url ? (
              <div>
                <p className="mb-2 text-sm text-gray-600">H5链接：</p>
                <a
                  href={contentData.h5_Url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="link link-primary"
                >
                  {contentData.h5_Url}
                </a>
                <div className="mt-4">
                  <iframe
                    src={contentData.h5_Url}
                    className="w-full h-96 border rounded"
                    title="文章预览"
                  />
                </div>
              </div>
            ) : (
              <p className="text-gray-500">无预览内容</p>
            )}
          </div>
        )

      case 6: // 图片
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">图片预览：</h3>
            {contentData.link_url ? (
              <div className="flex justify-center">
                <img
                  src={contentData.link_url}
                  alt={contentData.title || '图片'}
                  className="max-w-full max-h-96 object-contain rounded"
                  onError={(e) => {
                    e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTBMMTMwIDcwSDcwTDEwMCAxMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHJlY3QgeD0iNzAiIHk9IjgwIiB3aWR0aD0iNjAiIGhlaWdodD0iODAiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
                  }}
                />
              </div>
            ) : (
              <p className="text-gray-500">无图片内容</p>
            )}
            {contentData.image_width && contentData.image_height && (
              <p className="text-sm text-gray-500 mt-2 text-center">
                尺寸: {contentData.image_width} × {contentData.image_height}
              </p>
            )}
          </div>
        )

      case 8: // 视频
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">视频预览：</h3>
            {contentData.link_url ? (
              <div>
                {contentData.video_img && (
                  <div className="mb-4 flex justify-center">
                    <img
                      src={contentData.video_img}
                      alt="视频封面"
                      className="max-w-full max-h-48 object-contain rounded"
                    />
                  </div>
                )}
                <video
                  controls
                  className="w-full max-h-96 rounded"
                  poster={contentData.video_img}
                >
                  <source src={contentData.link_url} type="video/mp4" />
                  您的浏览器不支持视频播放
                </video>
                {contentData.video_time && (
                  <p className="text-sm text-gray-500 mt-2">
                    时长: {Math.floor(contentData.video_time / 60)}:{(contentData.video_time % 60).toString().padStart(2, '0')}
                  </p>
                )}
              </div>
            ) : (
              <p className="text-gray-500">无视频内容</p>
            )}
          </div>
        )

      case 7: // 音频
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">音频预览：</h3>
            {contentData.link_url ? (
              <div>
                <audio controls className="w-full mb-2">
                  <source src={contentData.link_url} type="audio/mpeg" />
                  您的浏览器不支持音频播放
                </audio>
                {contentData.voice_time && (
                  <p className="text-sm text-gray-500">
                    时长: {Math.floor(contentData.voice_time / 60)}:{(contentData.voice_time % 60).toString().padStart(2, '0')}
                  </p>
                )}
              </div>
            ) : (
              <p className="text-gray-500">无音频内容</p>
            )}
          </div>
        )

      case 9: // 语音
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">语音预览：</h3>
            {contentData.link_url ? (
              <div>
                <audio controls className="w-full mb-2">
                  <source src={contentData.link_url} type="audio/mpeg" />
                  <source src={contentData.link_url} type="audio/wav" />
                  您的浏览器不支持音频播放
                </audio>
                {contentData.voice_time && (
                  <p className="text-sm text-gray-500">
                    语音时长: {contentData.voice_time}秒
                  </p>
                )}
              </div>
            ) : (
              <p className="text-gray-500">无语音内容</p>
            )}
          </div>
        )

      case 4: // 链接
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">链接预览：</h3>
            {contentData.h5_Url && (
              <div>
                <a
                  href={contentData.h5_Url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="link link-primary block mb-4"
                >
                  {contentData.h5_Url}
                </a>
                <div className="border rounded p-4">
                  <iframe
                    src={contentData.h5_Url}
                    className="w-full h-96"
                    title="链接预览"
                  />
                </div>
              </div>
            )}
          </div>
        )

      case 3: // 文件
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">文件信息：</h3>
            <div className="space-y-2">
              {contentData.link_url && (
                <div>
                  <span className="font-medium">下载链接：</span>
                  <a
                    href={contentData.link_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="link link-primary ml-2"
                  >
                    下载文件
                  </a>
                </div>
              )}
              {contentData.file_size && (
                <div>
                  <span className="font-medium">文件大小：</span>
                  <span className="ml-2">{(contentData.file_size / 1024 / 1024).toFixed(2)} MB</span>
                </div>
              )}
              {contentData.files_type && (
                <div>
                  <span className="font-medium">文件类型：</span>
                  <span className="ml-2">
                    {contentData.files_type === 1 ? 'Excel' :
                      contentData.files_type === 2 ? 'PDF' :
                        contentData.files_type === 3 ? 'Word' :
                          contentData.files_type === 4 ? 'PPT' : '未知'}
                  </span>
                </div>
              )}
            </div>
          </div>
        )

      default:
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">原始数据：</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-96">
              {JSON.stringify(contentData, null, 2)}
            </pre>
          </div>
        )
    }
  }

  return (
    <div>
      {/* 标签切换 */}
      <div className="tabs tabs-boxed mb-6">
        <button
          className={`tab ${activeTab === 'local' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('local')}
        >
          本地素材库
        </button>
        <button
          className={`tab ${activeTab === 'search' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('search')}
        >
          HaoGu素材搜索
        </button>
      </div>

      {/* 内容区域 */}
      {activeTab === 'local' ? (
        <MaterialShow
          queryMaterials={queryMaterials}
          getMaterialTypes={getMaterialTypes}
          updateMaterial={updateMaterial}
          getMainCategories={getMainCategories}
          getSubCategories={getSubCategories}
          deleteMaterial={deleteMaterial}
          toggleMaterialEnable={toggleMaterialEnable}
          getDocOptions={getDocOptions}
          getPreviewContent={getPreviewContent}
          renderPreviewContent={renderPreviewContent}
          MATERIAL_TYPES={MATERIAL_TYPES}
          DOC_OPTIONS={DOC_OPTIONS}
        />
      ) : (
        <HaoGuMaterialSearch />
      )}
    </div>
  )
}