'use client'

import { useState } from 'react'

export default function MetabaseAnalysisPage() {
  const [isLoading, setIsLoading] = useState(false)

  const METABASE_URL = 'http://*************:3005/collection/5'
  const USERNAME = '<EMAIL>'
  const PASSWORD = 'freespirit1234'

  const handleAutoLogin = () => {
    setIsLoading(true)

    setTimeout(() => {
      window.open(METABASE_URL, '_blank')
      setIsLoading(false)
    }, 500)
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      alert('已复制到剪贴板')
    } catch (err) {
      console.error('复制失败:', err)
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert('已复制到剪贴板')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              Haogu 数据分析平台
            </h1>
            <p className="text-gray-600">
              访问 Metabase 分析平台，查看 Haogu 项目的数据仪表板和报告
            </p>
          </div>

          <div className="space-y-6">
            <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-slate-800 mb-3">
                平台信息
              </h2>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-slate-700 font-medium">访问地址:</span>
                  <div className="flex items-center space-x-2">
                    <code className="bg-slate-100 px-2 py-1 rounded text-sm">
                      {METABASE_URL}
                    </code>
                    <button
                      onClick={() => copyToClipboard(METABASE_URL)}
                      className="text-slate-600 hover:text-slate-800 text-sm underline"
                    >
                      复制
                    </button>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-700 font-medium">用户名:</span>
                  <div className="flex items-center space-x-2">
                    <code className="bg-slate-100 px-2 py-1 rounded text-sm">
                      {USERNAME}
                    </code>
                    <button
                      onClick={() => copyToClipboard(USERNAME)}
                      className="text-slate-600 hover:text-slate-800 text-sm underline"
                    >
                      复制
                    </button>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-700 font-medium">密码:</span>
                  <div className="flex items-center space-x-2">
                    <code className="bg-slate-100 px-2 py-1 rounded text-sm">
                      {PASSWORD}
                    </code>
                    <button
                      onClick={() => copyToClipboard(PASSWORD)}
                      className="text-slate-600 hover:text-slate-800 text-sm underline"
                    >
                      复制
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col">
              <button
                onClick={handleAutoLogin}
                disabled={isLoading}
                className={`w-full py-3 px-6 rounded-lg text-white font-semibold transition-all duration-200 ${
                  isLoading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-slate-600 hover:bg-slate-700 active:bg-slate-800'
                }`}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>正在跳转...</span>
                  </div>
                ) : (
                  '访问 Metabase 分析平台'
                )}
              </button>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-yellow-800 font-semibold mb-2">使用说明</h3>
              <ul className="text-yellow-700 text-sm space-y-1">
                <li>• 点击按钮将打开新窗口访问 Metabase 分析平台</li>
                <li>• 使用上方提供的用户名和密码登录</li>
                <li>• 可点击复制按钮快速复制登录信息</li>
                <li>• 建议将 Metabase 页面加入浏览器书签便于后续访问</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}