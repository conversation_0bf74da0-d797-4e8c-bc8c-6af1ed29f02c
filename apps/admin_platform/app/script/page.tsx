'use client'

import { toast } from 'react-toastify'
import { resetMessageTransferService } from './api/script'

export default function Page() {
  return <div>
    <button className="btn btn-neutral" onClick={() => {
      toast.promise(resetMessageTransferService, {
        success:'reset success',
        error: 'reset error',
        pending:'reset pending'
      })
    }}>reset message transfer cache</button>
  </div>
}