import { contentWithFrequency } from 'service/local_cache/type'

export interface ChatHistory {
  id: string;
  chat_id: string;
  content: string;
  created_at: Date;
  role: string;
  is_send_by_human: boolean | null;
  short_description: string | null;
  round_id: string | null;
  is_recalled: boolean | null;
  message_id: string | null;
  chat_state: {
    nodeInvokeCount: Record<string, number> // 记录每个节点被调用的次数
    state: Record<string, boolean | undefined> // 会话状态，放一些 Flag 已拉群，已邀请入群等
    nextStage: string // 下个节点
    userSlots: Record<string, contentWithFrequency>
  } | null;
}