'use client'
import { use } from 'react'
import { EditSop } from '@/app/component/sop/editSop'
import { getConditionJudgeKeys, getCustomKeys, getLinkSourceVariableTagKeys, getVariableMapKeys, getYCloudTemplateList, querySopById, updateSop } from '@/app/moer_overseas/api/sop'
import { ActionType } from 'service/visualized_sop/visualized_sop_type'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = use(params)
  return <EditSop id={param.id}
    getConditionJudgeKeys={getConditionJudgeKeys}
    getCustomKeys={getCustomKeys}
    getLinkSourceVariableTagKeys={getLinkSourceVariableTagKeys}
    getVariableMapKeys={getVariableMapKeys}
    querySopById={querySopById}
    updateSop={updateSop}
    enableActionType={[ActionType.text, ActionType.dynamicPrompt, ActionType.image, ActionType.video, ActionType.audio, ActionType.file, ActionType.custom, ActionType.ycloudTemplate]}
    getYCloudTemplateList={getYCloudTemplateList}
  />
}
