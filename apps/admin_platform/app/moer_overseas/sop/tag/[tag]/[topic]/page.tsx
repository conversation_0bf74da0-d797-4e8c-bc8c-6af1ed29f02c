'use client'
import { use } from 'react'
import { TagPagination } from '@/app/component/sop/tagPagination'
import { deleteSop, querySop, querySopCount, testAction, testAllSop, updateSop } from '@/app/moer_overseas/api/sop'
import { importSop } from '@/app/moer_overseas/api/sop_topic'
export default function Page({
  searchParams,
  params
}: {
  searchParams: Promise<{
    page: string | undefined;
    pageSize: string | undefined;
  }>;
  params: Promise<{ tag: string, topic:string }>
}) {
  const searchParam = use(searchParams)
  const param = use(params)
  const page = Number(searchParam.page ?? 1)
  const pageSize = Number(searchParam.pageSize ?? 20)
  const tag = decodeURIComponent(param.tag)
  const topic = decodeURIComponent(param.topic)
  return <TagPagination
    page={page}
    pageSize={pageSize}
    tag={tag}
    topic={topic}
    deleteSop={deleteSop}
    querySop={querySop}
    querySopCount={querySopCount}
    testAction={testAction}
    testAllSop={testAllSop}
    updateSop={updateSop}
    importSop={importSop}
  />
}
