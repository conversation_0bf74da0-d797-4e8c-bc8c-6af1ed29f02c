'use client'
import { ChatHistory } from '@/app/component/user/chat_history'
import { queryChatById } from '@/app/moer_overseas/api/chat'
import { queryChatHistoryByChatId } from '@/app/moer_overseas/api/chat_history'
import { createManyDashboardData, queryDashboardDataByChatId } from '@/app/moer_overseas/api/dashboard_data'
import { findOrCreateDashboardTag, queryAllDashboardTags } from '@/app/moer_overseas/api/dashboard_tag'
import { queryLogByChatId } from '@/app/moer_overseas/api/log_store'
import { use } from 'react'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  return <ChatHistory
    queryDashboardDataByChatId={queryDashboardDataByChatId}
    createManyDashboardData={createManyDashboardData}
    queryAllDashboardTags={queryAllDashboardTags}
    findOrCreateDashboardTag={findOrCreateDashboardTag}
    id={decodeURIComponent(id)}
    queryChatHistoryByChatId={queryChatHistoryByChatId}
    queryLogByChatId={queryLogByChatId}
    queryChatById={queryChatById}
    langsmithProjectId='d68cca52-8492-4ae5-9941-9b91b328db81'
  />
}