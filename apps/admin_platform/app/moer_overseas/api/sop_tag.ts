'use server'

import { SopTag } from '@/app/type/sop_tag'
import { AdminPrismaMongoClient } from '@/lib/prisma'


export async function queryTags({ page, pageSize }:{ page:number, pageSize:number}):Promise<SopTag[]> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  const tags = await mongoClient.sop_tag.findMany({ take:pageSize, skip:pageSize * (page - 1) })
  return tags
}
export async function createTag(name:string, enableAccounts:string[]) {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  await mongoClient.sop_tag.create({ data:{ name, enable:false, enable_account:enableAccounts } })
}

export async function deleteTag(id: string) {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  const tag = await mongoClient.sop_tag.findFirst({ where:{ id } })
  if (!tag) {
    throw (`没有找到${id}`)
  }
  await mongoClient.sop_tag.delete({ where:{ id } })
  await mongoClient.sop_topic.deleteMany({ where:{ tag:tag.name } })
  await mongoClient.sop.deleteMany({ where:{ tag:tag.name } })
}

export async function changeTagEnable(tag_id: string, enable:boolean) {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  await mongoClient.sop_tag.update({ where:{ id:tag_id }, data:{ enable } })
}

export async function updateTagEnableAccount(tag_id:string, enableAccounts:string[]) {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  await mongoClient.sop_tag.update({ where:{ id:tag_id }, data:{ enable_account:enableAccounts } })
}

export async function queryAllTags():Promise<SopTag[]> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  const tags = await mongoClient.sop_tag.findMany()
  return tags
}