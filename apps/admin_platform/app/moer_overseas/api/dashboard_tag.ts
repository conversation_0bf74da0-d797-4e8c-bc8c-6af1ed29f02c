'use server'

import { DashboardTag } from '@/app/type/dashboard_tag'
import { AdminPrismaMongoClient } from '@/lib/prisma'

export async function queryAllDashboardTags(): Promise<DashboardTag[]> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  const tags = await mongoClient.dashboard_tag.findMany({
    orderBy: { created_at: 'desc' }
  })
  return tags
}

// 生成随机浅色背景颜色
function generateRandomLightColor(): string {
  const lightColors = [
    '#E3F2FD', // 浅蓝色
    '#F3E5F5', // 浅紫色
    '#E8F5E8', // 浅绿色
    '#FFF3E0', // 浅橙色
    '#FCE4EC', // 浅粉色
    '#F1F8E9', // 浅青绿色
    '#FFF8E1', // 浅黄色
    '#EFEBE9', // 浅棕色
    '#E0F2F1', // 浅青色
    '#F9FBE7', // 浅黄绿色
    '#EDE7F6', // 浅深紫色
    '#E1F5FE', // 浅天蓝色
    '#FFF9C4', // 浅柠檬色
    '#FFEBEE', // 浅红色
    '#F0F4C3', // 浅橄榄色
  ]

  return lightColors[Math.floor(Math.random() * lightColors.length)]
}

export async function createDashboardTag(name: string, color?: string): Promise<DashboardTag> {
  try {
    const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()

    // 检查标签是否已存在
    const existingTag = await mongoClient.dashboard_tag.findFirst({
      where: { name }
    })

    if (existingTag) {
      console.log('标签已存在，返回现有标签:', existingTag)
      return existingTag
    }

    const tagData = {
      name,
      color: color || generateRandomLightColor()
    }

    console.log('准备创建标签:', tagData)
    const tag = await mongoClient.dashboard_tag.create({
      data: tagData
    })

    console.log('标签创建成功:', tag)
    return tag
  } catch (error) {
    console.error('createDashboardTag 错误:', error)
    throw error
  }
}

export async function updateDashboardTag(id: string, name: string, color?: string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  await mongoClient.dashboard_tag.update({
    where: { id },
    data: {
      name,
      color,
      updated_at: new Date()
    }
  })
}

export async function deleteDashboardTag(id: string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()

  // 删除标签前，需要从所有dashboard_data中移除该标签ID
  const dashboardDataWithTag = await mongoClient.dashboard_data.findMany({
    where: {
      tag_ids: {
        has: id
      }
    }
  })

  // 更新所有包含该标签的dashboard_data
  for (const data of dashboardDataWithTag) {
    const newTagIds = data.tag_ids.filter((tagId) => tagId !== id)
    await mongoClient.dashboard_data.update({
      where: { id: data.id },
      data: { tag_ids: newTagIds }
    })
  }

  // 删除标签
  await mongoClient.dashboard_tag.delete({
    where: { id }
  })
}

export async function getDashboardTagsByIds(tagIds: string[]): Promise<DashboardTag[]> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()
  const tags = await mongoClient.dashboard_tag.findMany({
    where: {
      id: {
        in: tagIds
      }
    }
  })
  return tags
}

export async function findOrCreateDashboardTag(name: string): Promise<DashboardTag> {
  try {
    const mongoClient = AdminPrismaMongoClient.getMoerOverseasCommonInstance()

    // 先尝试查找已存在的标签
    const existingTag = await mongoClient.dashboard_tag.findFirst({
      where: { name }
    })

    if (existingTag) {
      return existingTag
    }

    // 如果不存在，创建新标签（使用随机颜色）
    return await createDashboardTag(name)
  } catch (error) {
    console.error('findOrCreateDashboardTag 错误:', error)
    throw error
  }
}
