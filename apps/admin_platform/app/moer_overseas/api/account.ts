'use server'

import { AccountData } from '@/app/type/account'
import { AdminPrismaMongoClient } from '@/lib/prisma'

export async function queryAccounts():Promise<AccountData[]> {
  const mongoClient = AdminPrismaMongoClient.getConfigInstance()
  const result = await mongoClient.config.findMany({ where:{ enterpriseName:'moer_overseas' }, select:{ id:true, accountName:true, wechatId:true } })
  return result
}

