{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "noImplicitAny": false, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "experimentalDecorators": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}