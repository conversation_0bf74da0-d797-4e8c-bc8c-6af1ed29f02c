import * as fs from 'fs/promises'
import * as path from 'path'
import { PrismaMongoClient } from '../src/database/prisma'

// 定义 CSV 行数据的接口
interface SOPCSVRow {
  week: string;
  day: string;
  time: string;
  title: string;
  scene: string;
  conditionOperator: string;
  condition: string;
  step: string;
  type: string;
  content: string;
  description: string;
}

// 定义步骤数据接口
interface SOPStep {
  type: 'text' | 'material';
  content: string;
  description: string;
  materialId?: string;
}

// 定义场景数据接口
interface SOPScene {
  conditionOperator: string;
  condition: string;
  steps: SOPStep[];
}

// 定义嵌套的 SOP 项目接口
interface SOPItem {
  week: string;
  day: string;
  time: string;
  title: string;
  scenes: SOPScene[];
}

// CSV 解析函数 - 正确处理跨行引号
function parseCSV(content: string): string[][] {
  const result: string[][] = []
  let currentRow: string[] = []
  let currentField = ''
  let inQuotes = false
  let i = 0

  // 移除 BOM 标记
  if (content.charCodeAt(0) === 0xFEFF) {
    content = content.slice(1)
  }

  while (i < content.length) {
    const char = content[i]

    if (char === '"') {
      if (inQuotes && i + 1 < content.length && content[i + 1] === '"') {
        // 处理转义的双引号 ""
        currentField += '"'
        i += 2
        continue
      } else {
        // 切换引号状态
        inQuotes = !inQuotes
      }
    } else if (char === ',' && !inQuotes) {
      // 字段分隔符
      currentRow.push(currentField.trim())
      currentField = ''
    } else if ((char === '\n' || char === '\r') && !inQuotes) {
      // 行结束
      currentRow.push(currentField.trim())
      if (currentRow.some((field) => field !== '')) {
        // 只有非空行才加入结果
        result.push(currentRow)
      }
      currentRow = []
      currentField = ''

      // 处理 \r\n 的情况
      if (char === '\r' && i + 1 < content.length && content[i + 1] === '\n') {
        i++
      }
    } else {
      // 普通字符
      currentField += char
    }

    i++
  }

  // 处理最后一行
  if (currentField || currentRow.length > 0) {
    currentRow.push(currentField.trim())
    if (currentRow.some((field) => field !== '')) {
      result.push(currentRow)
    }
  }

  return result
}

// 解析 CSV 文件
async function parseSOPCSV(filePath: string): Promise<SOPItem[]> {
  try {
    const content = await fs.readFile(filePath, 'utf-8')
    const rows = parseCSV(content)

    if (rows.length === 0) {
      throw new Error('CSV 文件为空')
    }

    // 跳过标题行
    const dataRows = rows.slice(1)
    const sopItemsMap = new Map<string, SOPItem>()

    // 用于跟踪当前的上下文信息
    let currentWeek = ''
    let currentDay = ''
    let currentTime = ''
    let currentTitle = ''
    let sceneNumber = 1
    let stepNumber = 1

    for (let i = 0; i < dataRows.length; i++) {
      const columns = dataRows[i]

      // 确保至少有足够的列
      if (columns.length < 11) {
        console.warn(`第 ${i + 2} 行数据不完整，列数: ${columns.length}`)
        continue
      }

      const [
        week,
        day,
        time,
        title,
        scene,
        conditionOperator,
        condition,
        step,
        type,
        content,
        description
      ] = columns

      // 跳过完全空白的行
      if (!week && !day && !time && !title && !scene && !step && !type && !content) {
        continue
      }

      // 更新当前上下文信息
      if (week) currentWeek = week
      if (day) currentDay = day
      if (time) currentTime = time
      if (title) currentTitle = title

      // 创建唯一的 key 来标识每个 SOP 项目
      const itemKey = `${currentWeek}_${currentDay}_${currentTime}_${currentTitle}`

      // 获取或创建 SOP 项目
      let sopItem = sopItemsMap.get(itemKey)
      if (!sopItem) {
        sopItem = {
          week: currentWeek,
          day: currentDay,
          time: currentTime,
          title: currentTitle,
          scenes: []
        }
        sopItemsMap.set(itemKey, sopItem)
      }

      // 处理场景和步骤
      sceneNumber = scene ? parseInt(scene, 10) || 1 : sceneNumber
      stepNumber = step ? parseInt(step, 10) || 1 : stepNumber

      // 查找或创建场景 (按场景编号查找)
      let sopScene = sopItem.scenes[sceneNumber - 1]
      if (!sopScene) {
        // 确保数组有足够的长度
        while (sopItem.scenes.length < sceneNumber) {
          sopItem.scenes.push({
            conditionOperator: '',
            condition: '',
            steps: []
          })
        }
        sopScene = sopItem.scenes[sceneNumber - 1]
      }

      // 当 scene 不为空时，更新条件信息
      if (scene) {
        sopScene.conditionOperator = conditionOperator || ''
        sopScene.condition = condition || ''
      }

      // 创建步骤
      const sopStep: SOPStep = {
        type: type === 'material' ? 'material' : 'text',
        content: content || '',
        description: description || ''
      }

      // 如果是 material 类型，尝试提取 materialId
      if (sopStep.type === 'material' && sopStep.content) {
        const materialId = sopStep.content.match(/^\d+$/)
        if (materialId) {
          sopStep.materialId = sopStep.content
        }
      }

      // 确保步骤数组有足够的长度，然后插入到正确的位置
      while (sopScene.steps.length < stepNumber) {
        sopScene.steps.push({
          type: 'text',
          content: '',
          description: ''
        })
      }
      sopScene.steps[stepNumber - 1] = sopStep
    }

    // 清理空的步骤和场景
    const result = Array.from(sopItemsMap.values())
    result.forEach((item) => {
      // 移除空的场景
      item.scenes = item.scenes.filter((scene) =>
        scene.steps.some((step) => step.content || step.description)
      )
      // 移除空的步骤
      item.scenes.forEach((scene) => {
        scene.steps = scene.steps.filter((step) => step.content || step.description)
      })
    })

    return result

  } catch (error) {
    console.error('解析 CSV 文件时出错:', error)
    throw error
  }
}

// 数据验证函数
function validateSOPItem(item: SOPItem): string[] {
  const errors: string[] = []

  if (!item.time && item.title) {
    errors.push('时间字段不能为空')
  }

  if (!item.title) {
    errors.push('标题字段不能为空')
  }

  if (!item.scenes || item.scenes.length === 0) {
    errors.push('至少需要一个场景')
  }

  item.scenes.forEach((scene, sceneIndex) => {
    if (!scene.steps || scene.steps.length === 0) {
      errors.push(`场景 ${sceneIndex + 1}: 至少需要一个步骤`)
    }

    scene.steps.forEach((step, stepIndex) => {
      if (!['text', 'material'].includes(step.type)) {
        errors.push(`场景 ${sceneIndex + 1}, 步骤 ${stepIndex + 1}: 类型只能是 text 或 material`)
      }

      if (step.type === 'material' && !step.materialId) {
        errors.push(`场景 ${sceneIndex + 1}, 步骤 ${stepIndex + 1}: material 类型必须包含有效的 materialId`)
      }
    })
  })

  return errors
}

// 主函数
async function importHaoguSOP(): Promise<void> {
  const filename = 'SOP-Day3.csv'
  const csvPath = path.join(__dirname, filename)

  try {
    console.log(`开始解析 CSV 文件: ${csvPath}`)

    const sopItems = await parseSOPCSV(csvPath)

    console.log(`成功解析 ${sopItems.length} 条 SOP 记录`)

    // 数据验证
    let validItems = 0
    let invalidItems = 0

    for (const [index, item] of sopItems.entries()) {
      const errors = validateSOPItem(item)
      if (errors.length > 0) {
        console.warn(`第 ${index + 1} 条记录验证失败:`, errors)
        invalidItems++
      } else {
        validItems++
      }
    }

    console.log(`数据验证完成: ${validItems} 条有效记录, ${invalidItems} 条无效记录`)

    // 输出所有数据
    if (sopItems.length > 0) {
      console.log('\n所有解析数据:')
      console.log(JSON.stringify(sopItems, null, 2))
    }

    // 保存到数据库
    await saveToPrisma(sopItems)

  } catch (error) {
    console.error('导入失败:', error)
    process.exit(1)
  }
}

// 解析文本内容，将 {} 中的内容转换为动态变量
function parseTextContent(content: string) {
  const textList: any[] = []
  let currentIndex = 0

  // 使用正则表达式找到所有 {} 包围的内容
  const regex = /\{([^}]+)\}/g
  let match

  while ((match = regex.exec(content)) !== null) {
    // 添加 {} 之前的固定文本
    if (match.index > currentIndex) {
      const fixedText = content.substring(currentIndex, match.index)
      if (fixedText) {
        textList.push({
          type: 'fixed',
          text: fixedText
        })
      }
    }

    // 添加动态变量
    textList.push({
      type: 'variable',
      tag: match[1]
    })

    currentIndex = match.index + match[0].length
  }

  // 添加最后剩余的固定文本
  if (currentIndex < content.length) {
    const remainingText = content.substring(currentIndex)
    if (remainingText) {
      textList.push({
        type: 'fixed',
        text: remainingText
      })
    }
  }

  // 如果没有找到任何 {}，整个内容都是固定文本
  if (textList.length === 0 && content) {
    textList.push({
      type: 'fixed',
      text: content
    })
  }

  return textList
}

// 将 SOP 数据转换为 Prisma 数据库格式
function convertToPrismaFormat(sopItems: SOPItem[]) {
  return sopItems.map((item) => {
    // 将周数转换为数字
    const weekMap: { [key: string]: number } = {
      '课前周': -1,
      '上课周': 0,
      '课后第一周': 1,
      '课后第二周': 2,
      '课后第三周': 3,
      '课后第四周': 4,
    }

    // 将星期转换为数字
    const dayMap: { [key: string]: number } = {
      '周一': 1, '星期一': 1,
      '周二': 2, '星期二': 2,
      '周三': 3, '星期三': 3,
      '周四': 4, '星期四': 4,
      '周五': 5, '星期五': 5,
      '周六': 6, '星期六': 6,
      '周日': 7, '星期日': 7,
    }

    const week = weekMap[item.week] ?? 1
    const day = dayMap[item.day] ?? 1

    // 转换 scenes 为 Situation 格式
    const situations = item.scenes.map((scene) => {
      // 构建条件
      const conditions: any[] = []
      if (scene.condition) {
        conditions.push({
          isOrNotIs: scene.conditionOperator !== '非', // '非' 表示否定
          type: 'fixed', // 可以根据需要调整类型
          condition: scene.condition
        })
      }

      // 构建动作 - 将步骤转换为动作数组
      const action = scene.steps.map((step) => {
        if (step.type === 'material') {
          // material 类型：使用 sourceId，不包含 content 和 materialId
          return {
            type: 'material',
            sourceId: step.materialId || step.content,
            description: step.description
          }
        } else {
          // text 类型：解析 content 中的动态变量，转换为 textList
          const textList = parseTextContent(step.content)
          return {
            type: 'text',
            textList,
            description: step.description
          }
        }
      })

      return {
        conditions,
        action
      }
    })

    return {
      title: item.title,
      week,
      day,
      time: item.time,
      situations,
      enable: true, // 默认启用
      tag: '好人好股SOP', // 可以根据需要调整
      topic: 'Day3' // 可以根据需要调整
    }
  })
}

// 保存数据到 Prisma 数据库
async function saveToPrisma(sopItems: SOPItem[]): Promise<void> {
  const prisma = PrismaMongoClient.getCommonInstance()

  try {
    console.log('开始将数据保存到数据库...')

    const prismaData = convertToPrismaFormat(sopItems)
    // console.dir(prismaData, { depth:Infinity })

    // 批量创建 SOP 记录
    const results = await Promise.all(
      prismaData.map(async (data) => {
        return await prisma.sop.create({
          data: data
        })
      })
    )

    console.log(`成功保存 ${results.length} 条 SOP 记录到数据库`)

    // 输出创建的记录ID
    results.forEach((result, index) => {
      // console.log(`SOP ${index + 1}: ${result.id} - ${result.title}`)
    })

  } catch (error) {
    console.error('保存到数据库时出错:', error)
    throw error
  }
}

// 导出函数供其他模块使用
export {
  parseSOPCSV,
  validateSOPItem,
  convertToPrismaFormat,
  saveToPrisma,
  SOPItem,
  SOPScene,
  SOPStep,
  SOPCSVRow
}

// 如果直接运行此脚本，执行主函数
if (require.main === module) {
  importHaoguSOP()
}