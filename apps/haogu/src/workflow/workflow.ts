import logger from 'model/logger/logger'
import { AsyncLock } from 'model/lock/lock'
import { Config } from 'config'
import { DataService } from './helper/get_data'
import { getState, IWorkflowState } from 'service/llm/state'
import { Router } from './router'
import { InterruptError } from 'service/message_handler/interrupt/interrupt_handler'
import { VisualizedSopTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { GroupNotification } from 'service/group_notification/group_notification'
import { override } from '../config/override'
import { HumanTransferType } from 'service/human_transfer/human_transfer'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../config/instance/base_instance'
import { extractUserSlots, humanTransfer, memoryStoreClient } from '../config/instance/instance'
import { commonMessageSender, replyClient } from '../config/instance/send_message_instance'
import { eventTrackClient } from '../config/instance/event_track_instance'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from './helper/register_task'
import { FreeTalk } from 'service/agent/freetalk'
import { ContextBuilder } from './context'
import { stageFilter } from './meta_action/stage'
import { BaseWorkFlow, Node, WorkFlowNode } from 'service/agent/workflow'
import { TaskManager } from 'service/task/task_manager'

export class FreeTalkNode extends WorkFlowNode {
  public static async invoke(state: IWorkflowState) {
    const freeTalk = new FreeTalk(chatHistoryServiceClient, ContextBuilder, eventTrackClient, replyClient, stageFilter)
    return await freeTalk.invoke(state)
  }
}

export const NodeMap: { [key in Node]?: typeof WorkFlowNode } = {
  [Node.FreeTalk]: FreeTalkNode,
}

export class Workflow extends BaseWorkFlow {
  /**
     * 对话流程
     * @param chat_id
     * @param user_id
     * @param userMessage
     */
  public static async step(chat_id: string, user_id: string, userMessage: string) {
    try {
      userMessage = this.transferWechatEmoji(userMessage)
      await chatHistoryServiceClient.addUserMessage(chat_id, userMessage)
      if (userMessage === '【表情】') {
        return
      } else if (userMessage === '【语音/视频通话】') {
        await humanTransfer.transfer(chat_id, user_id, HumanTransferType.VoiceOrVideoCall, 'onlyNotify')
        await commonMessageSender.sendText(chat_id, {
          text: '你好，我这边在忙不方便接听电话，你要是有什么事情可以直接发消息哈，我微信可以回复消息的',
          description: '客户发起语音/视频通话'
        })
        return
      } else if (userMessage.toLowerCase() === 'clear' && Config.isTestAccount()) {
        await this.resetChat(chat_id, user_id)
        return
      }

      const lock = new AsyncLock()

      await lock.acquire(chat_id, async () => { // 如果有新消息，当前回复会被丢弃
        const entryNode = (await chatStateStoreClient.get(chat_id)).nextStage
        const state = await getState(chat_id, user_id, userMessage) // 如果有新消息，在锁释放后，当前流程会中断
        await Workflow.run(entryNode as Node, state)
      }, { timeout: 2 * 60 * 1000 })
    } catch (e) {
      if (!(e instanceof InterruptError)) {
        try {
          if (e instanceof Error) {
            // 将错误的堆栈信息打印到自定义 logger 中
            logger.error(`Error: ${e.message}\nStack Trace: ${e.stack}`)
          } else {
            // 如果 e 不是 Error 实例，捕获当前的堆栈信息
            const stack = new Error().stack
            logger.error(`消息回复失败: ${stack}`)
          }
          await humanTransfer.transfer(chat_id, user_id, HumanTransferType.MessageSendFailed)
        } catch (e) {
          logger.error('转人工失败', e)
        }
      }
    }
  }

  private static async run(entryNode: Node, state: IWorkflowState) {
    let node = NodeMap[entryNode]
    logger.trace({ chat_id: state.chat_id }, `初始跳转节点: ${entryNode}`)
    if (!node) {
      logger.error(`[WorkFlow] node not found: ${entryNode}`)
      return
    }

    await this.preReply(state)

    // 根据客户消息自动转移
    const autoTransferNode = await Router.route(state)
    if (autoTransferNode === Node.DummyEnd) {
      return
    }

    if (autoTransferNode && autoTransferNode !== Node.Dummy) {
      logger.trace({ chat_id: state.chat_id }, `重定向到节点: ${autoTransferNode}`)

      node = NodeMap[autoTransferNode]
      if (!node) {
        logger.error(`[WorkFlow] auto transfer node not found: ${autoTransferNode}`)
        return
      }
    }

    const nextStage = await node.invoke(state)
    await chatStateStoreClient.update(state.chat_id, { nextStage })

    await this.postReply(state)
  }

  private static async postReply(state: IWorkflowState) {
    // 运行沉默检测
    await SilentReAsk.schedule(
      TaskName.SilenceDetection,
      state.chat_id,
      10 * 60 * 1000,
      undefined,
      {
        auto_retry: true
      }
    )
  }

  public static async humanInvolveGroupNotify(contactName: string, courseNo: number | null, userMessage: string) {
    await GroupNotification.notify(`${contactName}（${courseNo}）客户AI未开启，请人工处理\n客户：${userMessage}`)
  }

  private static transferWechatEmoji(message: string) {
    const emojiRegex = /\[.*?]/g
    const emojiMap:Record<string, string> = {
      '[微笑]': '😊',
      '[调皮]': '😝',
      '[合十]': '🙏',
      '[爱心]': '💗',
      '[玫瑰]': '🌹',
      '[捂脸]': '🤦',
      '[笑哭]': '😂',
      '[咖啡]': '☕️',
      '[抱拳]': '🙏',
      '[拥抱]': '🫂'
    }
    return message.replace(emojiRegex, (match) => {
      const emoji = emojiMap[match]
      return emoji || match
    })
  }

  private static async preReply(state: IWorkflowState) {
    // 异步提取 Memory, 客户槽位
    memoryStoreClient.extractMemoriesAndUserSlots(state.chat_id, state.round_id, extractUserSlots)
  }

  private static async resetChat(chat_id: string, user_id: string) {
    await commonMessageSender.sendText(chat_id, { text:'聊天已重置', description:'聊天重置' }, {
      force:true
    })
    await chatHistoryServiceClient.clearChatHistory(chat_id)
    await chatStateStoreClient.clear(chat_id)
    if (await chatDBClient.getById(chat_id)) {
      await chatDBClient.setHumanInvolvement(chat_id, false)
      // 重置课程编号为当前日期
      const currentCourseNo = DataService.getCurrentCourseNo()
      await chatDBClient.updateCourseNo(chat_id, currentCourseNo)
    }

    await VisualizedSopTasks.clearSop('haogu', chat_id)

    // clear memory
    await memoryStoreClient.clearMemory(chat_id)

    // cancel all active tasks
    try {
      const canceledCount = await TaskManager.cancelAllActiveTasks(chat_id)
      logger.log(`Reset chat: canceled ${canceledCount} active tasks for chat_id: ${chat_id}`)
    } catch (error) {
      logger.error(`Failed to cancel tasks during reset for chat_id: ${chat_id}`, error)
    }

    await override.sendWelcomeMessage(chat_id, user_id)
  }
}