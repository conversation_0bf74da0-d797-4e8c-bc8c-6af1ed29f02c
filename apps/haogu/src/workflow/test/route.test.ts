import { <PERSON>ziAP<PERSON> } from 'model/juzi/api'
import { Config } from 'config'
import { loadConfigByAccountName } from 'service/database/config'


describe('Test', function () {
  beforeAll(() => {

  })

  it('test invite to group', async () => {
    JuziAPI.wxIdToExternalUserId = async (user_id) => {
      return '11'
    }
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe1')
    // await EventHandler.inviteToGroup('7881302298050442_1688857404698934', '7881302298050442_1688857404698934')
  }, 60000)
})
