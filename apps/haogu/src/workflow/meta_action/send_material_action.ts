import { PromptTemplate } from '@langchain/core/prompts'
import { LLM } from 'lib/ai/llm/llm_model'
import { MaterialManager } from '../helper/material_manager'
import { sleep } from 'openai/core'
import { commonMessageSender } from '../../config/instance/send_message_instance'
import { ActionInfo } from 'service/agent/stage'
import { DataService } from '../helper/get_data'
import logger from 'model/logger/logger'


export class SendMaterialAction {


  public static async sendMaterial(chat_id:string, round_id:string, strategy: string | string[]) {
    const prompt = `# 角色设定
你是一个“素材调用助手”。

# 职责
- 你的工作不是执行对话策略，而是：**从对话策略中抽取出“需要发送/展示/提供”的素材目标**，并在素材目录中找到对应条目。
- 忽略对话策略中所有与“发送/展示/提供”无关的步骤（如追问、协助、强调等）。

# 任务
- 仅当对话策略中出现“发送/展示/提供/分享/推送/附上/引用/给出/调用”等动词，**并且其宾语能明确指向某个素材**时，才选择对应素材；
- 如果只是说“提醒/说明/强调/提到”，不视为发送素材；
- 最多选 3 条素材；
- 未命中上述动词指令，一律返回空数组 []。

# 本轮对话策略
{{strategy}}

# 时间信息
{{timeInfo}}

# 素材目录说明
- 下方目录中的条目仅为**示例**，并不是完整集合。
- **优先精确匹配**：若策略中的素材名称能在目录中找到高度一致的条目，输出目录里的正式标题。
- **允许保留原名**：若策略中的素材在目录中找不到完全对应的条目，不要强行映射到相似但不等价的条目，应保留策略中出现的原始名称作为 title。
- **模板精确匹配**：当目录项为“第x节…”模板时，按上文占位符规则替换 x 后，视为**精确匹配**。

# 素材目录结构
- 公司介绍相关（可能包含的素材名称：公司好评案例）
- 6天课程相关（可能包含的素材标题：福利课1:《双线合一核心解析》、福利课2:《四点共振实战应用》、福利课3:《主力锁仓和出货》 、第x节预习视频、第x节课后作业、第x节课后笔记、第x节课约课礼）
- 交易体系相关
- 工具相关(可能包含的素材标题：手机/电脑版APP安装方法、多空趋势线指标工具、抄底先锋指标工具、主力进出指标工具)
- 3360实战班相关
- 下单相关
- 客户案例相关
- 其他

# 输出格式（仅输出 JSON，不要额外文字）
{
  "think":"思考过程",
  "selected_materials": [
    {
      "category": "素材目录中的分类",
      "title": "素材标题"
    }
  ]
}`

    const promptTemplate = PromptTemplate.fromTemplate(prompt, { templateFormat:'mustache' })

    const timeInfo = await SendMaterialAction.getTimInfo(chat_id)

    const res = await LLM.predict(promptTemplate, {
      meta: {
        round_id: round_id,
        chat_id: chat_id
      },
      responseJSON:true,
      reasoningEffort:'medium',
      maxTokens: 2048
    }, { strategy, timeInfo })

    const selectedMaterials = res.selected_materials

    let caseInfo = '资料描述：'
    const sourceIdList: string[] = []

    for (const selectedMaterial of selectedMaterials) {
      const material = await new MaterialManager().searchMaterialByTitle(chat_id, selectedMaterial.title, selectedMaterial.category)
      if (material && await new MaterialManager().isValidCourseMaterial(chat_id, material?.title)) {
        caseInfo += `${material.description}`
        sourceIdList.push(material.source_id)
      }
    }

    const callBack = async () => {
      for (const sourceId of sourceIdList) {
        await sleep(3000)
        logger.log({ chat_id:chat_id }, `被动回复发送素材${sourceId}`)
        await commonMessageSender.sendMaterial(chat_id, { sourceId: sourceId })
      }
    }

    return {
      guidance: caseInfo,
      callback: callBack
    } as ActionInfo

  }

  private static async  getTimInfo(chatId: string): Promise<string> {
    const currentTime = await DataService.getCurrentTime(chatId)

    if (currentTime.day < 1) {
      return '课前'
    } else if (currentTime.day > 6) {
      return '课后'
    } else {
      const dayNames = ['一', '二', '三', '四', '五', '六']
      return `当前课程第${dayNames[currentTime.day - 1]}天`
    }
  }

}