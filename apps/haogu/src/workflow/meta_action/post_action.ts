import logger from 'model/logger/logger'
import { ActionInfo } from 'service/agent/stage'
import { chatStateStoreClient } from '../../config/instance/base_instance'
import { commonMessageSender } from '../../config/instance/send_message_instance'
import { IChattingFlag } from '../../config/manifest'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'

export class PostAction {
  public static async sendInvitation() {
    const purchaseLink = 'https://www.integrity.com.cn/s/vEM'
    const actionInfo: ActionInfo = { guidance: `务必向客户发送下单链接：${purchaseLink}，并邀约客户购买` }
    return actionInfo
  }

  public static async reaskAnotherDay(chat_id: string) {
    logger.trace({ chat_id: chat_id }, '客户保留名额，次日询问')
    // await SilentReAsk.schedule(TaskName.reask_another_day, chat_id, 24 * 60 * 60 * 1000, {}, { auto_retry: true }) // 24小时后提醒客户
    return { guidance: '' } as ActionInfo
  }

  public static async sendGiftVideo(chat_id: string): Promise<ActionInfo> {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

    if (state.is_send_core_materials) {
      logger.log(`筹码峰核心资料已发送，跳过 Post Action for chat: ${chat_id}`)
      return {
        guidance: '之前给客户发过福利课视频:\n' +
            '《双线合一核心解析》: https://ai.9635.com.cn/1CxoIXlo7 (6分钟) \n' +
            '《四点共振实战应用》: https://ai.9635.com.cn/19VyCvLbj (7分钟) \n' +
            '《主力锁仓和出货》: https://ai.9635.com.cn/sLo5EycF \n' +
            '正常回复客户消息即可', callback: async () => {
        }
      }
    }
    return {
      guidance: '简单回复客户消息之后发送3节福利课链接:\n' +
          '《双线合一核心解析》: a.ckjr001.com/xq4d3d/pe9joj3 (6分钟) \n' +
          '《四点共振实战应用》: a.ckjr001.com/0NkyNm/pe9joj3 (7分钟) \n' +
          '《主力锁仓和出货》: a.ckjr001.com/67a5RG/pe9joj3 \n' +
          '并表示期待客户看完之后通知你', callback: async () => {
        // 更新状态
        await chatStateStoreClient.update(chat_id, {
          state: <IChattingFlag>{
            is_send_core_materials: true
          }
        })
      }
    }
  }

  public static async sendInstallationGuide(chat_id: string): Promise<ActionInfo> {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

    // 如果已经发送过，则不再发送
    if (state.is_send_installation_guide) {
      logger.log(`筹码峰安装教程已发送，跳过 Post Action for chat: ${chat_id}`)
      return {
        guidance: '之前给客户发过筹码峰安装教程:\n' +
            '手机版、电脑版app安装链接: https://download.9635.com.cn/\n' +
            '调出筹码峰视频教程: https://drive.weixin.qq.com/s?k=AGwAqAeQAA0d84u1Sv\n' +
            '正常回复客户消息即可', callback: async () => {
        }
      }
    }
    return {
      guidance: '指导客户安装好人好股app并且调出筹码峰:\n' +
          '手机版、电脑版app安装链接: https://download.9635.com.cn/\n' +
          '调出筹码峰视频教程: https://drive.weixin.qq.com/s?k=AGwAqAeQAA0d84u1Sv ', callback: async () => {
        await chatStateStoreClient.update(chat_id, {
          state: <IChattingFlag>{
            is_send_installation_guide: true,
            is_welcome_stage_completed: true
          }
        })
      }
    }
  }

  public static async sendEconomicCurve(chat_id: string, round_id: string): Promise<ActionInfo> {
    await commonMessageSender.sendMsg(chat_id, [{
      type: SendMessageType.image,
      url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/asset/expert_capital_curve.png',
      description: '高手资金曲线图片'
    }, {
      type: SendMessageType.text,
      text: '你看，这就是高手的资金曲线——平稳向上，没有剧烈的大起大落。交易的终局不是靠一次暴富改变命运，而是靠长期稳定复利，让资金曲线越来越漂亮。股市是“久富平台”，不是“暴富平台”。真正爽的交易，是每一次买卖都胸有成竹，利润是努力和学习的自然结果；而不是今天赚明天亏，情绪跟着账户上蹿下跳。你可以对照看下你的投资账户资金曲线，也可一发给我来看看🫣',
      description: '介绍高手资金曲线'
    }, {
      type: SendMessageType.text,
      text: '我们好人好股，是唯一一家把股民当成交易员进行专业赋能的培训机构。我们不推荐股票，而是授人以渔，真心希望教会学员一套稳定赚钱的交易方法，让每一次操作都有章可循。很开心在茫茫人海中遇到你！接下来，我们将开启一段6天的股民开悟之旅，让你彻底搞明白——你在股市赚的是什么钱、如何持续稳定地赚钱。📅 明天第一节课，记得准时来上课，我们直播见！',
      description: '介绍高手资金曲线后介绍好人好股'
    }])
    await chatStateStoreClient.update(chat_id, {
      state: <IChattingFlag>{
        after_bonding: true
      }
    })
    return {
      guidance: '如果感到需要就简单结尾一下，否则就不说话', callback: async () => {
      }
    }
  }

}