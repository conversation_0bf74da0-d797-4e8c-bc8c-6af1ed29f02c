import { SimpleRag } from './simple_rag'
import { chatHistoryServiceClient } from '../../../config/instance/base_instance'
import { RewooRag } from './rewoo_rag'

export class RAG {
  public static async search(inputQuery: string, strategy: string | undefined, chat_id: string, round_id: string) {

    let ragContext = await SimpleRag.search(inputQuery, chat_id)

    if (!strategy) {
      strategy = ''
    } else {
      strategy = strategy.split('\n')[0]
    }

    const dialogHistory = await chatHistoryServiceClient.getDialogHistory(chat_id, 3, 6)
    if (await RewooRag.isNeedReasoning(dialogHistory, strategy, ragContext, round_id)) {
      return await RewooRag.search(dialogHistory, strategy, chat_id, round_id)
    }

    if (/.*?_\w{4}\.\w+/.test(ragContext)) { // RAG 包含文件
      ragContext = `下面答案中如果提到文件资源为 [文件名称] 格式，请严格按照原始文件名称输出，不要添加额外的文件或信息，并且只参考补充信息中提到的文件资源
    ${ragContext}`
    }

    return ragContext
  }

  // public static async search(inputQuery: string, chat_id: string, round_id: string) {
  //   return ''
  // }
}