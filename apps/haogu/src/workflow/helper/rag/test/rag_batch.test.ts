import { FileHelper } from 'lib/file'
import { chatHistoryServiceClient } from '../../../../config/instance/base_instance'
import { RAG } from '../rag'
import { UUID } from 'lib/uuid/uuid'
import { DataService } from '../../get_data'


describe('ragBatchTest', () => {

  it('batchTest', async () => {
    const data = await FileHelper.readFile('/Users/<USER>/Desktop/code/eliza-sales/apps/haogu/src/workflow/helper/rag/test/data/聊天记录.json')
    let jsonData = JSON.parse(data)

    DataService.getCurrentTime = async () => {
      return {
        is_course_day: true,
        day: 8,
        time: '08:00:00',
      }
    }

    //保留100至200
    jsonData = jsonData.splice(100, 50)

    const resList:any[] = []

    try {
      for (let i = 0; i < jsonData.length; i++) {
        const inputQuery = jsonData[i].conversation_text
        const strategy = ''

        console.log(i, inputQuery)

        chatHistoryServiceClient.getDialogHistory = async (chat_id: string) => {
          return inputQuery
        }



        const ragRes = await RAG.search(inputQuery, strategy, '123', UUID.v4())

        const res = {
          ragRes: ragRes,
          inputQuery: inputQuery,
        }

        resList.push(res)
      }
    } catch (error) {
      await FileHelper.writeFile('/Users/<USER>/Desktop/code/eliza-sales/apps/haogu/src/workflow/helper/rag/test/data/rag_batch.json', JSON.stringify(resList))
    }

    await FileHelper.writeFile('/Users/<USER>/Desktop/code/eliza-sales/apps/haogu/src/workflow/helper/rag/test/data/rag_batch.json', JSON.stringify(resList))
  }, 9e8)

})