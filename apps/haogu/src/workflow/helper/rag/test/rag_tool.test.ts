import { ReasoningRagTool } from '../reasoning_rag_tool'
import { DataService } from '../../get_data'
import { UUID } from 'lib/uuid/uuid'


describe('ragToolTest', () => {
  it('testGeneralSearch', async () => {

    DataService.getCurrentTime = (chatId) => {
      return Promise.resolve({
        is_course_day: false,
        day: 8,
        time: '08:00:00',
      })
    }

    const query = '股票交易中A、B、C，三点和一二三高，单踩双踩分别是什么意思？'
    const tool = await ReasoningRagTool.getToolByKey(ReasoningRagTool.GeneralSearch)
    const res = await tool?.execute({
      strategy: '',
      chatId: '1',
      roundId: '1',
      searchKey: query
    })

    console.log(res)
  }, 60000)


  it('testTransactionSystemKnowledgeExtract', async () => {

    const query = '我感觉长期交易坚持不下去'
    const tool = await ReasoningRagTool.getToolByKey(ReasoningRagTool.SearchTransactionSystemKnowledge)
    const res = await tool?.execute({
      strategy: '先用建立信任认可坚持难的现实但指出核心在方法与节奏 用明确终局描绘通过体系让资金曲线平稳上升的目标感',
      chatId: '1',
      roundId: '1',
      searchKey: query
    })

    console.log(res)

  }, 9e8)


  it('testSearchCourseLink', async () => {

    const chatId = '712221735350273048_2300'
    const roundId = UUID.v4()

    const tool = await ReasoningRagTool.getToolByKey(ReasoningRagTool.SearchCourseLink)
    const res = await tool?.execute({
      strategy: '',
      chatId: chatId,
      roundId: roundId,
      searchKey: ''
    })

    console.log(res)

  }, 9e8)
})
