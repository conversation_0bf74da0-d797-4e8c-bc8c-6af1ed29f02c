import { RAG } from '../rag'
import { UUID } from 'lib/uuid/uuid'
import { chatHistoryServiceClient } from '../../../../config/instance/base_instance'

describe('ragTest', () => {

  it('simpleRag', async () => {
    const inputQuery = '我想问下你们这个课程多少钱啊？'
    const strategy = ''

    chatHistoryServiceClient.getDialogHistory = async (chat_id: string) => {
      return inputQuery
    }

    const res = await RAG.search(inputQuery, strategy, '123', UUID.v4())

    console.log('res', res)
  }, 9e8)

  it('multiQuestionRetrieve', async () => {
    const inputQuery = '我想问下你们这个课程多少钱啊？然后到时能不能分期？'
    const strategy = ''

    chatHistoryServiceClient.getDialogHistory = async (chat_id: string) => {
      return '我想问下你们这个课程多少钱啊？然后到时能不能分期？'
    }

    const res = await RAG.search(inputQuery, strategy, '123', UUID.v4())

    console.log('res', res)
  }, 9e8)


  it('testTransactionSystemKnowledgeExtract', async () => {

    const inputQuery = '我感觉长期交易坚持不下去'
    const strategy = '先用建立信任认可坚持难的现实但指出核心在方法与节奏 用明确终局描绘通过体系让资金曲线平稳上升的目标感'

    chatHistoryServiceClient.getDialogHistory = async (chat_id: string) => {
      return inputQuery
    }

    const res = await RAG.search(inputQuery, strategy, '123', UUID.v4())

    console.log('res', res)

  }, 9e8)
})
