import { PlannerMaterialRag } from '../planner_material_rag'
import { UUID } from 'lib/uuid/uuid'


describe('PlannerMaterial', () => {
  it('testExtractMaterial', async () => {
    const mainTask = '把预习课资料发给客户'
    const res = await PlannerMaterialRag.extractMaterial(mainTask, '', UUID.v4())

    console.log(res)
  }, 9e8)


  it('sendMaterial', async () => {

    await PlannerMaterialRag.sendMaterial('', ['课程预约流程图'])

  }, 9e8)
})