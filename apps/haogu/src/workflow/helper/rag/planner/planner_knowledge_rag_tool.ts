import { SimpleRag } from '../simple_rag'
import ElasticSearchService from 'model/elastic_search/elastic_search'
import { DataService } from '../../get_data'
import { RagToolExecuteParams } from '../reasoning_rag_tool'

export interface IKnowledgeRagTool {
    name: string
    description: string
    execute: (params: RagToolExecuteParams) => Promise<string>
}

export class KnowledgeRagTool {

  public static GeneralSearch = 'rag搜索' //常规rag查询
  public static SearchCourseLink = '获取课程链接' //查询基础规则
  public static SearchKeyConcepts = '搜索课程关键概念'

  public static async getTools(): Promise<IKnowledgeRagTool[]> {
    return [
      await this.getGeneralSearchTool(),
      await this.getSearchCourseLinkTool(),
      // await this.getSearchKeyConceptsTool(),
    ]
  }

  public static async getToolByKey(key: string): Promise<IKnowledgeRagTool | null> {
    const toolMap = {
      [KnowledgeRagTool.GeneralSearch]: await this.getGeneralSearchTool(),
      [KnowledgeRagTool.SearchCourseLink]: await this.getSearchCourseLinkTool(),
      // [KnowledgeRagTool.SearchKeyConcepts]: await this.getSearchKeyConceptsTool(),
    }

    return Promise.resolve(toolMap[key])
  }


  private static async getGeneralSearchTool() {
    const name = KnowledgeRagTool.GeneralSearch
    const description = `用于查询知识、课程与概念性问题。
- 输入格式要求：
1.input: 必须是 单一、清晰的自然语言问题（疑问句），以“？”结尾。
2.只能包含单一概念/问题，问题应尽量准确简短。`

    const execute = async (params: RagToolExecuteParams) => {
      // 获取要查询的文档
      const queryDocs = await SimpleRag.getQueryDocs(params.chatId)

      // 构建查询 filter
      const filter = {
        bool:{
          must:[
            {
              terms: {
                'metadata.doc': queryDocs
              }
            }
          ]
        }
      }

      const res = await ElasticSearchService.embeddingSearch(
        SimpleRag.index,
        params.searchKey ?? '',
        2,
        0.74,
        filter
      )

      if (res.length === 0) {
        return `${name}没有找到相关结果`
      } else {
        return `${name}搜索结果：
${res.map((item) => {
    return `问题：${item.metadata.q}
答案：${item.metadata.a}`
  }).join('\n')}`
      }
    }


    return {
      name:name,
      description: description,
      execute: execute
    } as IKnowledgeRagTool
  }


  private static async getSearchCourseLinkTool() {
    const name = KnowledgeRagTool.SearchCourseLink
    const description = '获取课程直播/回放链接，当策略中需要提醒上课/到课时，需要获取'

    const execute = async (params: RagToolExecuteParams) => {
      const currentTime = await DataService.getCurrentTime(params.chatId)

      const courseMap: Record<number, string> = {
        1: '四点共振',
        2: '双线和一',
        3: '抄底先锋',
        4: '趋势拐点',
        5:'优中选优',
        6:'卖点'
      }
      const results: string[] = []

      // 昨日课程回放链接
      const yesterdayDay = currentTime.day - 1
      if (yesterdayDay >= 1 && yesterdayDay <= 4) {
        const yesterdayLink = await DataService.getCourseLinkByChatId(params.chatId, yesterdayDay)
        if (yesterdayLink) {
          results.push(`昨日课程（${courseMap[yesterdayDay]}）回放链接：${yesterdayLink}`)
        }
      }

      // 今日课程链接
      if (currentTime.day >= 1 && currentTime.day <= 6) {
        const todayLink = await DataService.getCourseLinkByChatId(params.chatId, currentTime.day)
        if (todayLink) {
          results.push(`今日课程（${courseMap[currentTime.day]}）直播/回放链接：${todayLink}`)
        }
      }

      // 明日课程链接
      const tomorrowDay = currentTime.day + 1
      if (tomorrowDay >= 1 && tomorrowDay <= 6) {
        const tomorrowLink = await DataService.getCourseLinkByChatId(params.chatId, tomorrowDay)
        if (tomorrowLink) {
          results.push(`明日课程（${courseMap[tomorrowDay]}）直播链接：${tomorrowLink}`)
        }
      }

      return results.join('\n')
    }

    return {
      name: name,
      description: description,
      execute: execute
    } as IKnowledgeRagTool
  }


  private static async getSearchKeyConceptsTool() {
    const name = KnowledgeRagTool.SearchKeyConcepts
    const description = '用于搜索课程关键概念（关键概念有：冥想入门营，冥想系统班），入参：需要搜索的课程概念(只能搜索已列举的概念)'

    return {
      name: name,
      description: description,
      // execute: execute
    } as IKnowledgeRagTool
  }

}