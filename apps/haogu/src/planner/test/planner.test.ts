//
// import { UUID } from '../../../../../lib/uuid/uuid'
// import { Planner } from '../plan/planner'
// import { Config } from '../../../../../config/config'
//
// describe('Test', function () {
//   beforeAll(() => {
//
//   })
//
//   it('全流程测试', async () => {
//     // Detect -> buildContext -> 触发 planner 生成 -> 沉默激活 -> 选择时间发送
//     // 跑五个人看看效果
//
//     // 聊天记录 -> 生成 plan -> 执行一条 plan -> 如果有 plan 模拟沉默（把 rest 生成 SOP 发送)
//   })
//
//   it('generatePlan', async () => {
//     process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
//     process.env.LANGCHAIN_TRACING_V2 = 'true'
//     process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
//
//     const plan = await Planner.generatePlan('7881302383130009_1688856297674945', UUID.v4(), '')
//
//     console.log(plan)
//   }, 1E8)
//
//   // it('context', async () => {
//   //   const plannerContext = await PlannerContext.build('7881302383130009_1688856297674945', '老师你好', UUID.v4())
//   //
//   //   console.log(plannerContext)
//   // }, 30000)
//
//   it('knowledge tool calls', async () => {
//     console.log(await PlannerContext.getPlannerRAG('7881302383130009_1688856297674945', '老师你好', UUID.v4()))
//   }, 30000)
//
//   it('销售小记', async () => {
//     const salesNote =  await PlannerContext.getSalesNote('7881302383130009_1688856297674945', UUID.v4())
//
//     console.log(salesNote)
//   }, 30000)
//
//   it('循环依赖', async () => {
//     console.log(new Date('2025-08-07 08:10:00').toLocaleString())
//   }, 30000)
// })