// import { WorkFlow } from '../../flow/flow'
// import { ChatHistoryService } from '../../chat_history/chat_history'
// import { Config } from '../../../../../config/config'
// import { loadConfigByWxId } from '../../../../../../test/tools/load_config'
// import { getUserId } from '../../../../../config/chat_id'
// import { registerSilentReAskTasks, startSilentReAskSystem } from '../../schedule/silent_reask_tasks'
//
// describe('Test', function () {
//   beforeAll(() => {
//
//   })
//
//   it('全流程简单测试', async () => {
//     const userMessages = await ChatHistoryService.getUserMessages('7881303189944040_1688857949631398')
//     Config.setting.wechatConfig = await loadConfigByWxId('1688857949631398')
//     const chat_id = '7881303189944040_1688857949631398'
//     const user_id = getUserId(chat_id)
//
//     startSilentReAskSystem()
//
//     for (const userMessage of userMessages) {
//       await WorkFlow.step(chat_id, user_id, userMessage)
//     }
//   }, 1E8)
// })