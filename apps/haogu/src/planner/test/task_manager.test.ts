//
// import { TaskStatus } from '@prisma/client'
// import { TaskManager } from '../task/task_manager'
//
// /**
//  * TaskManager 测试
//  * 验证极简任务管理的4个核心功能
//  */
// describe('TaskManager', () => {
//   const testChatId = `test_chat_${  Date.now()}`
//   const testRoundId = `test_round_${  Date.now()}`
//   const createdTaskIds: string[] = []
//
//   afterAll(async () => {
//     // 清理测试数据 - 删除所有测试创建的任务
//     if (createdTaskIds.length > 0) {
//       const { PrismaMongoClient } = await import('../../../../../model/mongodb/prisma')
//       const prismaClient = PrismaMongoClient.getInstance()
//
//       await prismaClient.task.deleteMany({
//         where: {
//           id: { in: createdTaskIds }
//         }
//       })
//     }
//   })
//
//   test('1. 批量创建任务', async () => {
//     const descriptions = ['写方案', '校对', '发送']
//     const overallGoal = '完成报价'
//
//     const result = await TaskManager.createTasks(
//       testChatId,
//       descriptions.map((d) => ({ description: d })),
//       overallGoal,
//       testRoundId
//     )
//
//     expect(result.count).toBe(3)
//
//     // 验证任务是否正确创建
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(3)
//
//     // 保存任务ID用于清理
//     createdTaskIds.push(...activeTasks.map((task) => task.id))
//
//     // 验证任务内容
//     expect(activeTasks[0].description).toBe('写方案')
//     expect(activeTasks[0].overall_goal).toBe(overallGoal)
//     expect(activeTasks[0].round_id).toBe(testRoundId)
//     expect(activeTasks[0].status).toBe(TaskStatus.TODO)
//     expect(activeTasks[0].priority).toBe(0)
//
//     expect(activeTasks[1].description).toBe('校对')
//     expect(activeTasks[1].priority).toBe(1)
//
//     expect(activeTasks[2].description).toBe('发送')
//     expect(activeTasks[2].priority).toBe(2)
//   })
//
//   test('2. 获取活跃任务（按优先级和创建时间排序）', async () => {
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//
//     expect(activeTasks).toHaveLength(3)
//
//     // 验证排序：优先级 ASC, 创建时间 ASC
//     expect(activeTasks[0].priority).toBeLessThanOrEqual(activeTasks[1].priority)
//     expect(activeTasks[1].priority).toBeLessThanOrEqual(activeTasks[2].priority)
//
//     // 验证只返回活跃任务（TODO + DOING）
//     activeTasks.forEach((task) => {
//       expect([TaskStatus.TODO, TaskStatus.DOING]).toContain(task.status)
//     })
//   })
//
//   test('3. 更新任务状态', async () => {
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     const firstTask = activeTasks[0]
//
//     // 更新为 DOING
//     await TaskManager.updateStatus(firstTask.id, TaskStatus.DOING)
//
//     // 验证状态更新 - 使用 getTaskById 因为 DOING 状态的任务不在 activeTasks 中
//     const updatedTask = await TaskManager.getTaskById(firstTask.id)
//     expect(updatedTask?.status).toBe(TaskStatus.DOING)
//     expect(updatedTask?.completed_at).toBeNull()
//
//     // 验证活跃任务数量减少了1
//     const updatedActiveTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(updatedActiveTasks).toHaveLength(2)
//
//     // 更新为 DONE
//     await TaskManager.updateStatus(firstTask.id, TaskStatus.DONE)
//
//     // 验证任务已完成且不在活跃任务中
//     const finalActiveTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(finalActiveTasks).toHaveLength(2)
//     expect(finalActiveTasks.find((task) => task.id === firstTask.id)).toBeUndefined()
//   })
//
//   test('4. 更新任务优先级', async () => {
//     // 创建一个紧急任务
//     await TaskManager.createTasks(
//       testChatId,
//       [{ description: '1' }],
//       '完成报价',
//       testRoundId
//     )
//
//     let activeTasks = await TaskManager.getActiveTasks(testChatId)
//     const urgentTask = activeTasks.find((task) => task.description === '紧急跟进')
//     expect(urgentTask).toBeDefined()
//
//     // 保存任务ID用于清理
//     if (urgentTask) {
//       createdTaskIds.push(urgentTask.id)
//     }
//
//     // 设置为最高优先级
//     await TaskManager.updatePriority(urgentTask!.id, -10)
//
//     // 验证优先级更新和排序
//     activeTasks = await TaskManager.getActiveTasks(testChatId)
//     const updatedUrgentTask = activeTasks.find((task) => task.id === urgentTask!.id)
//     expect(updatedUrgentTask?.priority).toBe(-10)
//
//     // 验证紧急任务现在排在第一位
//     expect(activeTasks[0].id).toBe(urgentTask!.id)
//   })
//
//   test('5. 取消任务（软删除）', async () => {
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     const taskToCancel = activeTasks[activeTasks.length - 1]
//
//     // 取消任务
//     await TaskManager.cancelTask(taskToCancel.id)
//
//     // 验证任务不再出现在活跃任务中
//     const remainingActiveTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(remainingActiveTasks.find((task) => task.id === taskToCancel.id)).toBeUndefined()
//     expect(remainingActiveTasks).toHaveLength(activeTasks.length - 1)
//   })
//
//
//   it('', async () => {
//     const triggerTime = 1754275003970 + 366916031
//
//     console.log(new Date(triggerTime).toLocaleString())
//   }, 30000)
// })
