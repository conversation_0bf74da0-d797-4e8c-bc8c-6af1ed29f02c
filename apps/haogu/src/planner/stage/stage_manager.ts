import logger from 'model/logger/logger'
import { DateHelper } from 'lib/date/date'
import { DataService } from '../../workflow/helper/get_data'
import { StagePrompt } from './stage_types'

export class StageManager {
  public async getStageInformation(chatId: string): Promise<string> {
    const stagePromptData = this.getBriefStagePromptData()
    return await this.getStagePrompt(chatId, stagePromptData)
  }

  private getBriefStagePromptData(): StagePrompt {
    return {
      preCourse: '客户还没开始上课',
      firstCourse: '现在老师正在上第一课四点共振（市场合力突破的买点）',
      firstCourseFinishInDay1: '第一课已经上完了，明晚开始第二课双线合一',
      firstCourseFinishInDay2: '第一课已经上完了，今晚开始第二课双线合一',
      secondCourse: '现在是第二课，老师正在讲解双线和一（上涨加速前低吸的买点）',
      secondCourseFinishInDay2: '第二课双线合一已经上完了，明晚开始第三课抄底先锋',
      secondCourseFinishInDay3: '第二课双线合一已经上完了，今晚开始第三课抄底先锋',
      thirdCourse: '现在是第三课，老师正在讲解抄底先锋（利用散户割肉的恐慌选股）',
      thirdCourseFinishInDay3: '第三课抄底先锋已经上完了，明晚开始第四课趋势拐点',
      thirdCourseFinishInDay4: '第三课抄底先锋已经上完了，今晚开始第四课趋势拐点',
      fourthCourse: '现在是第四课，老师正在讲解趋势拐点（利用主力黄金坑提高胜率），天天老师推出9.9团购优惠券，提到了后面还有半年的实战训练营，并表示只有购买9.9优惠券才能听第六节课',
      fourthCourseFinishInDay4: '第四课趋势拐点已经上完了，明晚开始第五课优中选优',
      fourthCourseFinishInDay5: '第四课趋势拐点已经上完了，明晚开始第四课优中选优',
      fifthCourse: '现在是第五课，老师正在讲解优中选优（选择最具潜力的股票），再次提到后面有半年的实战训练营，买了团购优惠券后可以听第六节课并享受团购价',
      fifthCourseFinishInDay5: '第五课优中选优已经上完了，明晚开始第六课卖点',
      fifthCourseFinishInDay6: '第五课优中选优已经上完了，明晚开始第六课卖点',
      sixthCourse: '现在是第六课，老师正在讲解卖点（高纬度无延迟筹码峰用法）',
      sixthCourseFinish: '第六课卖点已经结束了，课程全部完成',
      postCourseWeek: '好人好股6天开悟之旅已经结束了',
    }
  }

  private async getStagePrompt(chatId: string, prompts: StagePrompt): Promise<string> {
    try {
      const stagePromptPreContext: string = '## 阶段背景'
      const result = await this.chooseStagePromptByRule(chatId, prompts)

      return result ? `${stagePromptPreContext}\n${result}` : ''
    } catch (e) {
      logger.error('阶段 Prompt 获取失败', e)
      return ''
    }
  }

  private async chooseStagePromptByRule(chatId: string, prompts: StagePrompt): Promise<string> {
    const currentTime = await DataService.getCurrentTime(chatId)

    if (!currentTime.is_course_day) {
      return prompts.preCourse
    } else {
      switch (currentTime.day) {
        case 1:
          if (DateHelper.isTimeBefore(currentTime.time, '19:20:00')) {
            return prompts.preCourse
          } else if (DateHelper.isTimeBefore(currentTime.time, '20:44:00')) {
            return prompts.firstCourse
          } else {
            return prompts.firstCourseFinishInDay1
          }
        case 2:
          if (DateHelper.isTimeBefore(currentTime.time, '19:20:00')) {
            return prompts.firstCourseFinishInDay2
          } else if (DateHelper.isTimeBefore(currentTime.time, '20:50:00')) {
            return prompts.secondCourse
          } else {
            return prompts.secondCourseFinishInDay2
          }
        case 3:
          if (DateHelper.isTimeBefore(currentTime.time, '19:20:00')) {
            return prompts.secondCourseFinishInDay3
          } else if (DateHelper.isTimeBefore(currentTime.time, '20:53:00')) {
            return prompts.thirdCourse
          } else {
            return prompts.thirdCourseFinishInDay3
          }
        case 4:
          if (DateHelper.isTimeBefore(currentTime.time, '19:20:00')) {
            return prompts.thirdCourseFinishInDay4
          } else if (DateHelper.isTimeBefore(currentTime.time, '20:55:00')) {
            return prompts.fourthCourse
          } else {
            return prompts.fourthCourseFinishInDay4
          }
        case 5:
          if (DateHelper.isTimeBefore(currentTime.time, '19:20:00')) {
            return prompts.fourthCourseFinishInDay5
          } else if (DateHelper.isTimeBefore(currentTime.time, '20:54:00')) {
            return prompts.fifthCourse
          } else {
            return prompts.fifthCourseFinishInDay5
          }
        case 6:
          if (DateHelper.isTimeBefore(currentTime.time, '19:20:00')) {
            return prompts.fifthCourseFinishInDay6
          } else if (DateHelper.isTimeBefore(currentTime.time, '20:52:00')) {
            return prompts.sixthCourse
          } else {
            return prompts.sixthCourseFinish
          }
        default:
          return prompts.postCourseWeek
      }
    }
  }
}