import { Haogu<PERSON><PERSON> } from 'model/haogu/crm/client'
import HaoGuLiveAP<PERSON> from 'model/haogu/live/client'

const baseUrl = 'https://ddm.integrity.com.cn/v1/api/ke'
const appId = '7k3p9x2d5s8f1v6j'
const appSecret = 'KjRoLgkNCXxOCJBdjy3SuuK16i5gPi86mVauKWTvnJU='

export const haoguLiveAPI = new HaoGuLiveAPI(baseUrl, appId, appSecret)

const haoguScrmBaseUrl = 'https://wecom-api.integrity.com.cn'
// const haoguScrmBaseUrl = 'http://47.97.123.214:54001'
const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCeF5P8b+0oaf1ukVPYfKGHDv+c51qUz+hrn23OEuXxSVbai+iEVJhn9YOmpLBjYeTaYje4u32j7uu87Bv+1E455e5Sat9Qs/DHeYbaTA/geojL6PgTLo/CMmlTP7Ac7oF4UQ3CmQY3M/OOVtA0Pje+fa1z+242oeX0WyQNYY0PiQIDAQAB'

export const haoguScrmApi = new HaoguApi(haoguScrmBaseUrl, publicKey)
