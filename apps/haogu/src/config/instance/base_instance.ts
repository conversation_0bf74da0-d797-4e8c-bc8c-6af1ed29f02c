import { ChatHistoryService } from 'service/chat_history/chat_history'
import { ChatDB, IChat } from 'service/database/chat'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { PrismaMongoClient } from '../../database/prisma'

export interface HaoguChat extends IChat{
  conversation_id: string
  customer_unified_user_id: string
  wx_union_id: string
}


export const chatDBClient = new ChatDB<HaoguChat>(PrismaMongoClient.getCommonInstance())
export const chatStateStoreClient = new ChatStateStore(chatDBClient)
export const chatHistoryServiceClient = new ChatHistoryService(PrismaMongoClient.getCommonInstance(), chatStateStoreClient)