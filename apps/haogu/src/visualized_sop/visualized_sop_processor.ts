import { LLM } from 'lib/ai/llm/llm_model'
import { LLMReply } from 'service/llm/llm_reply'
import { getState } from 'service/llm/state'
import { HandleActionOption, VisualizedSopProcessor } from 'service/visualized_sop/visualized_sop_processor'
import { ContentDynamicPrompt, SopScheduleTime } from 'service/visualized_sop/visualized_sop_type'
import { actionCustomMap, conditionJudgeMap, linkSourceVariableTagMap, textVariableMap } from './visualized_sop_variable'
import { XMLHelper } from 'lib/xml/xml'
import { ContextBuilder } from '../workflow/context'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'
import { PrismaMongoClient } from '../database/prisma'
import dayjs from 'dayjs'
import { commonMessageSender } from '../config/instance/send_message_instance'

export class HaoguVisualizedSopProcessor extends VisualizedSopProcessor {
  async handleActionDynamicPrompt(chatId: string, userId: string, action: ContentDynamicPrompt, opt: HandleActionOption): Promise<void> {
    const state = await getState(chatId, userId)
    const context = await ContextBuilder.build({
      state,
      customPrompt: action.customPrompt == '' ? '# 动态SOP\n你需要根据任务与客户画像来生成SOP话术，如果没有称呼，就叫老板' : '',
      talkStrategyPrompt: action.dynamicPrompt,
      customerChatRounds: action.chatHistoryRounds,

      retrievedKnowledge: action.includeRAG,
      customerMemory: action.includeMemory,
      customerBehavior: action.includeUserBehavior,
      customerPortrait: action.includeUserSlots,
      temporalInformation: action.includeTimeInfo,
    })

    const llm = new LLM({
      maxTokens: 400,
      projectName: 'haogu',
      promptName: `sop_${action.description}`,
      meta: {
        chat_id: state.chat_id,
        round_id: state.round_id,
        promptName: `sop_${action.description}`,
      },
    })
    const res = await llm.predictMessage(context)
    if (action.noSplit) {
      await commonMessageSender.sendText(chatId, {
        text: res,
        description: action.description
      }, {
        sopId:opt.sop_id,
        roundId:state.round_id,
        force:opt.force
      })
    } else {
      const splitSentence = LLMReply.splitIntoSentencesWithMaxSentences(res, 2)
      await commonMessageSender.sendMsg(chatId, splitSentence.map((item) => ({
        type: SendMessageType.text,
        text: item,
        description: action.description
      })), {
        sopId:opt.sop_id,
        roundId:state.round_id,
        force:opt.force
      })
    }
  }

  async judgeDynamicCondition(chatId: string, userId: string, condition: string): Promise<boolean> {
    const state = await getState(chatId, userId, '')
    const context = await ContextBuilder.build({
      state,
      customerMemory: true,
      talkStrategyPrompt: condition,
      customerChatRounds: 6,
      injectChatHistory: true,
      customPrompt: HaoguVisualizedSopProcessor.getThinkPrompt(),
    })
    const result = await LLM.predictMessage(context, { projectName:'yuhe', promptName:'think_condition', maxTokens:1000, meta:{ chatId:chatId, promptName:'think_condition'  } })
    const judge = XMLHelper.extractContent(result, 'judge') || ''
    return judge == 'true'
  }

  private static getThinkPrompt() {
    return `# 角色设定
- 你是顶级销售，擅长根据对话记录，客户行为和客户画像，判断条件是否是正确的

## 主要任务
- 思考（think）总结聊天记录，判断是否和条件内容相关，结合客户行为和客户画像判断条件是否正确
- 判断（judge）只输出"true"或"false",条件正确则输出"true",条件错误则输出"false"

## 格式要求
- 请先将思考输出到 <think></think> 标签中
- 然后将判断输出到 <judge></judge> 标签中`
  }

  getActionCustomMap(): Record<string, (params: { chatId: string; userId: string;opt:HandleActionOption }) => Promise<void>> {
    return actionCustomMap
  }
  getConditionJudgeMap(): Record<string, ((params: { chatId: string; userId: string; }) => Promise<boolean>)> {
    return conditionJudgeMap
  }
  getLinkSourceVariableTagMap(): Record<string, (params: { chatId: string; userId: string; }) => Promise<string>> {
    return linkSourceVariableTagMap
  }
  getTextVariableMap(): Record<string, (params: { chatId: string; userId: string; }) => Promise<string>> {
    return textVariableMap
  }
}

export async function calTaskTime(time: SopScheduleTime, chat_id: string): Promise<Date | undefined> {
  const mongoClient = PrismaMongoClient.getInstance()
  const chatInfo = await mongoClient.chat.findUnique({ where:{
    id:chat_id
  }, select:{
    course_no:true
  } })

  if (!chatInfo?.course_no) {
    return undefined
  }
  const day0 = dayjs(String(chatInfo.course_no)).hour(0).minute(0).second(0)
  const timeStringSplited = time.time.split(':')
  return day0.add(time.week * 7 + time.day, 'day').hour(Number(timeStringSplited[0])).minute(Number(timeStringSplited[1])).second(Number(timeStringSplited[2])).toDate()
}