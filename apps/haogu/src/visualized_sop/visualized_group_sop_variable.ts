export const groupConditionJudgeMap:Record<string, ((params:{groupId:string})=>Promise<boolean>)> = {
}

export const groupTextVariableMap:Record<string, (params:{groupId:string})=> Promise<string>> = {
}

export const groupActionCustomMap:Record<string, (params:{groupId:string})=> Promise<void>> = {
}

export const groupLinkSourceVariableTagMap:Record<string, (params:{groupId:string})=>Promise<string>> = {
}
