import { HandleActionOption } from 'service/visualized_sop/visualized_sop_processor'
import { DataService } from '../workflow/helper/get_data'
import { HWLink } from '../config/homework_link'

export const conditionJudgeMap:Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)> = {
  '第一节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 1)
  },
  '第一节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 1)
  },
  '第二节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 2)
  },
  '第二节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 2)
  },
  '第三节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 3)
  },
  '第三节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 3)
  },
  '第四节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 4)
  },
  '第四节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 4)
  },
  '第五节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 5)
  },
  '第五节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 5)
  },
  '第六节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 6)
  },
  '第六节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 6)
  }
}

export const textVariableMap:Record<string, (params:{chatId:string;userId:string})=> Promise<string>> = {
  '用户昵称': async({ chatId:string }) => {
    return '同学'
  },
  '第一节课直播链接': async({ chatId }) => {
    return await DataService.getCourseLinkByChatId(chatId, 1) ?? ''
  },
  '第二节课直播链接': async({ chatId }) => {
    return await DataService.getCourseLinkByChatId(chatId, 2) ?? ''
  },
  '第三节课直播链接': async({ chatId }) => {
    return await DataService.getCourseLinkByChatId(chatId, 3) ?? ''
  },
  '第四节课直播链接': async({ chatId }) => {
    return await DataService.getCourseLinkByChatId(chatId, 4) ?? ''
  },
  '第五节课直播链接': async({ chatId }) => {
    return await DataService.getCourseLinkByChatId(chatId, 5) ?? ''
  },
  '第六节课直播链接': async({ chatId }) => {
    return await DataService.getCourseLinkByChatId(chatId, 6) ?? ''
  },
  '第一节课后作业链接': async() => {
    return HWLink.day1
  },
  '第二节课后作业链接': async() => {
    return HWLink.day2
  },
  '第三节课后作业链接': async() => {
    return HWLink.day3
  },
  '第四节课后作业链接': async() => {
    return HWLink.day4
  },
  '第五节课后作业链接': async() => {
    return HWLink.day5
  },
  '第六节课后作业链接': async() => {
    return HWLink.day6
  },
}

export const actionCustomMap:Record<string, (params:{chatId:string;userId:string;opt:HandleActionOption})=> Promise<void>> = {
}

export const linkSourceVariableTagMap:Record<string, (params:{chatId:string;userId:string})=>Promise<string>> = {
}
