import { PrismaClient } from '../../prisma_client'
import { PrismaClient as PrismaCommonClient } from 'model/prisma_client'
import { PrismaMongoClient as PrismaMongoCommonClient } from 'model/mongodb/prisma'

export class PrismaMongoClient {
  private static instance?: PrismaClient
  private static commonInstance?:PrismaCommonClient

  public static getInstance(): PrismaClient {
    if (!PrismaMongoClient.instance) {
      PrismaMongoClient.instance = new PrismaClient()
    }
    return PrismaMongoClient.instance
  }

  public static getCommonInstance(): PrismaCommonClient {
    if (!PrismaMongoClient.commonInstance) {
      PrismaMongoClient.commonInstance = PrismaMongoCommonClient.newInstance('haogu')
    }
    return PrismaMongoClient.commonInstance
  }
}