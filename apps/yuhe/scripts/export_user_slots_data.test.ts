import { writeFileSync } from 'node:fs'
import { contentWithFrequency } from 'service/local_cache/type'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { PrismaMongoClient } from '../database/prisma'

describe('导出客户画像', () => {
  jest.setTimeout(60000000)
  test('导出第x期客户画像', async() => {
    const startCourseNo = 20250501
    const endCourseNo = 20250512
    const mongoClient = PrismaMongoClient.getInstance()
    const users = await mongoClient.chat.findMany({ where:{ course_no:{ gte:startCourseNo, lte:endCourseNo } } })
    let text = '昵称,性别,年龄,行业,店面类型,是否抖音在做,年营业额,所在城市\n'
    for (const user of users) {
      const name = user.contact.wx_name
      const userSlots = UserSlots.fromRecord(user.chat_state.userSlots as Record<string, contentWithFrequency>)
      const sex = userSlots.getSubTopicContent('基本信息', '年龄')
      const age = userSlots.getSubTopicContent('基本信息', '性别')
      const industry = userSlots.getSubTopicContent('基本信息', '行业类目')
      const revenue = userSlots.getSubTopicContent('基本信息', '年营业额')
      const shopType = userSlots.getSubTopicContent('基本信息', '店面类别')
      const isDouYinWork = userSlots.getSubTopicContent('抖音运营状态', '是否抖音在做')
      const city = userSlots.getSubTopicContent('基本信息', '所在城市')
      text += `${name},${sex},${age},${industry},${shopType},${isDouYinWork},${revenue},${city}\n`
    }
    writeFileSync('./info.csv', text)
  })

  test('导出第x期流量博士客户画像', async() => {
    const startCourseNo = 20250822
    const endCourseNo = 20250823
    const mongoClient = PrismaMongoClient.getInstance()

    // 指定的_id包含的字符串列表
    const targetIdParts = [
      '1688854467774270',
      '1688854707746016',
      '1688854952697121',
      '1688856296731952',
      '1688856090660659'
    ]

    const users = await mongoClient.chat.findMany({
      where: {
        course_no: { gte: startCourseNo, lte: endCourseNo },
        OR: targetIdParts.map((idPart) => ({
          id: { contains: idPart }
        }))
      }
    })
    let text = '昵称,性别,年龄,行业,店面类型,是否抖音在做,年营业额,所在城市\n'
    for (const user of users) {
      const name = user.contact.wx_name
      const userSlots = UserSlots.fromRecord(user.chat_state.userSlots as Record<string, contentWithFrequency>)
      const sex = userSlots.getSubTopicContent('基本信息', '年龄')
      const age = userSlots.getSubTopicContent('基本信息', '性别')
      const industry = userSlots.getSubTopicContent('基本信息', '行业类目')
      const revenue = userSlots.getSubTopicContent('基本信息', '年营业额')
      const shopType = userSlots.getSubTopicContent('基本信息', '店面类别')
      const isDouYinWork = userSlots.getSubTopicContent('抖音运营状态', '是否抖音在做')
      const city = userSlots.getSubTopicContent('基本信息', '所在城市')
      text += `${name},${sex},${age},${industry},${shopType},${isDouYinWork},${revenue},${city}\n`
    }
    writeFileSync('./info.csv', text)
  })
})