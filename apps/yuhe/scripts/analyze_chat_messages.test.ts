import { DataService } from '../helper/getter/get_data'
import { XingyanAPI } from 'model/xingyan/api'
import { IRoomMessage } from 'model/xingyan/type'
import * as fs from 'fs'
import * as path from 'path'
import { MemoryStore } from 'service/memory/memory_store'
import { ExtractUserSlots } from '../user_slots/user_slots_extraction'
import { UUID } from 'lib/uuid/uuid'
import { memoryStoreClient } from '../service/instance'

interface CourseData {
  courseNo: number
  days: {
    day: number
    liveId: number
    liveName: string
    userMessages: {
      phone: string
      userName: string
      messages: IRoomMessage[]
    }[]
  }[]
}

interface AnalysisResult {
  courseNo: number
  userAnalysis: {
    phone: string
    userName: string
    messagesByDay: {
      day: number
      messageCount: number
      messages: IRoomMessage[]
    }[]
    totalMessages: number
  }[]
  dayAnalysis: {
    day: number
    totalMessages: number
    uniqueUsers: number
  }[]
}

async function main() {
  try {
    console.log('Starting chat message analysis...')

    // Course numbers to analyze
    const courseNos = [20250506, 20250507, 20250508, 20250509]
    const results: AnalysisResult[] = []

    for (const courseNo of courseNos) {
      console.log(`Processing course ${courseNo}...`)

      // Get course info (live IDs for days 1-4)
      const courseInfo = await DataService.getCourseInfoByCourseNo(courseNo)
      if (!courseInfo) {
        console.log(`No course info found for course ${courseNo}`)
        continue
      }

      // Get all users for this course
      const chats = await DataService.getChatsByCourseNo(courseNo)
      console.log(`Found ${chats.length} users for course ${courseNo}`)

      const courseData: CourseData = {
        courseNo,
        days: []
      }

      // For each day (1-4)
      for (const info of courseInfo) {
        console.log(`Processing day ${info.day} (Live ID: ${info.liveId})...`)

        const dayData = {
          day: info.day,
          liveId: info.liveId,
          liveName: info.liveName,
          userMessages: [] as CourseData['days'][0]['userMessages']
        }

        // For each user
        for (const chat of chats) {
          if (!chat.phone) {
            console.log(`User ${chat.contact.wx_name} has no phone number, skipping...`)
            continue
          }

          // Get chat messages for this user in this live session
          const messages = await XingyanAPI.getRoomMsgPage(
            info.liveId.toString(),
            chat.phone
          )

          if (messages && messages.length > 0) {
            dayData.userMessages.push({
              phone: chat.phone,
              userName: chat.contact.wx_name,
              messages: messages
            })
            console.log(`Found ${messages.length} messages for user ${chat.contact.wx_name} on day ${info.day}`)
          }
        }

        courseData.days.push(dayData)
      }

      // Analyze the data
      const analysisResult = analyzeData(courseData)
      results.push(analysisResult)

      // Save raw data
      const rawDataDir = path.join(__dirname, 'output')
      if (!fs.existsSync(rawDataDir)) {
        fs.mkdirSync(rawDataDir, { recursive: true })
      }

      fs.writeFileSync(
        path.join(rawDataDir, `course_${courseNo}_raw_data.json`),
        JSON.stringify(courseData, null, 2)
      )
    }

    // Save analysis results
    const outputDir = path.join(__dirname, 'output')
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    // Save overall analysis
    fs.writeFileSync(
      path.join(outputDir, 'chat_analysis_results.json'),
      JSON.stringify(results, null, 2)
    )

    // Generate CSV reports
    generateCSVReports(results, outputDir)

    console.log('Analysis complete! Results saved to the output directory.')
  } catch (error) {
    console.error('Error in analysis:', error)
  }
}

function analyzeData(courseData: CourseData): AnalysisResult {
  const result: AnalysisResult = {
    courseNo: courseData.courseNo,
    userAnalysis: [],
    dayAnalysis: []
  }

  // Create a map of users and their messages by day
  const userMap = new Map<string, {
    phone: string
    userName: string
    messagesByDay: Map<number, IRoomMessage[]>
  }>()

  // Process each day's data
  for (const day of courseData.days) {
    let dayTotalMessages = 0
    const uniqueUsersForDay = new Set<string>()

    for (const userData of day.userMessages) {
      // Add user to map if not exists
      if (!userMap.has(userData.phone)) {
        userMap.set(userData.phone, {
          phone: userData.phone,
          userName: userData.userName,
          messagesByDay: new Map()
        })
      }

      // Add messages for this day
      const user = userMap.get(userData.phone)!
      user.messagesByDay.set(day.day, userData.messages)

      // Update day statistics
      dayTotalMessages += userData.messages.length
      uniqueUsersForDay.add(userData.phone)
    }

    // Add day analysis
    result.dayAnalysis.push({
      day: day.day,
      totalMessages: dayTotalMessages,
      uniqueUsers: uniqueUsersForDay.size
    })
  }

  // Convert user map to array for the result
  for (const [_, userData] of userMap.entries()) {
    const messagesByDay: AnalysisResult['userAnalysis'][0]['messagesByDay'] = []
    let totalMessages = 0

    // Add data for each day (1-4)
    for (let day = 1; day <= 4; day++) {
      const messages = userData.messagesByDay.get(day) || []
      messagesByDay.push({
        day,
        messageCount: messages.length,
        messages
      })
      totalMessages += messages.length
    }

    result.userAnalysis.push({
      phone: userData.phone,
      userName: userData.userName,
      messagesByDay,
      totalMessages
    })
  }

  // Sort users by total message count (descending)
  result.userAnalysis.sort((a, b) => b.totalMessages - a.totalMessages)

  return result
}

function generateCSVReports(results: AnalysisResult[], outputDir: string) {
  // Generate user activity report
  let userCsv = 'Course,Phone,UserName,Day1Messages,Day2Messages,Day3Messages,Day4Messages,TotalMessages\n'

  for (const result of results) {
    for (const user of result.userAnalysis) {
      const day1 = user.messagesByDay.find((d) => d.day === 1)?.messageCount || 0
      const day2 = user.messagesByDay.find((d) => d.day === 2)?.messageCount || 0
      const day3 = user.messagesByDay.find((d) => d.day === 3)?.messageCount || 0
      const day4 = user.messagesByDay.find((d) => d.day === 4)?.messageCount || 0

      userCsv += `${result.courseNo},${user.phone},"${user.userName}",${day1},${day2},${day3},${day4},${user.totalMessages}\n`
    }
  }

  fs.writeFileSync(path.join(outputDir, 'user_activity_report.csv'), userCsv)

  // Generate day activity report
  let dayCsv = 'Course,Day,TotalMessages,UniqueUsers\n'

  for (const result of results) {
    for (const day of result.dayAnalysis) {
      dayCsv += `${result.courseNo},${day.day},${day.totalMessages},${day.uniqueUsers}\n`
    }
  }

  fs.writeFileSync(path.join(outputDir, 'day_activity_report.csv'), dayCsv)

  // Generate message content report (limited to keep file size manageable)
  let messageCsv = 'Course,Day,Phone,UserName,MessageTime,MessageContent\n'

  for (const result of results) {
    for (const user of result.userAnalysis) {
      for (const dayData of user.messagesByDay) {
        // Limit to first 100 messages per user per day to keep file size manageable
        const messages = dayData.messages.slice(0, 100)

        for (const msg of messages) {
          // Escape quotes and newlines in content
          const content = msg.content.replace(/"/g, '""').replace(/\n/g, ' ')

          messageCsv += `${result.courseNo},${dayData.day},${user.phone},"${user.userName}","${msg.msgTime}","${content}"\n`
        }
      }
    }
  }

  fs.writeFileSync(path.join(outputDir, 'message_content_report.csv'), messageCsv)
}


describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    await main()
  }, 1E8)

  it('12312321', async () => {
    await memoryStoreClient.extractMemoriesAndUserSlots('local_7881301441024893', UUID.short(), new ExtractUserSlots())
  }, 60000)
})
