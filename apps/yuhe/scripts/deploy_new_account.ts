#!/usr/bin/env ts-node
/**
 * 交互式部署新账号脚本
 *
 * 功能：
 * - 交互式选择要部署的新账号
 * - 自动生成数据库配置和Docker配置
 * - 自动提交代码并调用部署脚本
 */

import inquirer from 'inquirer'
import { execSync } from 'child_process'
import { JuziAPI } from 'model/juzi/api'
import { FileHelper } from 'lib/file'
import * as path from 'path'
import axios from 'axios'
import chalk from 'chalk'
import { PrismaMongoClient } from 'model/mongodb/prisma'

// ===== 配置变量 =====
const ENTERPRISE_NAME = 'yuhe'
const ORG_TOKEN = '6801c818f8b463b1d228f23d'
const NOTIFY_GROUP_ID = 'R:10815051791863856'
const DOCKER_COMPOSE_PATH = path.join(process.cwd(), 'apps', 'yuhe', 'docker', 'docker-compose.yaml')
const CLEAR_CACHE_URL = 'http://*************:6001/api/clear-server-address-cache'

// ===== 工具函数 =====
function log(message: string, color: 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'gray' = 'cyan') {
  console.log(chalk[color](`[${new Date().toLocaleTimeString()}] ${message}`))
}

function runCommand(command: string) {
  log(`执行命令: ${command}`, 'gray')
  try {
    execSync(command, { stdio: 'inherit' })
  } catch (error) {
    log(`命令执行失败: ${command}`, 'red')
    log(`错误详情: ${error instanceof Error ? error.message : error}`, 'red')
    process.exit(1)
  }
}

// ===== 获取新账号信息 =====
async function getNewAccounts() {
  log('正在获取新账号信息...', 'blue')

  try {
    // 获取所有微信账号
    const res = await JuziAPI.listAccounts(ORG_TOKEN)
    const accounts = res.data

    // 获取已配置的账号
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany({
      where: {
        enterpriseName: ENTERPRISE_NAME
      }
    })

    // 找出未配置的新账号
    const newAccounts: any[] = []
    for (const account of accounts) {
      if (!account.wxid) {
        continue
      }

      const existingConfig = configs.find((config) => config.wechatId === account.wxid)
      if (!existingConfig) {
        newAccounts.push(account)
      }
    }

    log(`发现 ${newAccounts.length} 个新账号`, 'green')
    return newAccounts
  } catch (error) {
    log(`获取账号信息失败: ${error instanceof Error ? error.message : error}`, 'red')
    process.exit(1)
  }
}

// ===== 生成账号配置 =====
function generateAccountConfig(account: any, accountName: string, port: number) {
  return {
    enterpriseName: ENTERPRISE_NAME,
    accountName: accountName,
    nickName: account.nickName,
    wechatId: account.wxid,
    address: `http://***************:${port}`,
    port: port.toString(),
    botUserId: account.weixin,
    orgToken: ORG_TOKEN,
    enterpriseConfig: {
      notifyGroupId: NOTIFY_GROUP_ID,
    }
  }
}

// ===== 生成Docker配置 =====
function generateDockerConfig(accountName: string, port: number) {
  return `
  ${accountName}:
      image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/yuhe:latest
      container_name: ${accountName}
      environment:
      - NODE_ENV=dev
      - TEACHER_NAME=中神通
      - TZ=Asia/Shanghai
      - WECHAT_NAME=${accountName}
      ports:
        - "${port}:${port}"
      restart: always
      command: ["apps/yuhe/client/client_server.ts"]`
}

// ===== 获取下一个可用端口 =====
async function getNextAvailablePort(): Promise<number> {
  const configs = await PrismaMongoClient.getConfigInstance().config.findMany({
    where: {
      enterpriseName: ENTERPRISE_NAME
    }
  })

  const usedPorts = configs.map((config) => parseInt(config.port, 10)).sort((a, b) => a - b)
  // 获取最大的端口
  const maxPort = usedPorts.length > 0 ? usedPorts[usedPorts.length - 1] : 5000

  return maxPort + 1
}

// ===== 主流程 =====
async function main() {
  log('===== 开始部署新账号流程 =====', 'cyan')

  try {
    // 1. 获取新账号
    const newAccounts = await getNewAccounts()

    if (newAccounts.length === 0) {
      log('没有发现新账号，退出流程', 'yellow')
      process.exit(0)
    }

    // 2. 显示新账号信息并让客户选择
    log('发现以下新账号:', 'blue')
    newAccounts.forEach((account, index) => {
      console.log(`${index + 1}. ${account.nickName || account.wxid} (${account.wxid})`)
    })

    const { selectedAccounts } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'selectedAccounts',
        message: '请选择要部署的账号 (空格键选择，回车确认):',
        choices: newAccounts.map((account, index) => ({
          name: `${account.nickName || account.wxid} (${account.wxid})`,
          value: index
        })),
        validate(answer: number[]) {
          if (answer.length < 1) {
            return '请至少选择一个账号进行部署！'
          }
          return true
        },
      }
    ])

    const accountsToProcess = selectedAccounts.map((index: number) => newAccounts[index])
    log(`选中 ${accountsToProcess.length} 个账号进行部署`, 'green')

    // 3. 为每个选中的账号生成配置
    const configs: any[] = []
    let dockerConfigs = ''
    let currentPort = await getNextAvailablePort()

    for (const account of accountsToProcess) {
      // 生成账号名称
      const accountName = `yuhe${currentPort - 5000}`

      // 确认账号信息
      const { confirmed } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmed',
          message: `确认部署账号: ${account.nickName || account.wxid} -> ${accountName} (端口: ${currentPort})?`,
          default: true
        }
      ])

      if (!confirmed) {
        log(`跳过账号: ${account.nickName || account.wxid}`, 'yellow')
        continue
      }

      // 生成配置
      const config = generateAccountConfig(account, accountName, currentPort)
      configs.push(config)

      // 生成Docker配置
      dockerConfigs += generateDockerConfig(accountName, currentPort)

      log(`生成配置: ${accountName} (端口: ${currentPort})`, 'green')
      currentPort++
    }

    if (configs.length === 0) {
      log('没有账号被确认部署，退出流程', 'yellow')
      process.exit(0)
    }

    // 4. 保存数据库配置
    log('正在保存数据库配置...', 'blue')
    await PrismaMongoClient.getConfigInstance().config.createMany({
      data: configs
    })
    log('数据库配置保存成功', 'green')

    // 5. 更新Docker配置文件
    log('正在更新Docker配置文件...', 'blue')
    await FileHelper.appendFile(DOCKER_COMPOSE_PATH, dockerConfigs)
    log('Docker配置文件更新成功', 'green')

    // 6. 清除服务器地址缓存
    log('正在清除服务器地址缓存...', 'blue')
    try {
      const res = await axios.post(CLEAR_CACHE_URL)
      log('服务器地址缓存清除成功', 'green')
    } catch (error) {
      log(`清除缓存失败: ${error instanceof Error ? error.message : error}`, 'yellow')
    }

    // 7. 询问是否自动提交代码并部署
    const { shouldCommitAndDeploy } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'shouldCommitAndDeploy',
        message: '是否自动提交代码并执行部署？',
        default: true
      }
    ])

    if (shouldCommitAndDeploy) {
      // 提交代码
      log('正在提交代码...', 'blue')
      runCommand('git add .')
      runCommand(`git commit -m "feat: 添加新账号配置 - ${configs.map((c) => c.accountName).join(', ')}"`)
      runCommand('git pull')
      runCommand('git push')
      log('代码提交成功', 'green')

      // 执行部署
      const { shouldRunRelease } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'shouldRunRelease',
          message: '是否执行release脚本进行部署？',
          default: true
        }
      ])

      if (shouldRunRelease) {
        log('正在执行release脚本...', 'blue')
        runCommand('./release.sh')
        log('部署完成', 'green')
      }
    } else {
      log('请手动提交代码并执行部署:', 'yellow')
      log('1. git add .', 'yellow')
      log(`2. git commit -m "feat: 添加新账号配置 - ${configs.map((c) => c.accountName).join(', ')}"`, 'yellow')
      log('3. git push', 'yellow')
      log('4. ./release.sh', 'yellow')
    }

    log('===== 部署新账号流程完成 =====', 'cyan')
    log(`成功配置 ${configs.length} 个新账号:`, 'green')
    configs.forEach((config) => {
      log(`- ${config.accountName} (端口: ${config.port})`, 'green')
    })

  } catch (error) {
    log(`部署流程失败: ${error instanceof Error ? error.message : error}`, 'red')
    process.exit(1)
  }
}

// 执行主流程
if (require.main === module) {
  main().catch((error) => {
    log(`未处理的错误: ${error instanceof Error ? error.message : error}`, 'red')
    process.exit(1)
  })
}
