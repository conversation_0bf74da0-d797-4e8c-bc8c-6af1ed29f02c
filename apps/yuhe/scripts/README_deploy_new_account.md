# 部署新账号脚本使用说明

## 概述

`deploy_new_account.ts` 是一个交互式脚本，用于自动化部署新的微信账号。它将原本需要手动执行的测试代码流程转换为一键部署的自动化脚本。

## 功能特性

- 🔍 **自动发现新账号** - 从JuziAPI获取未配置的新账号
- 🎯 **交互式选择** - 让客户确认要部署的账号信息
- ⚙️ **自动配置生成** - 生成数据库配置和Docker配置
- 📝 **自动代码提交** - 可选择自动提交代码更改
- 🚀 **一键部署** - 可选择自动调用release脚本进行部署

## 使用方法

### 1. 运行脚本

```bash
npm run yuhe:deploy-new-account
```

或者直接运行：

```bash
ts-node scripts/deploy_new_account.ts
```

### 2. 交互式流程

脚本会引导你完成以下步骤：

1. **发现新账号** - 自动检测未配置的新账号
2. **选择账号** - 使用复选框选择要部署的账号
3. **确认配置** - 逐个确认每个账号的配置信息
4. **保存配置** - 自动保存到数据库和Docker配置文件
5. **提交代码** - 选择是否自动提交代码
6. **执行部署** - 选择是否自动运行release脚本

### 3. 示例输出

```
[10:30:15] ===== 开始部署新账号流程 =====
[10:30:16] 正在获取新账号信息...
[10:30:17] 发现 2 个新账号
发现以下新账号:
1. WangKun (***************)
2. TuanDuiXingLe (****************)
? 请选择要部署的账号 (空格键选择，回车确认): (Press <space> to select, <a> to toggle all, <i> to invert selection, and <enter> to proceed)
❯◉ WangKun (***************)
 ◉ TuanDuiXingLe (****************)

? 确认部署账号: WangKun -> yuhe21 (端口: 5021)? Yes
? 确认部署账号: TuanDuiXingLe -> yuhe22 (端口: 5022)? Yes
[10:30:25] 正在保存数据库配置...
[10:30:26] 数据库配置保存成功
[10:30:26] 正在更新Docker配置文件...
[10:30:27] Docker配置文件更新成功
[10:30:27] 正在清除服务器地址缓存...
[10:30:28] 服务器地址缓存清除成功
? 是否自动提交代码并执行部署？ Yes
[10:30:30] 正在提交代码...
[10:30:35] 代码提交成功
? 是否执行release脚本进行部署？ Yes
[10:30:37] 正在执行release脚本...
[10:32:15] 部署完成
[10:32:15] ===== 部署新账号流程完成 =====
[10:32:15] 成功配置 2 个新账号:
[10:32:15] - yuhe21 (端口: 5021)
[10:32:15] - yuhe22 (端口: 5022)
```

## 配置说明

### 自动生成的配置

脚本会为每个新账号自动生成：

1. **数据库配置**：
   - 企业名称：yuhe
   - 账号名称：yuhe{端口号-5000}
   - 微信ID：从API获取
   - 地址：http://139.224.228.125:{端口}
   - 端口：自动分配下一个可用端口
   - 机器人客户ID：使用账号昵称或微信ID
   - 组织令牌：6801c818f8b463b1d228f23d
   - 通知群ID：R:10815051791863856

2. **Docker配置**：
   - 镜像：crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/yuhe:latest
   - 容器名称：与账号名称相同
   - 环境变量：NODE_ENV=dev, TEACHER_NAME=中神通, TZ=Asia/Shanghai
   - 端口映射：自动分配
   - 重启策略：always

### 端口分配规则

- 起始端口：5001
- 自动检测已使用的端口
- 分配下一个连续可用的端口

## 安全特性

- ✅ 执行前确认每个账号的配置
- ✅ 可选择性提交代码和部署
- ✅ 详细的日志输出
- ✅ 错误处理和回滚机制
- ✅ 命令执行失败时自动退出

## 注意事项

1. **权限要求**：确保有数据库写入权限和Git提交权限
2. **网络连接**：需要能够访问JuziAPI和清除缓存的服务器
3. **Docker配置**：会直接修改docker-compose.yaml文件
4. **代码提交**：如果选择自动提交，会直接推送到当前分支

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MongoDB连接配置
   - 确保Prisma客户端正常工作

2. **API调用失败**
   - 检查网络连接
   - 验证ORG_TOKEN是否正确

3. **文件写入失败**
   - 检查文件权限
   - 确保docker-compose.yaml文件存在

4. **Git操作失败**
   - 检查Git配置
   - 确保有推送权限

### 手动回滚

如果自动部署失败，可以手动回滚：

```bash
# 回滚Git提交
git reset --hard HEAD~1

# 手动删除数据库配置（需要连接数据库）
# 手动编辑docker-compose.yaml文件删除新增的服务
```

## 相关文件

- `scripts/deploy_new_account.ts` - 主脚本文件
- `apps/yuhe/docker/docker-compose.yaml` - Docker配置文件
- `release.sh` - 部署脚本
- `packages/model/juzi/api.ts` - JuziAPI接口
- `packages/model/mongodb/prisma.ts` - 数据库客户端

## 开发说明

脚本基于现有的测试代码 `scripts/create_bot_config.test.ts` 中的逻辑开发，主要改进：

1. 交互式客户界面
2. 错误处理和日志记录
3. 自动化流程控制
4. 安全确认机制

如需修改配置参数，请编辑脚本顶部的配置变量部分。
