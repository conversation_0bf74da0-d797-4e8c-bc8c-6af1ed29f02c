import {
  initializeMockEnvironment, processChatHistory,
  pullMessagesFromChatHistory,
  PullMessagesOptions,
  readMessagesFromJSON
} from './end_to_end'
import path from 'path'
import { IDBBaseMessage } from 'service/chat_history/chat_history'
import { DataService } from '../../helper/getter/get_data'
import { when } from 'jest-when'

describe('Chat History Test', function () {
  beforeAll(() => {
    // 初始化工作
  })

  it('12312312', async () => {
    console.log('187671107307'.length)
  }, 60000)

  it('自定义拉取线上聊天记录批量测试', async () => {
    // 配置要拉取的课程编号和条件
    const pullOptions: PullMessagesOptions = {
      specifyChatId:['7881302457898402_1688855542697646'],
      courseNos: [], // 可以自定义这里的课程编号
      minUserMessageCount: 4, // 最少客户消息数
      maxUserMessageCount: 100,
      outputFilePath: path.join(process.cwd(), 'chat_history.json') // 输出文件路径
    }

    // 选择是否重新拉取数据还是使用已有数据
    const shouldPullFromOnline = true // 设置为 true 会重新从线上拉取数据

    let chatHistories: IDBBaseMessage[][]

    if (shouldPullFromOnline) {
      // 从线上拉取聊天记录
      chatHistories = await pullMessagesFromChatHistory(pullOptions)
    } else {
      // 从本地文件读取聊天记录
      chatHistories = await readMessagesFromJSON()
    }

    // 初始化模拟环境
    await initializeMockEnvironment()

    // 创建 mock
    const getCurrentTimeMock = jest
      .spyOn(DataService, 'getCurrentTime')
      .mockImplementation((chatId: string) => undefined as any)

    console.log(`开始并发处理 ${chatHistories.length} 个聊天记录`)

    // 并发处理所有聊天记录
    const processPromises = chatHistories.map((userChatHistory, index) =>
      processChatHistory(userChatHistory, getCurrentTimeMock, index)
    )

    // 等待所有聊天记录处理完成
    await Promise.all(processPromises)

    console.log('所有聊天记录处理完成!')
  }, 1E8)

  // 如果需要单独测试单个聊天记录，可以使用这个测试
  it('should process single chat history', async () => {
    const chatHistories = await readMessagesFromJSON()
    await initializeMockEnvironment()

    const getCurrentTimeMock = jest
      .spyOn(DataService, 'getCurrentTime')
      .mockImplementation((chatId: string) => undefined as any)

    // 只处理第一个聊天记录
    await processChatHistory(chatHistories[0], getCurrentTimeMock, 0)
  }, 1E8)

  it('mock 测试', async () => {
    const getCurrentTimeMock = jest
      .spyOn(DataService, 'getCurrentTime')

    when(getCurrentTimeMock).calledWith('1').mockReturnValue({
      // @ts-ignore fku
      day: 0,
      time: '08:00:00'
    })

    console.log(await DataService.getCurrentTime('1'))
  }, 60000)
})