import { PrismaMongoClient } from '../database/prisma'
import logger from 'model/logger/logger'
import dayjs from 'dayjs'
import { Queue } from 'bullmq'
import { RedisDB } from 'model/redis/redis'

describe('Test', function () {
  beforeAll(() => {

  })


  it('0712期 跑下脚本', async () => {
    const queue = new Queue('yuhe_import_white_list', { connection: RedisDB.getInstance() })
    await queue.add('test', {})
  }, 30000)

  it('0711期 导入一遍直播间', async () => {

  }, 30000)

  it('should pass', async () => {
    // 账号切换
    const originAccount = '****************' // ****************
    const newAccount = '****************'

    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        wx_id: originAccount
      }
    })

    // 迁移 Chat 表
    for (const chat of chats) {
      try {
        const chatId = chat.id
        const newChatId = `${chat.contact.wx_id}_${newAccount}`

        await PrismaMongoClient.getInstance().chat.create({
          // @ts-ignore fku
          data: {
            ...chat,
            id: newChatId,
            wx_id: newAccount
          }
        })

        // 迁移 ChatHistory 表
        const chatHistorys = await PrismaMongoClient.getInstance().chat_history.findMany({
          where: {
            chat_id: chatId
          }
        })


        await PrismaMongoClient.getInstance().chat_history.createMany({
          // @ts-ignore fk u
          data: chatHistorys.map((history) => {
            // @ts-ignore fk u
            delete history.id

            return {
              ...history,
              chat_id: newChatId,
            }
          })
        })

        // console.log()
      } catch (e) {
        logger.error(e)
        logger.log(chat.contact.wx_name, chat.id, '迁移失败')
      }
    }

  }, 1E8)


  it('123213', async () => {
    const d = dayjs('********', 'YYYYMMDD').toDate()

    console.log(d.toLocaleString())
  }, 30000)
})