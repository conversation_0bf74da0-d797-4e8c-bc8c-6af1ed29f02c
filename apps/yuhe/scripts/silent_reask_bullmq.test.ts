import { SilentReAsk } from 'service/schedule/silent_requestion'
import { sleep } from 'lib/schedule/schedule'
import { TaskRegister } from '../schedule/register_task'
import { Config } from 'config'
import { loadConfigByWxId } from 'model/bot_config/load_config'
import { TaskName } from '../schedule/type'
import { Queue } from 'bullmq'
import { RedisCacheDB } from 'model/redis/redis_cache'
import dayjs from 'dayjs'
import { PrismaMongoClient } from '../database/prisma'
import { DataService } from '../helper/getter/get_data'
import { IWhitelistUser, XingyanAPI } from 'model/xingyan'
import { ObjectUtil } from 'lib/object'
import { catchError } from 'lib/error/catchError'
import { GroupNotification } from 'service/group_notification/group_notification'
import logger from 'model/logger/logger'
import { loadConfigByAccountName } from 'service/database/config'
import { JuziMessageHandler } from 'service/message_handler/juzi/message_handler'
import { chatHistoryServiceClient } from '../service/base_instance'


export async function importWhiteListOfDateUsers(date?: Date) {
  // 1. 处理默认日期，date 默认为昨天
  const baseDate = date ? dayjs(date) : dayjs().subtract(1, 'day')
  const nextDate = baseDate.add(1, 'day')

  // 2. 查询 baseDate 那天全天进入的客户
  const chats = await PrismaMongoClient.getInstance().chat.findMany({
    where: {
      course_no: Number(baseDate.format('YYYYMMDD'))
    }
  })

  // 3. 获取 nextDate 那天的直播间信息
  const liveRooms = await DataService.getLiveStreamCourseInfoByDate(nextDate.toDate())

  if (!liveRooms) {
    return
  }

  // 4. 构建待导入客户
  const importUsers: IWhitelistUser[] = []

  const noPhones:string[] = []

  for (const chat of chats) {
    if (chat.id.includes('null') || chat.id.includes('local') || chat.id.includes(' ')) {
      continue
    }

    if (chat.phone) {
      importUsers.push({
        account: chat.phone,
        name: chat.contact.wx_name,
      })
    } else {
      if (Config.isInternalMember(chat.contact.wx_id)) {
        continue
      }
      const isBindSuccess = await DataService.bindPhoneFromRemark(chat.id, chat.wx_id)
      if (!isBindSuccess) {
        noPhones.push(chat.contact.wx_name, chat.id)
      }
    }
  }

  console.log(noPhones.length, noPhones.join('\n'))

  // 5. 对每个直播间导入白名单
  await Promise.allSettled(
    liveRooms.detailList.map(async (liveRoom) => {
      let usersToImport = importUsers
      const currentWhiteList = await new XingyanAPI().getAllWhiteListUsers(liveRoom.roomId)
      if (currentWhiteList && currentWhiteList.length > 0) {
        const existingAccounts = new Set(currentWhiteList.map((u) => u.account))
        usersToImport = usersToImport.filter((user) => !existingAccounts.has(user.account))
      }
      usersToImport = ObjectUtil.removeDuplicateByKey(usersToImport, ['account'])
      const [err, data] = await catchError(
        new XingyanAPI().importUsersInBatches(liveRoom.roomId, usersToImport)
      )
      if (err || data === false) {
        await GroupNotification.notify(
          `导入直播间 ${DataService.getCurrentCourseNo().toString()} ${liveRoom.roomId} 白名单失败 ${err?.message ?? ''}`
        )
      }
    })
  )
  logger.log('导入白名单成功')
}

describe('SilentReAsk BullMQ Implementation - New API', () => {
  beforeAll(async () => {
    // 注册测试任务
    SilentReAsk.registerTask('testTask', async (chat_id: string, params?: any) => {
      console.log(`Test task executed for chat: ${chat_id}`, params)
      console.log(JSON.stringify(params, null, 4))
    })

    SilentReAsk.registerTask('independentTask1', async (chat_id: string, params?: any) => {
      console.log(`Independent task 1 executed for chat: ${chat_id}`)
      console.log(JSON.stringify(params, null, 4))
    })

    SilentReAsk.registerTask('independentTask2', async (chat_id: string, params?: any) => {
      console.log(`Independent task 2 executed for chat: ${chat_id}`)
      console.log(JSON.stringify(params, null, 4))
    })

    SilentReAsk.registerTask('autoRetryTask', async (chat_id: string, params?: any) => {
      console.log(`Auto retry task executed for chat: ${chat_id}`)
      console.log(JSON.stringify(params, null, 4))
    })

    // 启动 worker
    SilentReAsk.startWorker()
  })

  test('should handle multiple different chat IDs', async () => {
    // 注册测试任务
    SilentReAsk.registerTask('testTask', async (chat_id: string, params?: any) => {
      console.log(`Test task executed for chat: ${chat_id}`, params)
      console.log(JSON.stringify(params, null, 4))
    })

    // 为不同的 chat ID 调度任务
    await SilentReAsk.schedule('testTask', 'chat_a', 1000, {
      param1: 'value1',
      param2: 'value2'
    }, { auto_retry: true, independent: true })

    await SilentReAsk.schedule('independentTask1', 'chat_b', 1200)




    await SilentReAsk.schedule('independentTask2', 'chat_c', 800, {
      chat_id: 'chat_c',
    })

    // 等待所有任务执行
    await sleep(2000)
  }, 10000)

  test('should throw error for unregistered task', async () => {
    const chatId = 'test_chat_5'

    await expect(
      SilentReAsk.schedule('nonExistentTask', chatId, 1000)
    ).rejects.toThrow('Task \'nonExistentTask\' is not registered')
  })
})



describe('SilentReAsk 线上任务', () => {
  beforeAll(async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688854546332791')

    TaskRegister.register()

    console.log(await SilentReAsk.getQueue().getWorkers())

    // 启动 worker
    SilentReAsk.startWorker()
  })

  afterAll(async () => {
  })

  it('测试任务', async () => {
    const chatId = '7881300846030208_****************'

    // 客户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.leave_room, chatId,  1000) // 5分钟后检查

    await sleep(5000)
  }, 1E8)

  it('测试付款任务', async () => {
    const chatId = '7881300846030208_****************'
    Config.setting.localTest = false

    // 客户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.product_click, chatId,  1000) // 5分钟后检查

    await sleep(10 * 1000)
  }, 1E8)


  it('测试 auto retry', async () => {
    const chatId = '7881300846030208_****************'
    Config.setting.localTest = false

    // 客户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.test_task, chatId,  3000) // 5分钟后检查
    await chatHistoryServiceClient.addUserMessage(chatId, 'test')

    await sleep(10 * 1000)
  }, 1E8)


  it('测试 independent', async () => {
    const chatId = '7881300846030208_****************'
    Config.setting.localTest = false

    // 客户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.test_task, chatId, 2000, { hi: 'hi' },  { independent: true }) // 5分钟后检查
    await SilentReAsk.schedule(TaskName.test_task, chatId, 2000) // 5分钟后检查

    await sleep(10 * 1000)

    // const queue = SilentReAsk.getQueue()

  }, 1E8)

  it('查看消息 Worker', async () => {
    const messageQueueBullMQ = new Queue('user-message-queue_****************', {
      connection: RedisCacheDB.getInstance()
    })

    console.log(JSON.stringify(await messageQueueBullMQ.getWorkers(), null, 4))
  }, 60000)

  it('backoff', async () => {
    const chatId = '7881300846030208_****************'

    const BACKOFF_SECONDS = [20, 40, 80, 160, 320, 640, 1280, 1800] // 秒
    for (const retryTime of BACKOFF_SECONDS) {
      await SilentReAsk.schedule(
        TaskName.test_task,
        chatId,
        retryTime, // 毫秒
        { retryTime },
        {
          independent: true,
          auto_retry: false
        }
      )
    }

    await sleep(10 * 1000)
  }, 60000)


  it('123123', async () => {
    // console.log()

    console.log(dayjs().subtract(1, 'day').startOf('day').toDate().toLocaleString())
    console.log(dayjs().startOf('day').toDate().toLocaleString())
  }, 60000)


  it('把那', async () => {
    await importWhiteListOfDateUsers(new Date('2025-07-15'))
  }, 1E8)

})


describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe1')
    const queueName =  `silent_reask_queue_${Config.setting.wechatConfig?.id}`

    const queue = new Queue(queueName, {
      connection: RedisCacheDB.getInstance()
    })

    // Get finished jobs
    const finishedJobs = await queue.getJobs(['completed'])
    console.log('Finished jobs:', finishedJobs)

    // Get failed jobs
    const failedJobs = await queue.getJobs(['failed'])
    console.log('Failed jobs:', failedJobs)

    // Or both
    const jobs = await queue.getJobs(['completed', 'failed'])
    console.log('Completed & Failed jobs:', jobs)
  })
})


describe('T123est', function () {
  beforeAll(() => {

  })

  it('*********', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('****************')

    TaskRegister.register()

    console.log(await SilentReAsk.getQueue().getWorkers())

    // 启动 worker
    SilentReAsk.startWorker()
  }, 30000)

  it('isPastUser', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe20')

    console.log(await JuziMessageHandler.isPastUser('****************'))
  }, 30000)

})