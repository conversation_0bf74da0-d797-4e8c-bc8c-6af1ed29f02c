import { DataService } from '../helper/getter/get_data'
import { XingyanAPI } from 'model/xingyan'
import { PrismaMongoClient } from '../database/prisma'
import { chatDBClient } from '../service/base_instance'

describe('check', () => {
  jest.setTimeout(6000000)
  const chatId = '7881301356036850_1688858213716953'
  it('boardcast_data', async() => {

    const chatInfo = await chatDBClient.getById(chatId)
    if (!chatInfo) {
      throw ('没有找到相关的人')
    }
    const courseInfo = await DataService.getCourseInfoByChatId(chatId)
    if (!courseInfo) {
      throw ('没有找到相关课程信息')
    }
    console.log(courseInfo)
    for (const info of courseInfo) {
      console.log(info)
      const liveInfo = await XingyanAPI.getLiveStreamWatchTime(
        `${info.liveId}`,
        chatInfo.phone
      )
      const recordInfo = await XingyanAPI.getRoomRecordingWatchingTime(
        `${info.liveId}`,
        chatInfo.phone
      )
      console.log(liveInfo, recordInfo)
    }
  })

  it('check attend and complete course', async() => {
    const mongoClient = PrismaMongoClient.getInstance()
    const users = await mongoClient.chat.findMany({ where:{ course_no:{ gte:20250601 } } })
    await Promise.allSettled(users.map((user) => Promise.allSettled([
      DataService.isAttendCourse(user.id, 1),
      DataService.isAttendCourse(user.id, 2),
      DataService.isAttendCourse(user.id, 3),
      DataService.isAttendCourse(user.id, 4),
      DataService.isCompletedCourse(user.id, 1),
      DataService.isCompletedCourse(user.id, 2),
      DataService.isCompletedCourse(user.id, 3),
      DataService.isCompletedCourse(user.id, 4),
    ])))
  })
})