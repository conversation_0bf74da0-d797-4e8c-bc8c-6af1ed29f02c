import dayjs from 'dayjs'
import fs from 'fs'
import { PrismaMongoClient } from '../database/prisma'

describe('export chat history', () => {
  jest.setTimeout(6000000)
  it('export human chat', async() => {
    const mongoClient = PrismaMongoClient.getInstance()
    const chats = await mongoClient.chat.findMany({
      where:{
        course_no: {
          gte:20250701,
          lte:20250813
        }
      },
      select:{
        id:true,
        contact:true,
        course_no:true,
        phone:true
      }
    })

    let text = 'chat_id,wx_name,phone,chat_history,day\n'
    for (let j = 0; j < chats.length; j++) {
      console.log(j + 1, '/', chats.length)
      const chat = chats[j]
      const chatHistorys = await mongoClient.chat_history.findMany({
        where: {
          chat_id: chat.id
        },
        select:{
          chat_id:true,
          role:true,
          created_at:true,
          content:true,
          is_send_by_human:true
        },
        orderBy: {
          created_at: 'asc'
        }
      })
      for (let i = 0; i < chatHistorys.length; i++) {
        const chatHistory = chatHistorys[i]
        if (chatHistory.role == 'user' || !chatHistory.is_send_by_human) continue
        const around = chatHistorys.slice(Math.max(0, i - 3), i + 4)
        const summary = around.map((item) => `[${dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')} ${item.role} ${item.is_send_by_human ? '(人工回复)' : ''}: ${item.content}]`).join('\n')
        text += `"${chat.id}","${chat.contact.wx_name}","${chat.phone}","${summary.replaceAll('"', '\\"')}",${dayjs(chatHistory.created_at).diff(dayjs(String(chat.course_no), 'YYYYMMDD'), 'day')}\n`
      }
    }

    // 写入csv文件
    fs.writeFileSync('chat_history.csv', text, 'utf8')

  })
})