import { PrismaClient, chat } from 'model/prisma_client'
import { PrismaMongoClient as PrismaCommonMongoClient } from 'model/mongodb/prisma'

export type { chat }

export class PrismaMongoClient {
  private static instance: PrismaClient | undefined

  public static getInstance(): PrismaClient {
    if (!PrismaMongoClient.instance) {
      PrismaMongoClient.instance = PrismaCommonMongoClient.newInstance('yuhe')
    }
    return PrismaMongoClient.instance
  }
}