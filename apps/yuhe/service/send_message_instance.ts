import { WecomMessageSender } from 'service/message_handler/juzi/message_sender'
import { WecomCommonMessageSender } from 'service/visualized_sop/common_sender/wecom'
import { chatHistoryServiceClient } from './base_instance'

export const wecomMessageSender = new WecomMessageSender(chatHistoryServiceClient)
export const wecomCommonMessageSender = new WecomCommonMessageSender(wecomMessageSender, chatHistoryServiceClient)