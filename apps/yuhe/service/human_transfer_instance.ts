import { HumanTransfer, HumanTransferType } from 'service/human_transfer/human_transfer'
import { chatDBClient, chatStateStoreClient } from './base_instance'
import { eventTrackClient } from './event_track_instance'

export const humanTransferClient = new HumanTransfer({
  transferMessage: HumanTransferType,
  eventTracker: eventTrackClient,
  chatDB: chatDBClient,
  chatStateStore: chatStateStoreClient,
})
