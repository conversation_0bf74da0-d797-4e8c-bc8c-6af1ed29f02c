import {
  batchUpdateCustomersIntentByCourseNo,
  calculateCohortStatistics,
  calculateCustomerIntentScore,
  getIntentLevel,
  IntentLevel, updateAllCustomersIntentByCurrentTime,
  updateCustomerRemark
} from './intent_score'
import { Config } from 'config'
import { JuziAPI } from 'model/juzi/api'
import { loadConfigByAccountName } from 'service/database/config'
import { getUserId } from 'config/chat_id'
import { PrismaMongoClient } from '../database/prisma'

describe('Intent Score Tests', () => {
  it('test intent level calculation', () => {
    // 测试意向度等级计算
    expect(getIntentLevel(95)).toBe(IntentLevel.HIGH)
    expect(getIntentLevel(75)).toBe(IntentLevel.MEDIUM)
    expect(getIntentLevel(35)).toBe(IntentLevel.LOW)
    expect(getIntentLevel(15)).toBe(IntentLevel.ZERO)
  })

  it('test cohort statistics calculation', async () => {
    // 测试同期统计数据计算
    const testCourseNo = ******** // 使用一个测试期数

    try {
      const cohortStats = await calculateCohortStatistics(testCourseNo)
      console.log('同期统计数据计算结果:', cohortStats)

      expect(cohortStats.courseNo).toBe(testCourseNo)
      expect(cohortStats.q95Day2Before).toBeGreaterThanOrEqual(0)
      expect(cohortStats.q95Day2).toBeGreaterThanOrEqual(0)
      expect(cohortStats.q95Day3).toBeGreaterThanOrEqual(0)
      expect(cohortStats.q95Day4).toBeGreaterThanOrEqual(0)
    } catch (error) {
      console.log('同期统计数据计算失败（可能是测试期数没有客户数据）:', error)
    }
  }, 300000)

  it('test single customer intent calculation', async () => {
    // 测试单个客户意向度计算
    const testChatId = '7881299581915601_1688856382598119' // 使用一个存在的客户ID进行测试

    try {
      // 首先获取客户信息以获取期数
      const mongoClient = PrismaMongoClient.getInstance()
      const chat = await mongoClient.chat.findUnique({
        where: { id: testChatId }
      })

      if (!chat || !chat.course_no) {
        console.log('未找到测试客户数据或期数无效')
        return
      }

      const courseNo = chat.course_no
      console.log(`测试客户期数: ${courseNo}`)

      // 计算同期统计数据
      const cohortStats = await calculateCohortStatistics(courseNo)

      // 计算客户意向度
      const customerData = await calculateCustomerIntentScore(testChatId, cohortStats)

      if (customerData) {
        console.log('客户意向度计算结果:', {
          userName: customerData.userName,
          courseNo: customerData.courseNo,
          maxIntentScore: customerData.maxIntentScore,
          currentIntentLevel: customerData.currentIntentLevel
        })

        expect(customerData.maxIntentScore).toBeGreaterThanOrEqual(0)
        expect(customerData.maxIntentScore).toBeLessThanOrEqual(100)
        expect([IntentLevel.HIGH, IntentLevel.MEDIUM, IntentLevel.LOW, IntentLevel.ZERO]).toContain(customerData.currentIntentLevel)
      } else {
        console.log('未找到测试客户数据或计算失败')
      }
    } catch (error) {
      console.log('单个客户意向度计算失败:', error)
    }
  }, 600000)

  it('test batch update by course number', async () => {
    // 测试按期数批量更新
    console.log('开始测试按期数批量更新功能...')

    try {
      const counts = await batchUpdateCustomersIntentByCourseNo(********)
      console.log('更新结果:', counts)

      expect(counts.total).toBeGreaterThanOrEqual(0)
      expect(counts.success).toBeGreaterThanOrEqual(0)
      expect(counts.failed).toBeGreaterThanOrEqual(0)
    } catch (error) {
      console.log('批量更新测试失败:', error)
      // 在测试环境中，如果外部API不可用，这是预期的
    }
  }, 600000)

  it('test updateAllCustomersIntentByCurrentTime', async () => {
    try {
      const counts = await updateAllCustomersIntentByCurrentTime()
      console.log('更新结果:', counts)

      expect(counts.total).toBeGreaterThanOrEqual(0)
      expect(counts.success).toBeGreaterThanOrEqual(0)
      expect(counts.failed).toBeGreaterThanOrEqual(0)
    } catch (error) {
      console.log('实时更新测试失败:', error)
    }
  }, 600000)

  it('test updateCustomerRemark', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe_online_test')
    const bool = await updateCustomerRemark('7881302572153624_1688857404698934', IntentLevel.LOW)
  }, 60000)

  it('test getCustomerInfo', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe_online_test')
    const customerId = getUserId('7881302572153624_1688857404698934')
    const info = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig.id, customerId)
    console.log(info)
  }, 60000)

  it('test memory usage', async () => {
    let maxHeapUsed = 0
    const timer = setInterval(() => {
      const mem = process.memoryUsage()
      if (mem.heapUsed > maxHeapUsed) maxHeapUsed = mem.heapUsed
      console.log(`[interval] heapUsed: ${(mem.heapUsed / 1024 / 1024).toFixed(2)}MB, rss: ${(mem.rss / 1024 / 1024).toFixed(2)}MB`)
    }, 2000)
    const result = await updateAllCustomersIntentByCurrentTime()
    console.log('意向度计算结果:', result)

    clearInterval(timer)
    console.log('本次任务最大heapUsed:', (maxHeapUsed / 1024 / 1024).toFixed(2), 'MB')
  }, 600000)
})