import { DanmuAnalyzer, CourseTimeSlot } from './danmu_analyzer'

import { IRoomMessage } from 'model/xingyan'

describe('DanmuAnalyzer', () => {
  let analyzer: DanmuAnalyzer

  beforeEach(() => {
    analyzer = new DanmuAnalyzer()
  })

  describe('时间段判断逻辑', () => {
    it('应该根据直播间结束时间正确识别上午课程时间段', () => {
      // 模拟下午时间段的直播间结束
      const mockDate = new Date('2024-01-01 15:00:00')
      const originalDate = global.Date

      // 创建一个完整的Date模拟，包含所有必要的方法
      global.Date = jest.fn(() => mockDate) as any
      global.Date.now = jest.fn(() => mockDate.getTime())
      global.Date.UTC = originalDate.UTC
      global.Date.parse = originalDate.parse

      const emptyDanmu: IRoomMessage[] = []
      const timeSlot = (analyzer as any).getCourseTimeSlot(emptyDanmu)
      expect(timeSlot).toBe(CourseTimeSlot.MORNING)

      // 恢复原始Date
      global.Date = originalDate
    })

    it('应该根据直播间结束时间正确识别晚上课程时间段', () => {
      // 模拟晚上时间段的直播间结束
      const mockDate = new Date('2024-01-01 20:00:00')
      const originalDate = global.Date

      // 创建一个完整的Date模拟，包含所有必要的方法
      global.Date = jest.fn(() => mockDate) as any
      global.Date.now = jest.fn(() => mockDate.getTime())
      global.Date.UTC = originalDate.UTC
      global.Date.parse = originalDate.parse

      const emptyDanmu: IRoomMessage[] = []
      const timeSlot = (analyzer as any).getCourseTimeSlot(emptyDanmu)
      expect(timeSlot).toBe(CourseTimeSlot.EVENING)

      // 恢复原始Date
      global.Date = originalDate
    })

    it('当直播间结束时间不在课程时间段时应该默认返回晚上课程', () => {
      // 模拟凌晨时间段的直播间结束
      const mockDate = new Date('2024-01-01 03:00:00')
      const originalDate = global.Date

      // 创建一个完整的Date模拟，包含所有必要的方法
      global.Date = jest.fn(() => mockDate) as any
      global.Date.now = jest.fn(() => mockDate.getTime())
      global.Date.UTC = originalDate.UTC
      global.Date.parse = originalDate.parse

      const emptyDanmu: IRoomMessage[] = []
      const timeSlot = (analyzer as any).getCourseTimeSlot(emptyDanmu)
      expect(timeSlot).toBe(CourseTimeSlot.EVENING)

      // 恢复原始Date
      global.Date = originalDate
    })

    it('应该忽略弹幕内容，只根据时间判断', () => {
      // 模拟晚上时间段的直播间结束，但弹幕是上午的
      const mockDate = new Date('2024-01-01 19:00:00')
      const originalDate = global.Date

      // 创建一个完整的Date模拟，包含所有必要的方法
      global.Date = jest.fn(() => mockDate) as any
      global.Date.now = jest.fn(() => mockDate.getTime())
      global.Date.UTC = originalDate.UTC
      global.Date.parse = originalDate.parse

      const eveningDanmu: IRoomMessage[] = [
        { msgTime: '2024-01-01 14:30:00', content: '上午的弹幕' } as IRoomMessage,
        { msgTime: '2024-01-01 15:00:00', content: '上午的弹幕' } as IRoomMessage,
      ]

      const timeSlot = (analyzer as any).getCourseTimeSlot(eveningDanmu)
      expect(timeSlot).toBe(CourseTimeSlot.EVENING) // 应该根据直播间结束时间判断，而不是弹幕时间

      // 恢复原始Date
      global.Date = originalDate
    })
  })

  describe('课程开始时间计算', () => {
    it('应该正确计算上午课程的开始时间', () => {
      const msgDate = '2024-01-01'
      const courseStartTime = (analyzer as any).getCourseStartTime(msgDate, CourseTimeSlot.MORNING)

      // 转换为日期对象进行验证
      const startDate = new Date(courseStartTime * 1000)
      expect(startDate.getHours()).toBe(14)
      expect(startDate.getMinutes()).toBe(0)
      expect(startDate.getSeconds()).toBe(0)
    })

    it('应该正确计算晚上课程的开始时间', () => {
      const msgDate = '2024-01-01'
      const courseStartTime = (analyzer as any).getCourseStartTime(msgDate, CourseTimeSlot.EVENING)

      // 转换为日期对象进行验证
      const startDate = new Date(courseStartTime * 1000)
      expect(startDate.getHours()).toBe(18)
      expect(startDate.getMinutes()).toBe(50)
      expect(startDate.getSeconds()).toBe(0)
    })
  })

  describe('时间匹配逻辑', () => {
    it('应该正确匹配上午课程的问题', () => {
      const morningDanmu: IRoomMessage[] = [
        { msgTime: '2024-01-01 14:11:00', content: '回复1' } as IRoomMessage, // 第11分钟的问题
      ]

      // 计算上午课程的开始时间
      const courseStartTime = (analyzer as any).getCourseStartTime('2024-01-01', CourseTimeSlot.MORNING)
      const question = (analyzer as any).findClosestQuestion('2024-01-01 14:11:00', 1, courseStartTime)
      expect(question).toContain('你们做抖音有多久了')
    })

    it('应该正确匹配晚上课程的问题', () => {
      const eveningDanmu: IRoomMessage[] = [
        { msgTime: '2024-01-01 19:01:05', content: '回复1' } as IRoomMessage, // 第11分钟的问题
      ]

      // 计算晚上课程的开始时间
      const courseStartTime = (analyzer as any).getCourseStartTime('2024-01-01', CourseTimeSlot.EVENING)
      const question = (analyzer as any).findClosestQuestion('2024-01-01 19:01:05', 1, courseStartTime)
      expect(question).toContain('你们做抖音有多久了')
    })
  })
})
