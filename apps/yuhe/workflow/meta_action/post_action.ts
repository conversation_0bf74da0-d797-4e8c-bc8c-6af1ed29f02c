import { IChattingFlag } from '../../state/user_flags'
import { sleep } from 'openai/core'
import { IWecomMsgType } from 'model/juzi/type'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { getUserId } from 'config/chat_id'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../../schedule/type'
import logger from 'model/logger/logger'
import { DateHelper } from 'lib/date/date'
import { DataService } from '../../helper/getter/get_data'
import { SalesCase } from '../../helper/rag/sales_case'
import { ActionInfo } from 'service/agent/stage'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../../service/base_instance'
import { wecomMessageSender } from '../../service/send_message_instance'
import { purchaseLink } from '../../service/global_data'

import { Node } from 'service/agent/workflow'

export class PostAction {
  static async generalDiagnosis(chat_id: string) {
    await chatStateStoreClient.update(chat_id, { state:<IChattingFlag>{ after_bonding: true } })
    // 先调度催促发送抖音截图任务
    await SilentReAsk.schedule(
      TaskName.urge_douyin_screenshot,
      chat_id,
      1000 * 60 * 5,
      undefined,
      { auto_retry: true, independent:true }
    )
    return { guidance: '' } as ActionInfo
  }

  public static getPeiPaoCourseIntro(): string {
    return `陪跑营卖点:
- 陪跑营核心是中神通老师带队直接带你30天迈过从知道思路到拿到结果过程中所有的问题
- 整个过程是1对1私人订制您的方案，陪跑过程分为3个阶段。
  - 第一阶段（0-3天）：围绕账号定位账号搭建，根据你规模和你的生意订制你的账号定位。
  - 第二阶段（3-15天）：打造人设，拍摄剪辑。手把手带你用532 内容生产法则，真正打造能获客的账号。
  - 第三阶段（15-30天）：全矩阵的，直播引流，私域运营。
- 除了1对1陪跑服务外，还配套提供以下福利：
  - 365天答疑：30天结束后，咱们的陪跑群还是继续存在，365天给您答疑，平台规则发生的变化，后续落地遇到的任何问题都可以陪伴解答。
  - 37节的获客实操课程，以及团购直播课程：除了私人订制的陪跑服务之外，陪跑营还赠送37节的获客实操课程，以及团购直播课程，这些课程都是中神通老师自己讲的课程，都是非常落地的干货。支持永久回放。
  - AI数字人和AI爆店系统：在整个陪跑过程，针对出镜头难，文案能力不行的问题，赠送AI数字人和AI爆店系统，解决这2个问题。基本去掉了所有做起来账号的技术难点了。要的就是咱一定要做起来的决心了！只要您想做，我们就有信心帮您拿到结果。
  - 3天2夜的线下课：最后重磅！除了线上对接之外，2980还赠送3天2夜的线下课（机票酒店自理），直接现场带您实操落地！`
  }

  static async sendCourseIntro(chat_id: string) {
    const user_id = getUserId(chat_id)

    const actionInfo: ActionInfo = {
      guidance: PostAction.getPeiPaoCourseIntro(),
    }

    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).is_send_course_intro) { return actionInfo }

    actionInfo.callback = async () => {
      await sleep(2000)

      await wecomMessageSender.sendById(
        {
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: `给咱们总结下2980陪跑营要点：

✅陪跑营核心是中神通老师带队直接带你30天迈过从知道思路->拿到结果过程中所有的问题；整个过程是1对1私人订制您的方案，陪跑过程分为3个阶段
第一阶段（0-3天）：围绕账号定位账号搭建，根据你规模和你的生意订制你的账号定位
第二阶段（3-15天）：打造人设，拍摄剪辑。手把手带你用532内容生产法则，真正打造能获客的账号
第三阶段（15-30天）：全矩阵的，直播引流，私域运营

✅除了1对1陪跑服务外，还配套提供以下福利：
🚩【365天答疑】：30天结束后，咱们的陪跑群还是继续存在，365天给您答疑，平台规则发生的变化，后续落地遇到的任何问题都可以陪伴解答
🚩【37节的获客实操课程，以及团购直播课程】：除了私人订制的陪跑服务之外，陪跑营还赠送【37节的获客实操课程，以及团购直播课程】，这些课程都是中神通老师自己讲的课程，都是非常落地的干货。支持永久回放
🚩【AI数字人和AI爆店系统】在整个陪跑过程，针对出镜头难，文案能力不行的问题，赠送【AI数字人和AI爆店系统】解决这2个问题。基本去掉了所有做起来账号的技术难点了。要的就是咱一定要做起来的决心了！只要您想做，我们就有信心帮您拿到结果
🚩【3天2夜的线下课】最后重磅！除了线上对接之外，2980还赠送【3天2夜的线下课】（机票酒店自理），直接现场带您实操落地！（我们真心想帮助您拿到结果啦！）`,
        },
        { shortDes: '陪跑营课程介绍' },
      )
      await sleep(2000)

      await wecomMessageSender.sendById({
        user_id: user_id,
        chat_id: chat_id,
        ai_msg: '[陪跑营课程介绍图片]',
        send_msg: {
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/%e9%99%aa%e8%b7%91%e8%90%a5%e8%af%be%e7%a8%8b%e4%bb%8b%e7%bb%8d.jpg',
        },
      })
      await sleep(2000)

      await wecomMessageSender.sendById({
        user_id: user_id,
        chat_id: chat_id,
        ai_msg: '这是咱们赠送3天2夜线下课，学习氛围也非常的好',
      })
      await sleep(2000)

      await wecomMessageSender.sendById({
        user_id: user_id,
        chat_id: chat_id,
        ai_msg: '[陪跑营线下课现场图片]',
        send_msg: {
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/%e9%99%aa%e8%b7%91%e8%90%a5%e7%ba%bf%e4%b8%8b%e8%af%be%e7%8e%b0%e5%9c%ba.jpg',
        },
      })
      await chatStateStoreClient.update(chat_id, { state: <IChattingFlag> { is_send_course_intro: true } })
    }
    return actionInfo
  }

  public static async sendInvitation() {
    const actionInfo: ActionInfo = { guidance: `务必向客户发送下单链接：${purchaseLink}，并邀约客户购买` }
    return actionInfo
  }

  public static async sendCaseImage(chat_id: string): Promise<ActionInfo> {
    const user_id = getUserId(chat_id)
    const searchResult = await SalesCase.searchByChatId(chat_id, [SalesCase.peipaoyingDoc])
    const revenueTmp = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chat_id, '基本信息', '年营业额')
    const revenue = revenueTmp === '' ? '20万' : revenueTmp
    const actionInfo: ActionInfo = { guidance: `案例信息：学员学习前：${revenue}左右年营业额（营业额数值可以随机一点），学员学习后：营业额有提升但不要太夸张` }

    if (!searchResult) { return actionInfo }

    const chatHistory = await chatHistoryServiceClient.formatHistoryOnRole(chat_id, 'assistant', 5)

    if (chatHistory.includes(`${searchResult.topic}案例1`)) { return actionInfo }

    const sendImages = async () => {
      await sleep(5000)
      const images = searchResult.images.slice(0, 3)

      for (let i = 0; i < images.length; i++) {
        const image = images[i]
        await sleep(1000)

        await wecomMessageSender.sendById({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: `[${searchResult.topic}案例${i + 1}]`,
          send_msg: {
            type: IWecomMsgType.Image,
            url: image,
          },
        })
      }
    }

    actionInfo.guidance = `案例信息：${searchResult.description}`
    actionInfo.callback = sendImages
    return actionInfo
  }

  public static async provideDepositPlan() {
    const actionInfo: ActionInfo = { guidance: '主动提出可以先付1000定金，后续进入陪跑营之后再补齐，现在使用花呗还可以分期付款' }
    return actionInfo
  }

  public static async enterPostpone(chat_id: string) {

    logger.trace({ chat_id:chat_id }, '客户进入延期/复训逻辑')
    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).has_postpone || (await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).has_refresher_training) {
      return { guidance: '客户已经延期/复训过了，不能再延期' } as ActionInfo
    }

    await chatDBClient.setStopGroupPush(chat_id, true)

    await chatStateStoreClient.update(chat_id, {
      state: <IChattingFlag> {
        is_in_postpone: true,
      },
    })

    await chatStateStoreClient.update(chat_id, {
      nextStage: Node.FreeTalk
    })

    await SilentReAsk.schedule(TaskName.enter_postpone, chat_id, await PostAction.getPostPoneDelayTime(chat_id), {}, { auto_retry: true })
    const dayDiff = await PostAction.getNewCourseStartDayDiff(chat_id)
    const date = DateHelper.add(new Date(), dayDiff, 'day')
    return { guidance: `可供延期/复训时间：${DateHelper.getFormattedDate(date, false)}` } as ActionInfo
  }

  public static async reaskAnotherDay(chat_id: string) {
    logger.trace({ chat_id: chat_id }, '客户保留名额，次日询问')
    await SilentReAsk.schedule(TaskName.reask_another_day, chat_id, 24 * 60 * 60 * 1000, {}, { auto_retry: true }) // 24小时后提醒客户
    return { guidance: '' } as ActionInfo
  }

  /**
   * 返回day5 9 13等课程时间的**开课时间**
   * @param chat_id
   */
  public static async getNewCourseStartDayDiff(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    let day = currentTime.day
    // day = 0 为 day 5开课，day = 5为当天开课
    if (currentTime.day == 0) {
      return 5
    }
    if (day > 1) {
      day = day - 1
    }
    const multiple = Math.ceil(day / 4)
    return  multiple * 4 - currentTime.day + 1
  }

  private static async getPostPoneDelayTime(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    if (currentTime.day < 5) {
      return 30 * 60 * 1000
    } else {
      return 5 * 60 * 60 * 1000
    }
  }
}