import dayjs from 'dayjs'
import { IWorkflowState } from 'service/llm/state'
import { IChattingFlag } from '../../state/user_flags'
import { ContextBuilder } from '../context'
import { firstAskIntention } from '../../client/event_handler'
import { DataService } from '../../helper/getter/get_data'
import { replyClient } from '../../service/instance'
import { chatHistoryServiceClient, chatStateStoreClient } from '../../service/base_instance'
import { Node, trackInvoke, WorkFlowNode } from 'service/agent/workflow'
import { talkCounter } from '../../prometheus/client'
import { Config } from 'config'
import { FreeTalk } from 'service/agent/freetalk'
import { eventTrackClient } from '../../service/event_track_instance'
import { stageFilter } from '../meta_action/stage'

export class FreeTalkNode extends WorkFlowNode {
  public static async invoke(state: IWorkflowState) {
    talkCounter.labels({ bot_id: Config.setting.wechatConfig?.name || Config.setting.wechatConfig?.id }).inc(1)
    const freeTalk = new FreeTalk(chatHistoryServiceClient, ContextBuilder, eventTrackClient, replyClient, stageFilter)
    return await freeTalk.invoke(state)
  }
}

export class IntentionQueryNode extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState):Promise<Node> {
    const exitIntentionQueryNode = async() => {
      const next = await FreeTalkNode.invoke(state)
      await firstAskIntention(state.chat_id, state.user_id)
      return next
    }
    // 强规则如果已经在第一天以后了，强行变free_talk
    const currentTime = await DataService.getCurrentTime(state.chat_id)
    const now = dayjs()
    if ((currentTime.day == 1 && now.hour() >= 12) || (currentTime.day > 1)) {
      return await exitIntentionQueryNode()
    }

    const userState = await chatStateStoreClient.getFlags<IChattingFlag>(state.chat_id)
    if (!userState.is_send_first_ask_intention) { // 如果还没发送表单，简单回复
      const context = await ContextBuilder.build({
        state,
        retrievedKnowledge: true,
        talkStrategyPrompt: '和客户友好交流，简单回答问题'
      })
      await replyClient.invoke({
        state,
        context: context,
        promptName: 'intention_query',
      })
    } else {
      await FreeTalkNode.invoke(state)
      if ((await chatStateStoreClient.getFlags<IChattingFlag>(state.chat_id)).is_in_postpone) {
        return Node.FreeTalk
      }
    }
    await firstAskIntention(state.chat_id, state.user_id)
    return Node.IntentionQuery
  }
}