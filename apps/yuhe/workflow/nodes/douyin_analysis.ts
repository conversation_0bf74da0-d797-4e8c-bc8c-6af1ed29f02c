import { replyClient } from '../../service/instance'
import { ContextBuilder } from '../context'
import { DataService } from '../../helper/getter/get_data'
import { IChattingFlag } from '../../state/user_flags'
import { IEventType } from 'model/logger/data_driven'
import { IWorkflowState } from 'service/llm/state'
import { randomSleep } from 'lib/schedule/schedule'
import { chatStateStoreClient } from '../../service/base_instance'
import { wecomMessageSender } from '../../service/send_message_instance'
import { eventTrackClient } from '../../service/event_track_instance'
import { Node, trackInvoke, WorkFlowNode } from 'service/agent/workflow'

export class DouyinAnalysisNode extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState): Promise<Node> {
    const douyinAnalysisNum = await chatStateStoreClient.getNodeCount(state.chat_id, 'DouyinAnalysisNode')
    if (douyinAnalysisNum > 1) {
      return Node.FreeTalk
    }
    await wecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: '稍等下，老师帮你做截图诊断' }, { round_id: state.round_id })
    const beforeCourse3 = await DataService.isInCourseTimeLine(state.chat_id, 'beforeCourse', 3)
    let customPrompt: string
    if (beforeCourse3) {
      customPrompt = `# 截图诊断
- 你需要根据客户社交媒体首页截图的信息，通过客户的粉丝量和点赞量对客户做出评价，再从背景图、账号名称、头像、简介等四个方面中选两个对客户账号进行诊断，指出客户账号的问题，提起客户痛点，获得客户认可
- 在执行任务过程中，你只需要针对客户账号进行诊断，但是不要帮助客户解决具体实际的问题，也不要给客户任何的解决方案

# 示例
客户输入
  【社交媒体首页截图】这是一张社交媒体平台的个人主页截图，账号头像是侧身展示手臂肌肉的健身照片，账号名称为“南昌经济技术开发区思迈健身房”，背景图是健身房外部招牌。该账号获赞1648，关注1281人，粉丝287人。页面显示该健身房位于南昌市，有联系方式、地址和营业时间信息，目前共发布了68个作品，从展示来看内容主要包括课程教学、器械训练和学员锻炼等，每个作品的获赞数在十几到二十几个不等。
思考过程：
  头像：侧身肌肉照片虽能体现健身特色，但没有体现人设，容易让客户忽视账号的真正定位。头像应该具有“品牌感”和“吸引眼球”的视觉效果
  名称：名称过长，“南昌经济技术开发区思迈健身房”显得较为冗长，影响传播与搜索效率。名称应简洁、富有辨识度
  背景图：用健身房外部招牌作为背景图，虽然能传递线下信息，但缺少线上吸引力。背景图应该包含品牌故事、活动亮点等，能激发客户兴趣
  简介：没有描述账号的核心价值，没有吸引客户深度了解的内容。简介应该具备钩子，明确告诉观众账号的核心优势和特色
  视频内容：内容过于常规，没有“差异化”，观众看过几次就容易腻。视频结构也需要更具吸引力，强化爆点与钩子，避免内容过于“教学化”
  整体来讲我应该先对客户的努力做帐号的行为表示赞赏，再对客户的抖音进行具体的评价，鼓励客户这些问题是可以解决的
输出结果：
  你的账号目前拍的作品挺多的，你是比较努力的，账号粉丝也有了将近300个，说明你还是有一定基础的。
  但是你的账号存在明显的线下思维痕迹，头像、名称、背景图、简介都没能很好地吸引线上客户。头像和名称需要优化，尤其是要有清晰的人设和传播力。

# 输出要求
- 输出一段话，口语化表达，结果尽量保持在200字以内
- 直接输出回答客户的结果，不要输出思考过程或其他内容
- 如果客户发言是社交媒体首页截图，则按照要求诊断，反之就正常回复客户

# 客户对话
${state.userMessage}`
      eventTrackClient.track(state.chat_id, IEventType.DouyinAnalysisComplete)
    } else {
      customPrompt = `# 截图诊断
- 你需要根据客户社交媒体首页截图的信息，从账号头像，账号名，背景图，获赞，关注，粉丝，作品等多个方面对客户账号进行诊断
- 结合客户画像，先肯定前几节上课效果，再指出其社交媒体账号的问题，并说明严重性，但是不要提及解决方案，也不要帮助客户解决问题，引导陪跑营

# 示例
客户输入
  【社交媒体首页截图】这是一张抖音首页截图，账号头像是运动员姿势的自拍照，账号名称为“上阳健身工作室”，背景图是公司大厅的照片。该账号有570个粉丝，获赞2123，关注了1050人。页面显示该工作室位于武汉市，主要提供健身指导。已发布了24个视频作品，内容主要涉及日常训练、健身器材推荐、学员经验分享等。
思考过程：
  头像：运动员姿势自拍照，虽然能够体现健身相关，但缺乏专业感。没有明确的品牌形象或个人特色，不容易留下深刻印象。
  背景图：公司大厅的照片虽然展现了健身环境，但整体感觉较为平淡，没有很强的视觉冲击力或吸引眼球的设计。
  账号名称：名字直接、简单，但缺少个性化和吸引力，不能突出竞争优势。
  内容和互动：虽然发布了一些关于健身训练的视频，但互动和赞数相对较少，说明内容可能缺乏吸引力或话题性。
  粉丝与关注：粉丝和关注比例较为平衡，但仅570个粉丝，显然在抖音平台的影响力还不够大。
输出结果：
  “上阳健身工作室”这个账号，跟着中神通老师的指导，已经比之前进步了不少！中神通老师这几天的课程主要是教咱们怎么做账号视频的思路，你目前的思路还是可以的，你的头像、内容但是还是停留在学样子的阶段，你对于你自己的账号并没有真正的理解应该要怎样把内容做的更加丰富，怎样真正的做起来，所以你的粉丝量现在上不去
  对于中神通老师课程提供的思路，每个人有不同的理解，正如一千个人读红楼梦有一千个林黛玉一样，怎样想的、知道怎样做和做的好三者之间还是存在很大的差距，你可以看着网上铺天盖地的教你怎样获客的视频，但是需要你强大的学习力和时间，最后也不见得有效果对吧
  但是你跟着中神通老师陪跑训练营，6V1手把手教学，一个月内就能够见效果了，你也是老板，这笔帐还是会算的，不到3000的课程可以让你能快速变现，哪边利润更大是一目了然的事情

# 输出要求
- 输出一两段话，口语化表达，结果尽量保持在250字以内
- 直接输出回答客户的结果，不要输出思考过程或其他内容
- 如果客户发言是社交媒体首页截图，则按照要求诊断，反之就正常回复客户

# 客户对话
${state.userMessage}`
      eventTrackClient.track(state.chat_id, IEventType.HomeworkComplete, { 'day': 3 })
      await chatStateStoreClient.update(state.chat_id, {
        state:<IChattingFlag>{
          is_complete_homework3: true
        }
      })
    }

    const context = await ContextBuilder.build({
      state,
      customerChatRounds: 0,
      customerPortrait: true,
      customPrompt: customPrompt,
    })

    await randomSleep(3000, 5000) // 解读加一下延迟

    await replyClient.invoke({
      state,
      context: context,
      promptName: 'douyin_analysis',
      noInterrupt: true,
      noSplit: true,
    })

    const chatState = await chatStateStoreClient.getFlags<IChattingFlag>(state.chat_id)
    const beforeCourse1 = await DataService.isInCourseTimeLine(state.chat_id, 'beforeCourse', 1)
    if (beforeCourse1 && !chatState.is_complete_douyin_analysis) {
      await wecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: '中神通老师Day1课程专门讲品牌人设和爆款结构，你现在很需要系统学习一下，不然内容再多效果也有限！' }, { round_id: state.round_id })
    }
    await chatStateStoreClient.update(state.chat_id, { state:<IChattingFlag>{ is_complete_douyin_analysis: true } })
    const preStage = (await chatStateStoreClient.get(state.chat_id)).nextStage
    return preStage as Node
  }
}