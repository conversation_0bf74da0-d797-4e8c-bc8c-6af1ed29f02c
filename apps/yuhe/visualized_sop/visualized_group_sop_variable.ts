import dayjs from 'dayjs'
import { Config } from 'config'
import { JuziAPI } from 'model/juzi/api'
import logger from 'model/logger/logger'
import { startGroupTasks } from 'service/visualized_sop/visualized_group_sop_task_starter'
import { calGroupTaskTime, DataService } from '../helper/getter/get_data'
import { accountToName } from '../config/config'
import { RedisDB } from 'model/redis/redis'
import { AddUserToGroup } from '../helper/group/add_user_to_group'
import { PrismaMongoClient } from '../database/prisma'

export const groupConditionJudgeMap:Record<string, ((params:{groupId:string})=>Promise<boolean>)> = {
}

export const groupTextVariableMap:Record<string, (params:{groupId:string})=> Promise<string>> = {
  '第一节课链接': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行sop的时候没有找到这个群 group_id: ${groupId}`)
      return ''
    }
    return await DataService.getCourseLinkByCourseNo(groupInfo.course_no, 1) ?? ''
  },
  '第二节课链接': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行sop的时候没有找到这个群 group_id: ${groupId}`)
      return ''
    }
    return await DataService.getCourseLinkByCourseNo(groupInfo.course_no, 2) ?? ''
  },
  '第三节课链接': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行sop的时候没有找到这个群 group_id: ${groupId}`)
      return ''
    }
    return await DataService.getCourseLinkByCourseNo(groupInfo.course_no, 3) ?? ''
  },
  '第四节课链接': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行sop的时候没有找到这个群 group_id: ${groupId}`)
      return ''
    }
    return await DataService.getCourseLinkByCourseNo(groupInfo.course_no, 4) ?? ''
  }
}

export const groupActionCustomMap:Record<string, (params:{groupId:string})=> Promise<void>> = {
  '踢人': async({ groupId }) => {
    const imBotId = Config.setting.wechatConfig?.id as string
    const botUserId = Config.setting.wechatConfig?.botUserId as string
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行踢人sop的时候没有找到这个群 group_id: ${groupId}`)
      return
    }
    const whitelist = ['7881302171979485', '7881303151917354', '7881300050011210', '7881302873933299', '7881301516129585', '7881300889914774', '7881302298050442', '7881300516060552', '7881300846030208', '16**************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '16**************', '****************', '****************', '****************', '****************', ...Object.keys(accountToName)]
    await JuziAPI.kickNonWhitelistMembers(botUserId, imBotId, groupId, whitelist)
    await mongoClient.group.update({ where:{ group_id:groupId }, data:{ course_no:Number(dayjs(String(groupInfo.course_no), 'YYYYMMDD').add(4, 'day').format('YYYYMMDD')) } })
    const redisClient = RedisDB.getInstance()
    await redisClient.set(AddUserToGroup.getGroupNumberRedisKey(groupId), '0')
    await startGroupTasks(PrismaMongoClient.getInstance(), 'yuhe', groupId, calGroupTaskTime)
  }
}

export const groupLinkSourceVariableTagMap:Record<string, (params:{groupId:string})=>Promise<string>> = {
}
