import { VisualizedGroupSopProcessor } from 'service/visualized_sop/visualized_group_sop_processor'
import { groupActionCustomMap, groupConditionJudgeMap, groupLinkSourceVariableTagMap, groupTextVariableMap } from './visualized_group_sop_variable'

export class YuheVisualizedGroupSopProcessor extends VisualizedGroupSopProcessor {

  getActionCustomMap(): Record<string, (params: { groupId:string }) => Promise<void>> {
    return groupActionCustomMap
  }
  getConditionJudgeMap(): Record<string, ((params: { groupId:string }) => Promise<boolean>)> {
    return groupConditionJudgeMap
  }
  getLinkSourceVariableTagMap(): Record<string, (params: { groupId:string }) => Promise<string>> {
    return groupLinkSourceVariableTagMap
  }
  getTextVariableMap(): Record<string, (params: { groupId:string }) => Promise<string>> {
    return groupTextVariableMap
  }
}