import dayjs from 'dayjs'
import { Config } from 'config'
import { getChatId, getUserId } from 'config/chat_id'
import { catchError } from 'lib/error/catchError'
import { JuziAPI } from 'model/juzi/api'
import { IWecomMsgType } from 'model/juzi/type'
import { sleep } from 'lib/schedule/schedule'
import { FriendAcceptedEvent, JuziEventHandler } from 'service/message_handler/juzi/event_handler'
import { startTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { ISendMedia } from 'service/visualized_sop/visualized_sop_type'
import { calTaskTime, DataService } from '../helper/getter/get_data'
import { IChattingFlag } from '../state/user_flags'
import {
  IXingyanPushCallback,
  OrderCloseCallback,
  ProductClickCallback,
  ProductPurchaseCallback,
  UserEnterRoomCallback,
  UserLeaveRoomCallback,
  XingyanPushType,
} from 'model/xingyan'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { AsyncLock } from 'model/lock/lock'

import { HumanTransfer } from '../human_transfer/human_transfer'
import { IEventType } from 'model/logger/data_driven'
import logger from 'model/logger/logger'
import { TaskName } from '../schedule/type'
import { RegexHelper } from 'lib/regex/regex'
import RateLimiter from 'model/redis/rate_limiter'
import { PrismaMongoClient } from '../database/prisma'
import { sendMsg } from '../helper/send_msg'
import { ExtractUserSlots } from '../user_slots/user_slots_extraction'
import { chatDBClient, chatStateStoreClient } from '../service/base_instance'
import { wecomMessageSender } from '../service/send_message_instance'
import { eventTrackClient } from '../service/event_track_instance'

import { Node } from 'service/agent/workflow'
import { HumanTransferType } from 'service/human_transfer/human_transfer'

export class EventHandler extends JuziEventHandler {
  async handleFriendAcceptedEvent(data: FriendAcceptedEvent) {
    // 只处理一次加好友事件
    const userId = data.imContactId
    const chatId = getChatId(userId)
    const isFriendAccepted = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_friend_accepted
    if (isFriendAccepted) {
      return
    }
    // 初始化课程号

    // 这里直接调用一下 getCustomerInfo，防止被客户删除掉获取不到客户信息
    await catchError(JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId))

    await chatStateStoreClient.update(chatId, {
      state: {
        is_friend_accepted: true
      }
    })

    await sendYuHeWelComeMessage(chatId, userId)

    // 直接在这里更新客户名
    await chatDBClient.updateContact(chatId, data.imContactId, data.name)
  }
  /*
 * 按照是否是课程前一周、课程周、课程结束后的第几周，1-7 进行 + 时间 进行任务创建。
 */


  handleYuHeEvent(callback: IXingyanPushCallback<any>) {
    switch (callback.type) {
      case XingyanPushType.USER_ENTER_ROOM:
        return this.handleUserEnterRoom(callback as UserEnterRoomCallback)
      case XingyanPushType.USER_LEAVE_ROOM:
        return this.handleUserLeaveRoom(callback as UserLeaveRoomCallback)
      case XingyanPushType.PRODUCT_PURCHASE:
        return this.handleProductPurchase(callback as ProductPurchaseCallback)
      case XingyanPushType.PRODUCT_CLICK:
        return this.handleProductClick(callback as ProductClickCallback)
      case XingyanPushType.ORDER_CLOSE:
        return this.handleOrderClose(callback as OrderCloseCallback)
      default:
        throw new Error(`不支持的回调类型: ${callback.type}`)
    }
  }

  /**
   * 处理客户进入直播间事件
   * @param callback 回调数据
   */
  async handleUserEnterRoom(callback: UserEnterRoomCallback) {
    console.log('客户进入直播间:', JSON.stringify(callback, null, 4))
    const chat = await chatDBClient.getChatByPhone(callback.param.mobile)
    if (!chat) {
      return
    }

    const chatId =  chat.id

    await chatStateStoreClient.update(chatId, {
      state: {
        is_in_live_room: true
      }
    })
  }

  /**
   * 处理客户离开直播间事件
   * @param callback 回调数据
   */
  async handleUserLeaveRoom(callback: UserLeaveRoomCallback) {
    console.log('客户离开直播间:', JSON.stringify(callback, null, 4))
    const chat = await chatDBClient.getChatByPhone(callback.param.mobile)
    if (!chat) { return }

    const chatId =  chat.id
    await chatStateStoreClient.update(chatId, {
      state: {
        is_in_live_room: false
      }
    })

    // 客户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.leave_room, chatId,  5 * 60 * 1000) // 5分钟后检查
  }

  /**
   * 处理商品购买事件
   * @param callback 回调数据
   */
  async handleProductPurchase(callback: ProductPurchaseCallback) {
    console.log('商品购买:', JSON.stringify(callback, null, 4))

    const phone = callback.param.mobile ? callback.param.mobile : callback.param.orderPhone
    const linkPurchase = !callback.param.mobile && callback.param.orderPhone
    const transactionId = callback.param.transactionId

    const chat = await chatDBClient.getChatByPhone(phone)
    if (!chat) { return }

    const chatId =  chat.id
    const userId = getUserId(chatId)

    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)

    const previousTransactionIds = state.transactionIds ?? ''
    if (!previousTransactionIds.includes(transactionId)) {
      await chatStateStoreClient.update(chatId, { state: <IChattingFlag>{
        payment_number:(state.payment_number ?? 0) + 1,
        transactionIds: (previousTransactionIds == '' ? transactionId : `${previousTransactionIds} ${transactionId}`)
      } })
    }

    if (state.is_complete_payment) { return }

    await chatStateStoreClient.update(chatId, { state: { is_complete_payment: true } })
    const mongoClient = PrismaMongoClient.getInstance()
    await mongoClient.chat.update({ where:{ id:chatId }, data:{ pay_time:new Date() } })
    eventTrackClient.track(chatId, IEventType.PaymentComplete, { 'payment_type': linkPurchase ? 'offline' : 'live_room' })

    await sendMsg(userId, chatId, ['恭喜同学成功加入陪跑团队\n我马上给您拉群，开启您陪跑班所有权益～\n在此之前需要您把您得真实姓名和电话填写下\n\n真实姓名：\n电话：', `关于您可能遇到的疑问提前帮您解答下哈：
 ❓什么时候拉群：
 >我们已经正常拉群，您看下你的聊天列表，如果没拉会在12小时内补拉哈。 
❓群里怎么只有3位老师：
 >现在拉的是主要对接陪跑老师，她后续会把老师以及其他陪跑老师拉进来。 
❓群里为什么没有人回复：
 >陪跑老师的上班时间是【10-19点】，也有可能正好在会上会晚一点，请耐心等一下。 
❓陪跑权益在哪领取、AI工具在哪下载：
 >陪跑老师后续会在群里跟你对接后续陪跑方案和计划，以及陪跑权益、工具等老师会在群里给你发送。
❓老师周末为什么不回复消息：
>陪跑老师周一 到周五都是正常工作的哈【10-19点】，但是由于陪跑比较耗费心神，老师周末也要休息+学习知识更好的带你们，所有周末两天老师时休息的哈，回复消息不及时，是为了后面更好的帮助你，请耐心等待！
如有其他疑问，欢迎随时沟通，祝您开启快乐的学习旅程🎉`], '成功报名陪跑营营销信息')
    await HumanTransfer.transfer(chatId, userId, HumanTransferType.PaidCourse, 'onlyNotify', `${linkPurchase ? '线下支付' : '直播间支付'}，手机：${chat.phone}`)
    if (linkPurchase) {
      await wecomMessageSender.sendById({ user_id: userId, chat_id: chatId, ai_msg: '麻烦再发一下带有订单号或者交易单号的截图哈，回头财务好核对付款信息以免后面把咱们遗漏' })
      await sleep(2000)
      await wecomMessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '[示例订单编号截图]',
        send_msg: {
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/%E8%AE%A2%E5%8D%95%E7%BC%96%E5%8F%B7%E6%88%AA%E5%9B%BE.jpg',
        },
      })
    }

    await catchError(JuziAPI.updateUserAlias(Config.setting?.wechatConfig?.id as string, userId, `已付${chat.course_no?.toString()}`))
    try {
      await EventHandler.inviteToGroup(chatId, userId)
    } catch (e) {
      logger.error('拉群失败', e)

      await chatStateStoreClient.update(chatId, {
        state: {
          is_invite_group_fail_after_payment: true
        }
      })
      // 通知进群失败
      await HumanTransfer.transfer(chatId, userId, HumanTransferType.FailedToJoinGroup, true)
    }
  }

  /**
   * 处理商品点击事件
   * @param callback 回调数据
   */
  async handleProductClick(callback: ProductClickCallback) {
    console.log('商品点击:', JSON.stringify(callback, null, 4))
    const chat = await chatDBClient.getChatByPhone(callback.param.mobile)
    if (!chat) { return }

    const chatId =  chat.id

    // 记录商品点击事件
    eventTrackClient.track(chatId, IEventType.ProductClick, {
      roomId: callback.param.roomId,
      pushTime: callback.pushTime
    })

    // 如果已经处理过下单失败，退出
    const limiterPayment = new RateLimiter({
      windowSize: 120 * 60,
      maxRequests: 2
    })
    const currentTime = await DataService.getCurrentTime(chatId)
    const nodeName = `handled_product_click_day${currentTime.day}`
    const isAllowed = await limiterPayment.isAllowed(nodeName, chatId)
    if (!isAllowed) { return }

    // 点击订单后超过5分钟没有付款，视为下单失败
    await SilentReAsk.schedule(TaskName.product_click, chatId,  3 * 60 * 1000) // 3分钟后检查
  }

  async handleOrderClose(callback: OrderCloseCallback) {
    console.log('订单关闭:', JSON.stringify(callback, null, 4))
    const chat = await chatDBClient.getChatByPhone(callback.param.mobile)
    if (!chat) { return }

    const chatId =  chat.id

    // 如果已经处理过下单失败，退出
    const limiterPayment = new RateLimiter({
      windowSize: 120 * 60,
      maxRequests: 2
    })
    const currentTime = await DataService.getCurrentTime(chatId)
    const nodeName = `handled_order_close_day${currentTime.day}`
    const isAllowed = await limiterPayment.isAllowed(nodeName, chatId)

    if (!isAllowed) { return }

    // 关闭订单后超过20秒没有付款，视为下单失败
    await SilentReAsk.schedule(TaskName.order_close, chatId,  20 * 1000) // 20秒后检查
  }

  public static async inviteToGroup(chatId: string, userId: string) {
    const externalUserId = await JuziAPI.wxIdToExternalUserId(userId)
    if (!externalUserId) {
      throw new Error('获取 ExternalUserId 失败')
    }

    const groupTeacher = await PrismaMongoClient.getInstance().group_teacher.findMany({
      where: {
        accountWechatId: Config.setting.wechatConfig?.id as string
      }
    })

    if (groupTeacher.length === 0) {
      throw new Error('获取拉群老师失败')
    }

    const chat = await chatDBClient.getById(chatId)
    const contactName = chat ? chat.contact.wx_name : ''

    // 处理跨企业的情况
    if (!groupTeacher[0].wecomUserId || groupTeacher[0].wecomUserId.length === 0) {
      await this.inviteToGroupWithoutWecomUserId(contactName, externalUserId, groupTeacher[0].imContactId)
      return
    } else if (groupTeacher[0].wecomUserId.length === 32) {
      await this.inviteToGroupWithTeacherExternalId(contactName, externalUserId, groupTeacher[0].imContactId, groupTeacher[0].wecomUserId)
      return
    }

    const data = await JuziAPI.createRoom({
      botUserId: Config.setting.wechatConfig?.botUserId as string,
      userIds: [groupTeacher[0].wecomUserId],
      name: `银河🪐陪跑营【${contactName}】`,
      greeting: '恭喜同学成功加入陪跑团队\n陪跑老师上班时间：早10晚7点，明早上班后对接完信息，再第一时间回复你哈，也会把老师拉进群一起陪跑你！',
      externalUserIds: [externalUserId]
    })

    await sleep(5000)

    const roomId = data.roomWxid
    await JuziAPI.changeRoomOwner(roomId, Config.setting.wechatConfig?.id as string, groupTeacher[0].imContactId)
  }

  private static async inviteToGroupWithoutWecomUserId(contactName: string, externalUserId: string, teacherUserId: string) {
    const externalTeacherId = await JuziAPI.wxIdToExternalUserId(teacherUserId)
    if (!externalTeacherId) {
      throw new Error('获取老师 ExternalUserId 失败')
    }

    const data = await JuziAPI.createRoom({
      botUserId: Config.setting.wechatConfig?.botUserId as string,
      userIds: [Config.setting.wechatConfig?.botUserId as string],
      name: `银河🪐陪跑营【${contactName}】`,
      greeting: '恭喜同学成功加入陪跑团队\n陪跑老师上班时间：早10晚7点，明早上班后对接完信息，再第一时间回复你哈，也会把老师拉进群一起陪跑你！',
      externalUserIds: [externalUserId, externalTeacherId]
    })

    await sleep(5000)

    const roomId = data.roomWxid
    await JuziAPI.changeRoomOwner(roomId, Config.setting.wechatConfig?.id as string, teacherUserId)
  }

  private static async inviteToGroupWithTeacherExternalId(contactName: string, externalUserId: string, teacherUserId: string, teacherExternalUserId: string) {

    const data = await JuziAPI.createRoom({
      botUserId: Config.setting.wechatConfig?.botUserId as string,
      userIds: [Config.setting.wechatConfig?.botUserId as string],
      name: `银河🪐陪跑营【${contactName}】`,
      greeting: '恭喜同学成功加入陪跑团队\n陪跑老师上班时间：早10晚7点，明早上班后对接完信息，再第一时间回复你哈，也会把老师拉进群一起陪跑你！',
      externalUserIds: [externalUserId, teacherExternalUserId]
    })

    await sleep(5000)

    const roomId = data.roomWxid
    await JuziAPI.changeRoomOwner(roomId, Config.setting.wechatConfig?.id as string, teacherUserId)
  }


}

export async function sendYuHeWelComeMessage(chatId:string, userId:string) {
  const lock = new AsyncLock()
  await lock.acquire(chatId, async () => { // 如果有新消息，当前回复会被丢弃
    await chatStateStoreClient.update(chatId, {
      state: {
        is_friend_accepted: true
      }
    })

    const mongoClient = PrismaMongoClient.getInstance()
    const chat = await chatDBClient.getById(chatId)

    // 如果是迁移账号，课程期数和 手机号已有
    if (chat?.course_no) {
      const courseStartTime = dayjs(chat.course_no.toString(10), 'YYYYMMDD')

      // 讲一下上课期数
      await sendMsg(userId, chatId, `👏欢迎来到【AI实体线上获客】速成班！
您好！我是宇合实体获客操盘手-${Config.setting.AGENT_NAME}，您的专属助教，本次课程是全程直播
🕖课程时间：${courseStartTime.format('M月D日')}-${courseStartTime.add(3, 'day').format('M月D日')} 每晚18：50 连续四天（明晚就是第一课）
📌上课方式：微信上提前发上课入口，点链接上课，登录账号是你直播间购课手机号
----
温馨提示⚠️ 在抖音上面主动私信你都是骗子，我只会在这个微信上和你联系`, '欢迎语')


      // 没有手机号要一下手机号
      if (!chat.phone) {
        await sleep(5000)

        await sendMsg(userId, chatId, '咱们报名手机号是什么呀，老师需要手动给咱们开通下权限哈', '询问客户报名手机号')
        await chatStateStoreClient.update(chatId, {
          nextStage: Node.PhoneQuery
        })
      }

      return
    }


    if (chat) {
      const userSlotService = new ExtractUserSlots()
      userSlotService.extractUserSlotsFromChatHistory({ chatId, chatHistory:[{
        role: 'user',
        date: dayjs().format('YYYY-MM-DD'),
        message: chat.contact.wx_name
      }], topicRecommendations: userSlotService.getTopicRecommendations(), topicRules:userSlotService.getTopicRules() })
    }

    if (chat) {
    // 更新备注
      await catchError(JuziAPI.updateUserAlias(Config.setting?.wechatConfig?.id as string, userId,  dayjs().format('YYMMDD') + chat.contact.wx_name))
    }

    await mongoClient.chat.update({ where:{ id:chatId }, data:{ course_no:DataService.getCurrentCourseNo() } })

    await startTasks(chatStateStoreClient, 'yuhe', userId, chatId, calTaskTime)

    const courseStartTime = dayjs(await DataService.getCourseStartTimeByChatId(chatId))

    await sendMsg(userId, chatId, [`👏欢迎来到【AI实体线上获客】速成班！
您好！我是宇合实体获客操盘手-${Config.setting.AGENT_NAME}，您的专属助教，本次课程是全程直播
🕖课程时间：${courseStartTime.format('M月D日')}-${courseStartTime.add(3, 'day').format('M月D日')} 每晚18：50 连续四天（明晚就是第一课）
📌上课方式：微信上提前发上课入口，点链接上课，登录账号是你直播间购课手机号
----
温馨提示⚠️ 在抖音上面主动私信你都是骗子，我只会在这个微信上和你联系`, <ISendMedia>{
  description:'欢迎语介绍课程的图片',
  msg:{
    type: IWecomMsgType.Image,
    url:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/%E6%AC%A2%E8%BF%8E%E8%AF%AD%E4%B8%AD%E4%BB%8B%E7%BB%8D%E8%AF%BE%E7%A8%8B%E5%9B%BE%E7%89%87.jpg'
  },
}
    ], '欢迎语')

    // 防止手机号标签还没变就检测
    await sleep(10 * 1000)

    let phoneNumber = await DataService.bindPhoneFromRemark(chatId)
    if (!phoneNumber) {
      const phoneNumberFromName = RegexHelper.extractPhoneNumber(chat?.contact.wx_name ?? '')
      if (phoneNumberFromName) {
        await DataService.bindPhone(chatId, phoneNumberFromName)
        phoneNumber = phoneNumberFromName
      }
    }

    if (!phoneNumber) {
      await sendMsg(userId, chatId, '咱们报名手机号是什么呀，老师需要手动给咱们开通下权限哈', '询问客户报名手机号')
      await chatStateStoreClient.update(chatId, {
        nextStage: Node.PhoneQuery
      })
      await SilentReAsk.schedule(
        TaskName.phone_check,
        chatId,
        30 * 60 * 1000,
        undefined,
        {
          auto_retry: true,
          independent: true
        }
      )
    } else {
      await startAskIntention(chatId, userId)
    }

  }, { timeout: 2 * 60 * 1000 })
}

export async function startAskIntention(chatId:string, userId:string) {
  await firstAskIntention(chatId, userId)

  await SilentReAsk.schedule(
    TaskName.ask_intention,
    chatId,
    10 * 60 * 1000,
    undefined,
    { auto_retry: true, independent: true }
  )

  await SilentReAsk.schedule(
    TaskName.ask_intention_reminder,
    chatId,
    60 * 60 * 1000,
    undefined,
    { independent: true }
  )

  await chatStateStoreClient.update(chatId, {
    nextStage: Node.IntentionQuery
  })
}

export async function firstAskIntention(chatId:string, userId:string) {
  const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
  if (state.is_send_first_ask_intention) {
    return
  }
  await chatStateStoreClient.update(chatId, {
    state:<IChattingFlag>{
      is_send_first_ask_intention:true
    }
  })

  await sleep(15 * 1000)

  await sendMsg(userId, chatId, [{
    description:'周一欢迎语介绍课程', msg:{
      type:IWecomMsgType.Voice,
      voiceUrl:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/voice/%E5%AE%87%E5%90%88%E6%AC%A2%E8%BF%8E%E8%AF%AD5.29WW.silk',
      duration: 25
    }
  }, `✅为了更针对性地帮助你学习吸收，请你完成好以下信息发给老师❤️
1、怎么称呼您：
2、性别：
3、个人实体店/连锁店：
4、行业类目：
5、年营业额：
6、是否抖音在做：
7、你想要解决的问题：
8、所在城市：`], '周一欢迎语介绍课程')
}


export async function isInClassTime(chatId:string) {
  const days = [1, 2, 3, 4]
  for (const day of days) {
    if (await DataService.isInCourseTimeLine(chatId, 'inCourse', day)) {
      return true
    }
  }
  return false
}