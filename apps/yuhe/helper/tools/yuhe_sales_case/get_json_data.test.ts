
import { FileHelper } from 'lib/file'


//读取文件夹下文件列表
import fs from 'fs'

describe('yuheSalesCaseFormat', () => {
  it('getJsonData', async () => {
    const dirPath =
      '/Users/<USER>/Desktop/code/eliza-sales/apps/yuhe/helper/tools/yuhe_sales_case/images'
    await generateJsonData(dirPath)
  }, 9e8)
})

async function generateJsonData(dirPath: string) {
  const files = fs.readdirSync(dirPath)
  const res: any[] = []
  for (const file of files) {
    const fileName = file.split('.')[0]
    //清楚fileName中的数字
    const fileNameNoNumber = fileName.replace(/[0-9]/g, '')
    const topic = fileNameNoNumber.split('_')[0]
    const userName = fileNameNoNumber.split('_')[1]
    const searchRes = res.find(
      (item) => item.topic === topic && item.name === userName,
    )
    if (!searchRes) {
      res.push({
        q: topic,
        topic: topic,
        name: userName,
        description: '',
        successReason: '',
        doc: '陪跑营案例',
        images: [],
      })
    }
  }

  for (const file of files) {
    const fileName = file.split('.')[0]
    const fileNameNoNumber = fileName.replace(/[0-9]/g, '')
    const topic = fileNameNoNumber.split('_')[0]
    const userName = fileNameNoNumber.split('_')[1]
    const searchRes = res.find(
      (item) => item.topic === topic && item.name === userName,
    )
    //url编码文件名
    const url = `https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sales_case/${encodeURIComponent(file)}`
    searchRes.images.push(url)
  }

  await FileHelper.writeFile(
    '/Users/<USER>/Desktop/code/eliza-sales/apps/yuhe/helper/tools/yuhe_sales_case/sales_case_data.json',
    JSON.stringify(res),
  )
}
