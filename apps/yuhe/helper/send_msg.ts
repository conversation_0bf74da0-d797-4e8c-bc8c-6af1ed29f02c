import { ISendMedia } from 'service/visualized_sop/visualized_sop_type'
import { IWecomMsgType, IWecomTextMsg } from 'model/juzi/type'
import { commonSleep } from 'lib/schedule/schedule'
import { chatHistoryServiceClient } from '../service/base_instance'
import { wecomMessageSender } from '../service/send_message_instance'

/**
 *
 * @param userId
 * @param chatId
 * @param messages
 * @param description
 * @param isRepeatedCheck
 * @param roundId
 * @param sop_id
 * @returns
 * @deprecated 不要使用sendMsg了，请使用wecomCommonMessageSender替换
 */
export async function sendMsg(userId: string, chatId: string,  messages: (ISendMedia | string)[] | string, description?: string, isRepeatedCheck?: boolean, roundId?:string, sop_id?:string): Promise<void> {
  // 这里注意下，所有 消息重复的话都不进行发送了
  if (typeof messages === 'string') {
    const isRepeated = await chatHistoryServiceClient.hasRepeatedMsg(chatId, messages)
    if (isRepeated && !isRepeatedCheck) {
      return
    }

    await wecomMessageSender.sendById({
      user_id: userId,
      chat_id: chatId,
      ai_msg: messages,
    }, {
      shortDes: description ? `[${description}]` : undefined,
      round_id:roundId,
      sop_id
    })
    return
  }

  for (let i = 0; i < messages.length; i++) {
    const message = messages[i]
    if (typeof message === 'string') {
      const isRepeated = await chatHistoryServiceClient.hasRepeatedMsg(chatId, message)
      if (isRepeated && !isRepeatedCheck) {
        return
      }

      await wecomMessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: message,
      }, {
        round_id:roundId,
        shortDes: description ? `[${description}]` : undefined,
        sop_id
      })
    } else {
      // 文件，图片类型根据描述去重
      let checkRepeatedText = message.description

      // 对文本不进行根据描述去重
      if (message.msg.type === IWecomMsgType.Text) {
        checkRepeatedText = (message.msg as IWecomTextMsg).text
      }

      const isRepeated = await chatHistoryServiceClient.hasRepeatedMsg(chatId, checkRepeatedText)
      if (isRepeated && !isRepeatedCheck) {
        return
      }

      await wecomMessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: `[${message.description}]`,
        send_msg: message.msg,
      }, {
        shortDes: `[${ message.description  }]`,
        round_id:roundId,
        sop_id
      })
    }

    if (i < messages.length - 1) {
      await commonSleep()
    }
  }
}