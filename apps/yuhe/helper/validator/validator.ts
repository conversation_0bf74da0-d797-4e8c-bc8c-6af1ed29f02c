import { DataService } from '../getter/get_data'
import dayjs from 'dayjs'
import { YuHeConfigNotify } from './notify'
import { XingyanAPI } from 'model/xingyan'
import { catchError } from 'lib/error/catchError'

export class YuHeValidator {
  static async validateLiveStream(prevDay?: boolean) {
    let date = new Date()

    if (prevDay) {
      // 获取明天的日期
      date = dayjs().add(1, 'day').toDate()
    }

    const courseDate = dayjs(date).format('YY/M/D')

    // 获取今天的直播间信息
    const liveRooms = await DataService.getLiveStreamCourseInfoByDate(date)

    if (!liveRooms) {
      await YuHeConfigNotify.notify(`${courseDate} 没有找到直播间信息, 请建立 "${courseDate}中神通" 的直播分组`)

      throw new Error(`${courseDate} 没有找到直播间信息, 请建立 "${courseDate}中神通" 的直播分组`)
    }

    // 直播间进行配置校验
    for (const iLiveRoom of liveRooms.detailList) {
      const lastChar = iLiveRoom.roomName[iLiveRoom.roomName.length - 1]
      if (!(['1', '2', '3', '4'].includes(lastChar))) {
        // 匹配以 1、2、3、4 结尾的 roomName
        await YuHeConfigNotify.notify(`${iLiveRoom.roomName} 直播间名称错误, 请修改为以数字1-4结尾`)
      }

      if (iLiveRoom.authType !== 2) {
        await YuHeConfigNotify.notify(`${iLiveRoom.roomName} 直播间权限配置错误, 请修改为手机验证观看`)
      }

      // 检查直播间回放配置
      const [error, status] = await catchError(XingyanAPI.getRoomStatus(iLiveRoom.roomId))

      if (error || (status && status.roomStatus !== 3)) {
        await YuHeConfigNotify.notify(`${iLiveRoom.roomName} 直播间回放配置错误, 请检查是否开启回放`)
      }
    }
  }
}