import { UserSlots } from 'service/user_slots/extract_user_slots'
import ElasticSearchService, { IElasticEmbeddingRes } from 'model/elastic_search/elastic_search'
import { chatStateStoreClient } from '../../service/base_instance'

export interface IYuheSalesCase{
  topic: string
  description: string
  images: string[]
}


export class SalesCase {
  public static index = 'yuhe_sales_case'

  public static peipaoyingDoc = '陪跑营案例'


  public static async searchByChatId(chatId: string, docs: string[]) {
    const topic = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chatId, '基本信息', '行业类目')

    const searchResults = await this.embeddingSearch(topic, docs)

    if (searchResults.length === 0) {
      return null
    }

    //从searchResult中随机获取一个
    const res = searchResults[Math.floor(Math.random() * searchResults.length)]

    console.log(res)

    const salesCase:IYuheSalesCase = {
      topic: res.metadata.topic,
      description: res.metadata.description,
      images: JSON.parse(res.metadata.images)
    }
    return salesCase
  }


  private static async embeddingSearch(query: string, docs: string[]): Promise<IElasticEmbeddingRes[]> {
    // const filter = {
    //   terms: {
    //     'metadata.doc': docs
    //   }
    // }

    // Embedding Search
    return await ElasticSearchService.embeddingSearch(
      this.index,
      query,
      3,
      0.7
    )
  }
}