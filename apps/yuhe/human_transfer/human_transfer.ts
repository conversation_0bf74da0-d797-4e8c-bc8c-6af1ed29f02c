import logger from 'model/logger/logger'
import { accountToName } from '../config/config'
import { DataService } from '../helper/getter/get_data'
import { eventTrackClient } from '../service/event_track_instance'
import { humanTransferClient } from '../service/human_transfer_instance'
import { IEventType } from 'model/logger/data_driven'

export class HumanTransfer {
  public static async transfer(chatId: string, userId: string, transferMessage: string, toHuman: boolean | 'onlyNotify' = true, additionalMsg?: string) {
    if (userId === 'null') {
      logger.error('[HumanTransfer] userId is null', transferMessage)
      return
    }
    eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: transferMessage })
    const ip = await DataService.getIpByChatId(chatId)
    const courseNo = await DataService.getCourseNoByChatId(chatId) ?? 2025
    const accountName = accountToName[chatId.split('_')[1]] ?? '未知'
    const handleType = toHuman === true ? '请人工处理' : '请观察👀'
    const message = `（${ ip }${ courseNo }${ accountName }）${ transferMessage }，${ handleType }${ additionalMsg ? `\n${ additionalMsg }` : ''}`
    return await humanTransferClient.transferWithMessage(chatId, userId, message, toHuman)
  }
}
