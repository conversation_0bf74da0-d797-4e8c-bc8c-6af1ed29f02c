generator client {
  provider = "prisma-client-js"
  output   = "../prisma_client"
}

datasource db {
  provider = "mongodb"
  url      = "mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/moer?authSource=admin"
}

model material {
  id            String  @id @default(auto()) @map("_id") @db.ObjectId
  type          Int
  title         String
  description   String
  es_id         String
  enable        Boolean
  main_category String
  sub_category  String
  doc           String
  data          Json
}
