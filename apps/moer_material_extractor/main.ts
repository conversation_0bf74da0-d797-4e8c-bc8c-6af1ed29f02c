import express from 'express'
import { IReceivedLinkMsg, IReceivedMessage, IReceivedVideoChannelMsg } from 'model/juzi/type'
import { PrismaMongoClient } from './prisma/prisma'
const app = express().use(express.json())

app.post('/message', async (req, res) => {
  console.log(JSON.stringify(req.body))
  handleMessage(req.body)
  res.send('ok')
})

app.listen(4023, '0.0.0.0', () => {
  console.log('start')
})

async function handleMessage(msg:IReceivedMessage):Promise<void> {
  if (msg.messageType == 12) {
    await addLinkCard(msg.payload as IReceivedLinkMsg)
  } else if (msg.messageType == 14) {
    await addVideoChannel(msg.payload as IReceivedVideoChannelMsg)
  }
}

async function addVideoChannel(payload:IReceivedVideoChannelMsg):Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.material.create({ data:{
    type:107,
    title:payload.description,
    description:payload.description,
    es_id:'',
    enable:false,
    main_category:'',
    sub_category:'',
    doc:'',
    data: payload as any
  } })
}

async function addLinkCard(payload:IReceivedLinkMsg):Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.material.create({ data:{
    type:106,
    title:payload.title,
    description:payload.description,
    es_id:'',
    enable:false,
    main_category:'',
    sub_category:'',
    doc:'',
    data: payload as any
  } })
}
