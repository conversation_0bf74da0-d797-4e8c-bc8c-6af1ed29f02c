# Haogu数据同步服务

MongoDB到PostgreSQL的近实时数据同步服务，支持Metabase BI分析。

## 架构设计

```
多项目MongoDB → Change Streams → Redis Queue → 数据处理服务 → PostgreSQL → Metabase
    ├─ haogu.chat
    ├─ yuhe.chat 
    └─ moer_overseas.chat
```

**核心特性**：
- 🔄 **多项目支持**: 单个服务实例同时监听haogu、yuhe、moer_overseas多个项目
- 🏗️ **环境隔离**: 只按dev/test/prod环境区分部署，不按项目区分
- 📊 **统一分析**: 所有项目数据汇总到同一PostgreSQL进行BI分析

## 功能特性

- ✅ **Change Streams监听**: 实时监听MongoDB数据变更
- ✅ **Redis队列**: 基于BullMQ的可靠任务队列
- ✅ **数据转换**: 嵌套JSON扁平化为关系型结构
- ✅ **事务安全**: PostgreSQL事务确保数据一致性
- ✅ **错误重试**: 自动重试和错误恢复机制
- ✅ **监控指标**: 队列状态和同步统计

## 表结构设计

### 核心表
- `users`: 用户基础信息
- `user_states`: 用户状态（长表设计，支持动态字段）
- `user_slots`: 用户画像信息（按类别分组）
- `node_invoke_stats`: 节点调用统计

### 长表设计优势
1. **动态扩展**: 新增状态字段无需修改表结构
2. **BI友好**: 支持灵活的筛选、分组和聚合分析
3. **存储高效**: 只存储实际存在的数据，避免大量NULL值

## 本地开发和测试

### 环境要求
- Node.js >= 18
- PostgreSQL (Docker)
- Redis
- MongoDB访问权限

### 快速开始

1. **安装依赖**
```bash
cd apps/data_sync
pnpm install
```

2. **运行测试**
```bash
# 单元测试
pnpm test
```

3. **启动同步服务**
```bash
# 开发模式（同时监听所有项目：haogu, yuhe, moer_overseas）
pnpm run dev

# 生产模式  
pnpm run start:prod

# 自定义环境启动
pnpm start -- --env=test
```

### 环境变量配置

```bash
# 环境设置
NODE_ENV=prod  # 或 ENV=prod

# PostgreSQL配置（生产环境）
POSTGRES_URI=*********************************************/analytics_prod

# Redis配置（生产环境，可选）
REDIS_HOST=r-uf6xk3zjd1mzs4540dpd.redis.rds.aliyuncs.com
REDIS_PASSWORD=your_password
```

## 监控和运维

### 队列监控
服务启动后每30秒输出队列状态：
```
队列状态: 等待0 | 处理中1 | 已完成156 | 失败2
PostgreSQL: 用户9 | 状态55 | 插槽94
```

## 推荐BI分析查询

### 1. 状态完成率分析
```sql
SELECT 
  state_key,
  COUNT(CASE WHEN state_value = true THEN 1 END) as 完成用户数,
  COUNT(*) as 总用户数,
  ROUND(100.0 * COUNT(CASE WHEN state_value = true THEN 1 END) / COUNT(*), 2) as 完成率
FROM user_states 
GROUP BY state_key
ORDER BY 完成率 DESC;
```

### 2. 用户转化漏斗
```sql
WITH user_progress AS (
  SELECT user_id,
    MAX(CASE WHEN state_key = 'is_friend_accepted' AND state_value = true THEN 1 ELSE 0 END) as 加好友,
    MAX(CASE WHEN state_key = 'is_add_tasks' AND state_value = true THEN 1 ELSE 0 END) as 添加任务,
    MAX(CASE WHEN state_key = 'is_send_core_materials' AND state_value = true THEN 1 ELSE 0 END) as 发送资料,
    MAX(CASE WHEN state_key = 'is_finish_stock_ranking_assessment' AND state_value = true THEN 1 ELSE 0 END) as 完成评估
  FROM user_states GROUP BY user_id
)
SELECT 
  SUM(加好友) as 加好友完成数,
  SUM(添加任务) as 添加任务完成数,
  SUM(发送资料) as 发送资料完成数,
  SUM(完成评估) as 完成评估数
FROM user_progress;
```

### 3. 用户画像分析
```sql
SELECT 
  category,
  slot_key,
  content,
  COUNT(*) as 用户数
FROM user_slots 
WHERE category IN ('投资基础', '基本信息')
GROUP BY category, slot_key, content
ORDER BY category, 用户数 DESC;
```

## 故障排查

### 常见问题
1. **MongoDB连接失败**: 检查网络和认证信息
2. **Redis连接失败**: 确认Redis服务状态
3. **PostgreSQL表不存在**: 运行`ensureTablesExist()`
4. **队列任务堆积**: 检查Worker并发数和PostgreSQL性能

### 日志位置
- Change Stream日志: 控制台输出
- 队列处理日志: BullMQ内置日志
- PostgreSQL错误: 事务回滚日志