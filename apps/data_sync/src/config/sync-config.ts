export type Environment = 'dev' | 'test' | 'prod'
export type ProjectName = 'haogu' // | 'yuhe' | 'moer_overseas'

export interface ProjectConfig {
  dbName: string
  collectionName: string
  fullUri: string
}

export interface ProjectBaseConfig {
  dbName: string
  collectionName: string
}

export interface SyncConfig {
  environment: Environment
  mongodb: {
    baseUri: string
    authSource: string
  }
  postgresql: {
    baseUri: string
    databases: Record<ProjectName, string>
  }
  redis: {
    host: string
    password: string
  }
  queue: {
    name: string
    concurrency: number
    retryAttempts: number
  }
  projects: Record<ProjectName, ProjectConfig>
}

// 基础MongoDB连接URL（不包含数据库名）
const MONGODB_BASE_URI = 'mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717'

// 环境配置
const ENV_CONFIGS = {
  dev: {
    postgresql: {
      baseUri: 'postgresql://dev:dev@localhost:5432', // 基础连接，不包含库名
      databases: {
        haogu: 'haogu_analytics', // 使用现有analytics库
        // yuhe: 'yuhe_analytics',
        // moer_overseas: 'moer_analytics'
      }
    },
    redis: {
      host: '127.0.0.1', // Docker端口映射到本地
      password: '123456'
    },
    queue: {
      concurrency: 2,
      retryAttempts: 3
    }
  },
  test: {
    postgresql: {
      baseUri: 'postgresql://freespirit_admin:FreeSpirit%401234%23DB%21@*************:5432',
      databases: {
        haogu: 'haogu_analytics', // 使用现有analytics库
        // yuhe: 'yuhe_analytics',
        // moer_overseas: 'moer_analytics'
      }
    },
    redis: {
      host: 'r-uf6xk3zjd1mzs4540dpd.redis.rds.aliyuncs.com',
      password: 'free1234$spirit!'
    },
    queue: {
      concurrency: 2,
      retryAttempts: 3
    }
  },
  prod: {
    postgresql: {
      baseUri: 'postgresql://freespirit_admin:FreeSpirit%401234%23DB%21@*************:5432',
      databases: {
        haogu: 'haogu_analytics',
      }
    },
    redis: {
      host: 'r-uf6xk3zjd1mzs4540dpd.redis.rds.aliyuncs.com',
      password: 'free1234$spirit!'
    },
    queue: {
      concurrency: 2,
      retryAttempts: 3
    }
  }
}

// 项目数据库配置（业务逻辑层面的配置）
const PROJECT_CONFIGS: Record<ProjectName, ProjectBaseConfig> = {
  haogu: {
    dbName: 'haogu', // MongoDB数据库名
    collectionName: 'chat'
  },
  // yuhe: {
  //   dbName: 'yuhe', // yuhe使用free_spirit库
  //   collectionName: 'chat'
  // },
  // moer_overseas: {
  //   dbName: 'moer_overseas', // moer_overseas也使用free_spirit库
  //   collectionName: 'chat'
  // }
}

export function createSyncConfig(
  environment: Environment
): SyncConfig {
  const envConfig = ENV_CONFIGS[environment]
  // 为每个项目构建完整的MongoDB URI
  const projects = {} as Record<ProjectName, ProjectConfig>
  for (const [projectName, projectConfig] of Object.entries(PROJECT_CONFIGS)) {
    projects[projectName as ProjectName] = {
      ...projectConfig,
      fullUri: `${MONGODB_BASE_URI}/${projectConfig.dbName}?authSource=admin`
    }
  }

  return {
    environment,
    mongodb: {
      baseUri: MONGODB_BASE_URI,
      authSource: 'admin'
    },
    postgresql: envConfig.postgresql,
    redis: envConfig.redis,
    queue: {
      name: `data-sync-${environment}`, // 只按环境隔离
      concurrency: envConfig.queue.concurrency,
      retryAttempts: envConfig.queue.retryAttempts
    },
    projects
  }
}

/**
 * 从命令行参数和环境变量解析配置
 */
export function parseConfigFromArgs(): SyncConfig {
  // 从命令行参数解析
  const args = process.argv.slice(2)
  const envArg = args.find((arg) => arg.startsWith('--env='))?.split('=')[1] as Environment

  // 从环境变量解析
  const envFromEnv = (process.env.NODE_ENV || process.env.ENV) as Environment

  // 优先级：命令行参数 > 环境变量 > 默认值
  const environment = envArg || envFromEnv || 'dev'

  console.log(`📋 使用配置: 环境=${environment}`)
  console.log(`📋 监听项目: ${Object.keys(PROJECT_CONFIGS).join(', ')}`)

  return createSyncConfig(environment)
}