import { ChangeStreamService } from './services/change-stream-service'
import { SyncQueueService } from './services/sync-queue-service'
import { parseConfigFromArgs } from './config/sync-config'

export class DataSyncApplication {
  private changeStreamService: ChangeStreamService
  private queueService: SyncQueueService
  private config = parseConfigFromArgs()

  constructor() {
    this.changeStreamService = new ChangeStreamService(this.config)
    this.queueService = new SyncQueueService(this.config)
  }

  async start(): Promise<void> {
    try {
      console.log('启动数据同步服务...')

      // 1. 启动队列处理服务
      await this.queueService.start()

      // 2. 启动Change Stream监听
      await this.changeStreamService.start()

      console.log('数据同步服务启动成功!')
      console.log(`监听项目: ${Object.keys(this.config.projects).join(', ')}`)
      console.log(`PostgreSQL分库: ${Object.values(this.config.postgresql.databases).join(', ')}`)
      console.log(`redis队列: ${this.config.queue.name}`)

      // 设置监控定时器
      this.startMonitoring()

      // 优雅关闭处理
      this.setupGracefulShutdown()

    } catch (error) {
      console.error('数据同步服务启动失败:', error)
      throw error
    }
  }

  private startMonitoring(): void {
    // 每30秒输出一次队列状态
    setInterval(async () => {
      try {
        const metrics = await this.queueService.getQueueMetrics()
        console.log(`队列状态: 等待${metrics.queue.waiting} | 处理中${metrics.queue.active} | 已完成${metrics.queue.completed} | 失败${metrics.queue.failed}`)
        console.log(`PostgreSQL: 用户${metrics.postgres.haogu?.users || 0} | 状态${metrics.postgres.haogu?.states || 0} | 插槽${metrics.postgres.haogu?.slots || 0}`)
      } catch (error) {
        console.error('获取监控指标失败:', error)
      }
    }, 30000)

    // 每小时清理一次已完成的任务
    setInterval(async () => {
      try {
        await this.queueService.cleanupCompletedJobs()
      } catch (error) {
        console.error('队列清理失败:', error)
      }
    }, 60 * 60 * 1000)
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      console.log(`接收到${signal}信号，开始优雅关闭...`)

      try {
        await this.changeStreamService.stop()
        await this.queueService.stop()
        console.log('数据同步服务已安全关闭')
        process.exit(0)
      } catch (error) {
        console.error('关闭服务时发生错误:', error)
        process.exit(1)
      }
    }

    process.on('SIGINT', () => shutdown('SIGINT'))
    process.on('SIGTERM', () => shutdown('SIGTERM'))
  }

  /**
   * 获取服务运行状态
   */
  async getStatus() {
    const metrics = await this.queueService.getQueueMetrics()
    return {
      status: 'running',
      config: {
        environment: this.config.environment,
        projects: Object.keys(this.config.projects),
        queueName: this.config.queue.name
      },
      metrics
    }
  }
}

// 启动服务
async function bootstrap() {
  console.log('解析启动配置...')
  const app = new DataSyncApplication()

  try {
    await app.start()
  } catch (error) {
    console.error('启动失败:', error)
    process.exit(1)
  }
}

// 仅在直接运行时启动服务
if (require.main === module) {
  bootstrap().catch(console.error)
}