import { DataSyncApplication } from '../../main'
import { MongoChat } from '../../types/chat'

describe('DataSync Integration Tests', () => {
  let app: DataSyncApplication

  beforeAll(async () => {
    // 这些测试需要真实的数据库连接，标记为集成测试
    if (process.env.NODE_ENV !== 'integration') {
      console.log('跳过集成测试 - 设置 NODE_ENV=integration 来运行')

    }
  })

  beforeEach(() => {
    app = new DataSyncApplication()
  })

  describe('完整数据流测试', () => {
    it('应该能够处理完整的MongoDB到PostgreSQL同步流程', async () => {
      if (process.env.NODE_ENV !== 'integration') {
        return
      }

      // 1. 启动服务
      await app.start()

      // 2. 模拟MongoDB变更（实际环境中由Change Stream触发）
      const mockChatData: MongoChat = {
        _id: 'integration_test_user',
        wx_id: 'wx_integration_test',
        contact: {
          wx_id: 'wx_integration_test',
          wx_name: 'IntegrationTestUser'
        },
        chat_state: {
          nextStage: 'free_talk',
          state: {
            'is_friend_accepted': true,
            'is_add_tasks': true
          },
          userSlots: {
            '基本信息::称呼': {
              content: 'IntegrationTestUser',
              frequency: 1
            }
          },
          nodeInvokeCount: {
            'UserMessage': 3
          }
        },
        course_no: 20250829,
        created_at: new Date(),
        is_deleted: false,
        is_human_involved: false
      }

      // 3. 验证数据转换和同步
      // 注意：这里需要实际的数据库来验证

      // 4. 检查服务状态
      const status = await app.getStatus()
      expect(status.status).toBe('running')
      expect(status.config.projects).toContain('haogu')

    }, 30000) // 30秒超时，因为涉及网络请求
  })

  afterAll(async () => {
    if (app) {
      // 清理测试环境
    }
  })
})