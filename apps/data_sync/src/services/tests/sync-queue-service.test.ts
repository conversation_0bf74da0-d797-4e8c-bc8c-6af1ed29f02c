import { SyncQueueService } from '../sync-queue-service'
import { createSyncConfig } from '../../config/sync-config'
import { SyncJobData } from '../../types/chat'

// Mock依赖
jest.mock('model/redis/redis')
jest.mock('../services/postgresql-sync-service')

describe('SyncQueueService', () => {
  let service: SyncQueueService
  const mockConfig = createSyncConfig('test')

  beforeEach(() => {
    // 使用测试配置
    mockConfig.postgresql.baseUri = 'postgresql://test:test@localhost:5432/test_analytics'
    mockConfig.queue.name = 'test-sync-queue'

    service = new SyncQueueService(mockConfig)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('addSyncJob', () => {
    it('应该能够添加同步任务到队列', async () => {
      const jobData: SyncJobData = {
        operationType: 'insert',
        documentId: 'test_doc_123',
        fullDocument: {
          _id: 'test_doc_123',
          wx_id: 'wx_test',
          contact: { wx_id: 'wx_test', wx_name: 'TestUser' },
          chat_state: {
            state: { 'is_friend_accepted': true }
          }
        },
        projectName: 'haogu',
        timestamp: new Date()
      }

      // 这里应该mock queue.add方法
      await expect(service.addSyncJob(jobData)).resolves.not.toThrow()
    })
  })

  describe('getQueueMetrics', () => {
    it('应该返回队列监控信息', async () => {
      // Mock队列状态
      const metrics = await service.getQueueMetrics()

      expect(metrics).toHaveProperty('queue')
      expect(metrics).toHaveProperty('postgres')
      expect(metrics).toHaveProperty('isHealthy')
      expect(metrics.queue).toHaveProperty('waiting')
      expect(metrics.queue).toHaveProperty('active')
      expect(metrics.queue).toHaveProperty('completed')
      expect(metrics.queue).toHaveProperty('failed')
    })
  })
})