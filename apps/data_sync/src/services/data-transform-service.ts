import { MongoChat } from '../types/chat'

export interface TransformedData {
  baseUser: {
    id: string
    wx_id: string
    wx_union_id?: string
    customer_tool_user_id?: string
    conversation_id?: string
    wx_name: string
    course_no?: number
    created_at?: Date
    is_deleted: boolean
    is_human_involved: boolean
    next_stage?: string
  }
  userStates: Array<{
    user_id: string
    state_key: string
    state_value: boolean | null
    state_value_number: number | null // 新增：支持数字状态
  }>
  userSlots: Array<{
    user_id: string
    category: string
    slot_key: string
    content: string
    frequency: number
  }>
  nodeStats: Array<{
    user_id: string
    node_name: string
    invoke_count: number
  }>
}

export class DataTransformService {

  /**
   * 将MongoDB嵌套文档转换为PostgreSQL扁平化结构
   * 核心转换逻辑，参考quick_data_sync.ts的成功验证方案
   */
  transformChatDocument(mongoDoc: MongoChat, projectName?: string): TransformedData {
    // 基础用户信息
    const baseUser = {
      id: mongoDoc._id,
      wx_id: mongoDoc.wx_id,
      wx_union_id: mongoDoc.wx_union_id,
      customer_tool_user_id: mongoDoc.customer_tool_user_id,
      conversation_id: mongoDoc.conversation_id,
      wx_name: mongoDoc.contact?.wx_name || '',
      course_no: mongoDoc.course_no,
      created_at: mongoDoc.created_at,
      is_deleted: mongoDoc.is_deleted || false,
      is_human_involved: mongoDoc.is_human_involved || false,
      next_stage: mongoDoc.chat_state?.nextStage
    }

    // 转换用户状态（chat_state.state）
    const userStates: TransformedData['userStates'] = []
    if (mongoDoc.chat_state?.state) {
      for (const [stateKey, stateValue] of Object.entries(mongoDoc.chat_state.state)) {
        // 特殊处理：payment_number等累加字段保留数字，其他转布尔
        const isNumberField = stateKey.includes('number') || stateKey.includes('count') || stateKey.includes('times')
        
        if (isNumberField && typeof stateValue === 'number') {
          // 保留原始数字值
          userStates.push({
            user_id: mongoDoc._id,
            state_key: stateKey,
            state_value: null, // 数字字段不用布尔值
            state_value_number: stateValue
          })
        } else {
          // 转换为布尔值
          let boolValue: boolean
          if (typeof stateValue === 'boolean') {
            boolValue = stateValue
          } else if (typeof stateValue === 'number') {
            boolValue = stateValue > 0
          } else if (typeof stateValue === 'string') {
            boolValue = stateValue === 'true' || stateValue === '1'
          } else {
            boolValue = Boolean(stateValue)
          }
          
          userStates.push({
            user_id: mongoDoc._id,
            state_key: stateKey,
            state_value: boolValue,
            state_value_number: null
          })
        }
      }
    }

    // 转换用户信息插槽（chat_state.userSlots）
    const userSlots: TransformedData['userSlots'] = []
    if (mongoDoc.chat_state?.userSlots) {
      for (const [slotPath, slotData] of Object.entries(mongoDoc.chat_state.userSlots)) {
        // 解析插槽路径：如"基本信息::称呼" -> category="基本信息", slot_key="称呼"
        const [category, slotKey] = slotPath.includes('::')
          ? slotPath.split('::')
          : ['其他', slotPath]
        userSlots.push({
          user_id: mongoDoc._id,
          category: category,
          slot_key: slotKey,
          content: slotData.content,
          frequency: slotData.frequency
        })
      }
    }

    // 转换节点调用统计（chat_state.nodeInvokeCount）
    const nodeStats: TransformedData['nodeStats'] = []
    if (mongoDoc.chat_state?.nodeInvokeCount) {
      for (const [nodeName, count] of Object.entries(mongoDoc.chat_state.nodeInvokeCount)) {
        nodeStats.push({
          user_id: mongoDoc._id,
          node_name: nodeName,
          invoke_count: count
        })
      }
    }

    return {
      baseUser,
      userStates,
      userSlots,
      nodeStats
    }
  }

  /**
   * 验证转换后的数据完整性
   */
  validateTransformedData(data: TransformedData, projectName?: string): boolean {
    // 基础验证
    if (!data.baseUser.id || !data.baseUser.wx_id) {
      console.error(`${projectName} 基础验证失败: id=${data.baseUser.id}, wx_id=${data.baseUser.wx_id}`)
      return false
    }

    // 验证用户状态格式（支持布尔值或数字值）
    for (const state of data.userStates) {
      if (!state.user_id || !state.state_key) {
        console.error(`${projectName} 状态验证失败:`, state)
        return false
      }
      // 必须有state_value或state_value_number其中之一
      if (state.state_value === null && state.state_value_number === null) {
        console.error(`${projectName} 状态值验证失败:`, state)
        return false
      }
    }

    // 验证用户插槽格式
    for (const slot of data.userSlots) {
      if (!slot.user_id || !slot.category || !slot.slot_key) {
        console.error(`${projectName} 插槽验证失败:`, slot)
        return false
      }
    }

    return true
  }

  /**
   * 获取转换统计信息用于日志
   */
  getTransformStats(data: TransformedData) {
    return {
      userId: data.baseUser.id,
      statesCount: data.userStates.length,
      slotsCount: data.userSlots.length,
      nodeStatsCount: data.nodeStats.length,
      categoriesCount: new Set(data.userSlots.map((s) => s.category)).size
    }
  }
}