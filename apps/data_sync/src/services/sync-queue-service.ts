import { Job, Worker, Queue } from 'bullmq'
import { RedisDB } from 'model/redis/redis'
import { SyncConfig, ProjectName } from '../config/sync-config'
import { SyncJobData } from '../types/chat'
import { DataTransformService } from './data-transform-service'
import { PostgreSQLSyncService } from './postgresql-sync-service'

export class SyncQueueService {
  private worker: Worker
  private queue: Queue
  private transformService: DataTransformService
  private postgresService: PostgreSQLSyncService
  private config: SyncConfig

  constructor(config: SyncConfig) {
    this.config = config
    this.transformService = new DataTransformService()
    this.postgresService = new PostgreSQLSyncService(config)

    // 初始化BullMQ队列
    this.queue = new Queue(config.queue.name, {
      connection: RedisDB.getInstance()
    })

    // 初始化Worker
    this.worker = new Worker(config.queue.name, this.processJob.bind(this), {
      connection: RedisDB.getInstance(),
      concurrency: config.queue.concurrency
    })

    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    this.worker.on('completed', (job: Job) => {
      console.log(`任务完成: ${job.id}, 用户: ${job.data.documentId}`)
    })

    this.worker.on('failed', (job: Job | undefined, err: Error) => {
      console.error(`任务失败: ${job?.id}, 错误:`, err)
    })

    this.worker.on('error', (err: Error) => {
      console.error('Worker错误:', err)
    })

    this.queue.on('error', (err: Error) => {
      console.error('Queue错误:', err)
    })
  }

  /**
   * 处理队列中的同步任务
   */
  private async processJob(job: Job<SyncJobData>): Promise<void> {
    const { operationType, documentId, fullDocument, projectName } = job.data

    try {
      console.log(`开始处理: ${projectName} ${operationType} 操作，文档ID: ${documentId}`)

      if (operationType === 'delete') {
        // 删除操作
        await this.postgresService.deleteUserData(documentId, projectName as ProjectName)
        return
      }

      if (!fullDocument) {
        throw new Error(`${operationType} 操作缺少完整文档数据`)
      }

      // 数据转换（添加项目名到转换结果中）
      const transformedData = this.transformService.transformChatDocument(fullDocument, projectName)

      // 验证转换后的数据
      if (!this.transformService.validateTransformedData(transformedData, projectName)) {
        throw new Error('数据转换验证失败')
      }

      // 同步到PostgreSQL
      await this.postgresService.syncUserData(transformedData, projectName as ProjectName)

      // 记录转换统计
      const stats = this.transformService.getTransformStats(transformedData)
      console.log(`${projectName} 数据转换完成:`, stats)

    } catch (error) {
      console.error(`处理任务失败，${projectName} 文档ID: ${documentId}`, error)
      throw error // 重新抛出错误，让BullMQ处理重试
    }
  }

  /**
   * 启动队列处理服务
   */
  async start(): Promise<void> {
    try {
      // 连接PostgreSQL并确保表结构存在
      await this.postgresService.connect()
      // 为每个项目创建表结构
      for (const projectName of Object.keys(this.config.projects) as ProjectName[]) {
        await this.postgresService.ensureTablesExist(projectName)
      }

      console.log(`数据同步队列服务已启动，并发数: ${this.config.queue.concurrency}`)
      console.log(`队列名称: ${this.config.queue.name}`)

    } catch (error) {
      console.error('启动队列服务失败:', error)
      throw error
    }
  }

  /**
   * 停止队列处理服务
   */
  async stop(): Promise<void> {
    try {
      await this.worker.close()
      await this.queue.close()
      await this.postgresService.disconnect()
      console.log('数据同步队列服务已停止')
    } catch (error) {
      console.error('停止队列服务失败:', error)
    }
  }

  /**
   * 手动添加同步任务（用于补偿同步）
   */
  async addSyncJob(jobData: SyncJobData): Promise<void> {
    await this.queue.add('sync-chat-data', jobData, {
      attempts: this.config.queue.retryAttempts,
      backoff: {
        type: 'exponential',
        delay: 2000
      }
    })
  }

  /**
   * 获取队列监控信息
   */
  async getQueueMetrics() {
    const [waiting, active, completed, failed] = await Promise.all([
      this.queue.getWaiting(),
      this.queue.getActive(),
      this.queue.getCompleted(),
      this.queue.getFailed()
    ])

    const postgresStats = await this.postgresService.getSyncStats()

    return {
      queue: {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length
      },
      postgres: postgresStats,
      isHealthy: await this.postgresService.healthCheck()
    }
  }

  /**
   * 清理已完成的任务（定期维护）
   */
  async cleanupCompletedJobs(maxAge: number = 24 * 60 * 60 * 1000): Promise<void> {
    await this.queue.clean(maxAge, 100, 'completed')
    await this.queue.clean(maxAge, 100, 'failed')
    console.log('队列清理完成')
  }
}