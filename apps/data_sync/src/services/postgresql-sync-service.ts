import { Client } from 'pg'
import { TransformedData } from './data-transform-service'
import { SyncConfig, ProjectName } from '../config/sync-config'

export class PostgreSQLSyncService {
  private clients: Map<ProjectName, Client> = new Map()
  private config: SyncConfig

  constructor(config: SyncConfig) {
    this.config = config

    // 为每个项目创建独立的数据库连接
    for (const [projectName, dbName] of Object.entries(config.postgresql.databases)) {
      const connectionString = `${config.postgresql.baseUri}/${dbName}`
      const client = new Client({ connectionString })
      this.clients.set(projectName as ProjectName, client)
    }
  }

  async connect(): Promise<void> {
    for (const [projectName, client] of this.clients) {
      await client.connect()
      console.log(`✅ PostgreSQL连接成功: ${projectName}`)
    }
  }

  async disconnect(): Promise<void> {
    for (const [projectName, client] of this.clients) {
      await client.end()
      console.log(`🔴 PostgreSQL连接已关闭: ${projectName}`)
    }
  }

  /**
   * 为指定项目创建表结构
   */
  async ensureTablesExist(projectName: ProjectName): Promise<void> {
    const client = this.clients.get(projectName)!
    const sql = `
      -- 用户基础信息表
      CREATE TABLE IF NOT EXISTS users (
          id VARCHAR(255) PRIMARY KEY,
          wx_id VARCHAR(255) NOT NULL,
          wx_union_id VARCHAR(255),
          customer_tool_user_id VARCHAR(255),
          conversation_id VARCHAR(255),
          wx_name VARCHAR(255),
          course_no BIGINT,
          created_at TIMESTAMP,
          is_deleted BOOLEAN DEFAULT FALSE,
          is_human_involved BOOLEAN DEFAULT FALSE,
          next_stage VARCHAR(255),
          synced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- 用户状态表（长表设计，支持布尔和数字状态）
      CREATE TABLE IF NOT EXISTS user_states (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(255) REFERENCES users(id) ON DELETE CASCADE,
          state_key VARCHAR(255) NOT NULL,
          state_value BOOLEAN,
          state_value_number INTEGER, -- 新增：支持数字状态如payment_number
          synced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(user_id, state_key)
      );

      -- 用户信息插槽表
      CREATE TABLE IF NOT EXISTS user_slots (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(255) REFERENCES users(id) ON DELETE CASCADE,
          category VARCHAR(255) NOT NULL,
          slot_key VARCHAR(255) NOT NULL,
          content TEXT,
          frequency INTEGER DEFAULT 1,
          synced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(user_id, category, slot_key)
      );

      -- 节点调用统计表
      CREATE TABLE IF NOT EXISTS node_invoke_stats (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(255) REFERENCES users(id) ON DELETE CASCADE,
          node_name VARCHAR(255) NOT NULL,
          invoke_count INTEGER DEFAULT 0,
          synced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(user_id, node_name)
      );

      -- 创建分析用索引
      CREATE INDEX IF NOT EXISTS idx_users_wx_id ON users(wx_id);
      CREATE INDEX IF NOT EXISTS idx_users_course_no ON users(course_no);
      CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
      CREATE INDEX IF NOT EXISTS idx_user_states_user_id ON user_states(user_id);
      CREATE INDEX IF NOT EXISTS idx_user_states_key ON user_states(state_key);
      CREATE INDEX IF NOT EXISTS idx_user_states_value ON user_states(state_value);
      CREATE INDEX IF NOT EXISTS idx_user_slots_user_id ON user_slots(user_id);
      CREATE INDEX IF NOT EXISTS idx_user_slots_category ON user_slots(category);
      CREATE INDEX IF NOT EXISTS idx_node_stats_node ON node_invoke_stats(node_name);
    `

    await client.query(sql)
    console.log(`🏗️ ${projectName} 表结构创建完成`)
  }

  /**
   * 同步用户数据到指定项目数据库
   */
  async syncUserData(data: TransformedData, projectName: ProjectName): Promise<void> {
    const client = this.clients.get(projectName)!

    try {
      await client.query('BEGIN')

      // 1. 同步用户基础信息（UPSERT）
      await client.query(`
        INSERT INTO users (id, wx_id, wx_union_id, customer_tool_user_id, conversation_id, 
                          wx_name, course_no, created_at, is_deleted, is_human_involved, next_stage)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        ON CONFLICT (id) DO UPDATE SET
          wx_name = EXCLUDED.wx_name,
          course_no = EXCLUDED.course_no,
          next_stage = EXCLUDED.next_stage,
          is_human_involved = EXCLUDED.is_human_involved,
          synced_at = CURRENT_TIMESTAMP
      `, [
        data.baseUser.id, data.baseUser.wx_id, data.baseUser.wx_union_id,
        data.baseUser.customer_tool_user_id, data.baseUser.conversation_id,
        data.baseUser.wx_name, data.baseUser.course_no, data.baseUser.created_at,
        data.baseUser.is_deleted, data.baseUser.is_human_involved, data.baseUser.next_stage
      ])

      // 2. 删除该用户的旧关联数据，重新插入（确保数据一致性）
      await client.query('DELETE FROM user_states WHERE user_id = $1', [data.baseUser.id])
      await client.query('DELETE FROM user_slots WHERE user_id = $1', [data.baseUser.id])
      await client.query('DELETE FROM node_invoke_stats WHERE user_id = $1', [data.baseUser.id])

      // 3. 批量插入用户状态
      if (data.userStates.length > 0) {
        const stateValues = data.userStates.map((state, index) =>
          `($${index * 4 + 1}, $${index * 4 + 2}, $${index * 4 + 3}, $${index * 4 + 4})`
        ).join(', ')

        const stateParams = data.userStates.flatMap((state) => [
          state.user_id, state.state_key, state.state_value, state.state_value_number
        ])

        await client.query(`
          INSERT INTO user_states (user_id, state_key, state_value, state_value_number)
          VALUES ${stateValues}
        `, stateParams)
      }

      // 4. 批量插入用户插槽
      if (data.userSlots.length > 0) {
        const slotValues = data.userSlots.map((_, index) =>
          `($${index * 5 + 1}, $${index * 5 + 2}, $${index * 5 + 3}, $${index * 5 + 4}, $${index * 5 + 5})`
        ).join(', ')

        const slotParams = data.userSlots.flatMap((slot) => [
          slot.user_id, slot.category, slot.slot_key, slot.content, slot.frequency
        ])

        await client.query(`
          INSERT INTO user_slots (user_id, category, slot_key, content, frequency)
          VALUES ${slotValues}
        `, slotParams)
      }

      // 5. 批量插入节点统计
      if (data.nodeStats.length > 0) {
        const nodeValues = data.nodeStats.map((_, index) =>
          `($${index * 3 + 1}, $${index * 3 + 2}, $${index * 3 + 3})`
        ).join(', ')

        const nodeParams = data.nodeStats.flatMap((node) => [
          node.user_id, node.node_name, node.invoke_count
        ])

        await client.query(`
          INSERT INTO node_invoke_stats (user_id, node_name, invoke_count)
          VALUES ${nodeValues}
        `, nodeParams)
      }

      await client.query('COMMIT')
      console.log(` ${projectName} 用户 ${data.baseUser.id} 数据同步成功`)

    } catch (error) {
      await client.query('ROLLBACK')
      console.error(`${projectName} 用户 ${data.baseUser.id} 数据同步失败:`, error)
      throw error
    }
  }

  /**
   * 删除用户数据
   */
  async deleteUserData(userId: string, projectName: ProjectName): Promise<void> {
    const client = this.clients.get(projectName)!

    try {
      await client.query('BEGIN')

      // 由于设置了ON DELETE CASCADE，删除users记录会自动删除关联数据
      const result = await client.query('DELETE FROM users WHERE id = $1', [userId])

      await client.query('COMMIT')
      console.log(` ${projectName} 用户 ${userId} 数据删除成功，影响行数: ${result.rowCount}`)

    } catch (error) {
      await client.query('ROLLBACK')
      console.error(` ${projectName} 删除用户 ${userId} 数据失败:`, error)
      throw error
    }
  }

  /**
   * 获取同步统计信息
   */
  async getSyncStats(): Promise<Record<string, any>> {
    const stats: Record<string, any> = {}

    for (const [projectName, client] of this.clients) {
      try {
        const [users, states, slots, nodes] = await Promise.all([
          client.query('SELECT COUNT(*) as count FROM users'),
          client.query('SELECT COUNT(*) as count FROM user_states'),
          client.query('SELECT COUNT(*) as count FROM user_slots'),
          client.query('SELECT COUNT(*) as count FROM node_invoke_stats')
        ])

        stats[projectName] = {
          users: parseInt(users.rows[0].count, 10),
          states: parseInt(states.rows[0].count, 10),
          slots: parseInt(slots.rows[0].count, 10),
          nodes: parseInt(nodes.rows[0].count, 10)
        }
      } catch (error) {
        stats[projectName] = { error: error }
      }
    }

    return stats
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      for (const [projectName, client] of this.clients) {
        await client.query('SELECT 1')
      }
      return true
    } catch (error) {
      console.error('PostgreSQL健康检查失败:', error)
      return false
    }
  }
}