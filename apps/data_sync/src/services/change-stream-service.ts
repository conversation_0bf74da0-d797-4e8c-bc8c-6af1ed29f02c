import { MongoClient, ChangeStream, ChangeStreamDocument } from 'mongodb'
import { Queue } from 'bullmq'
import { RedisDB } from 'model/redis/redis'
import { SyncConfig, ProjectName } from '../config/sync-config'
import { MongoChat, SyncJobData } from '../types/chat'

export class ChangeStreamService {
  private mongoClients: Map<ProjectName, MongoClient> = new Map()
  private changeStreams: Map<ProjectName, ChangeStream> = new Map()
  private syncQueue: Queue
  private config: SyncConfig

  constructor(config: SyncConfig) {
    this.config = config
    this.syncQueue = new Queue(config.queue.name, {
      connection: RedisDB.getInstance()
    })

    // 为每个项目创建Mongo连接
    for (const [projectName, projectConfig] of Object.entries(config.projects)) {
      const client = new MongoClient(projectConfig.fullUri)
      this.mongoClients.set(projectName as ProjectName, client)
    }
  }

  async start(): Promise<void> {
    try {
      console.log('MongoDB连接成功，开始监听Change Streams...')

      // 为每个项目启动Change Stream
      for (const [projectName, projectConfig] of Object.entries(this.config.projects)) {
        const client = this.mongoClients.get(projectName as ProjectName)!
        await client.connect()

        const db = client.db(projectConfig.dbName)
        const collection = db.collection(projectConfig.collectionName)

        // 创建Change Stream，只监听非删除的记录
        const changeStream = collection.watch([
          {
            $match: {
              $and: [
                { 'fullDocument.is_deleted': { $ne: true } },
                {
                  $or: [
                    { operationType: 'insert' },
                    { operationType: 'update' },
                    { operationType: 'replace' }
                  ]
                }
              ]
            }
          }
        ], {
          fullDocument: 'updateLookup' // 获取完整文档
        })

        changeStream.on('change', (change) => this.handleChange(change, projectName as ProjectName))
        changeStream.on('error', (error) => this.handleError(error, projectName as ProjectName))

        this.changeStreams.set(projectName as ProjectName, changeStream)
        console.log(` Change Stream已启动: ${projectName}.${projectConfig.collectionName}`)
      }

      console.log(' 所有Change Stream服务启动完成')
    } catch (error) {
      console.error('Change Stream服务启动失败:', error)
      throw error
    }
  }

  private async handleChange(change: ChangeStreamDocument, projectName: ProjectName): Promise<void> {
    try {
      // 类型守护：只处理有documentKey的事件
      if (!('documentKey' in change) || !change.documentKey) {
        console.log(`跳过${projectName}事件: ${change.operationType} (无documentKey)`)
        return
      }

      // 类型守护：delete事件没有fullDocument
      if (change.operationType === 'delete') {
        const syncData: SyncJobData = {
          operationType: 'delete',
          documentId: change.documentKey._id.toString(),
          projectName,
          timestamp: new Date()
        }

        await this.syncQueue.add('sync-chat-data', syncData, {
          attempts: this.config.queue.retryAttempts,
          backoff: { type: 'exponential', delay: 2000 }
        })

        console.log(`${projectName}: delete for document ${syncData.documentId}`)
        return
      }

      // 处理insert/update/replace事件（有fullDocument）
      if (!('fullDocument' in change) || !change.fullDocument) {
        console.log(`跳过${projectName}事件: ${change.operationType} (无fullDocument)`)
        return
      }

      const syncData: SyncJobData = {
        operationType: change.operationType as any,
        documentId: change.documentKey._id.toString(),
        fullDocument: change.fullDocument as MongoChat,
        projectName,
        timestamp: new Date()
      }

      await this.syncQueue.add('sync-chat-data', syncData, {
        attempts: this.config.queue.retryAttempts,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      })

      console.log(`${projectName}: ${change.operationType} for document ${syncData.documentId}`)
    } catch (error) {
      console.error(`处理${projectName} Change Stream事件失败:`, error)
    }
  }

  private handleError(error: Error, projectName: ProjectName): void {
    console.error(`${projectName} Change Stream错误:`, error)
    // 实现重连逻辑
    setTimeout(() => {
      console.log(`尝试重新启动${projectName} Change Stream...`)
      this.restartProjectStream(projectName).catch(console.error)
    }, 5000)
  }

  // 重启单个项目的Change Stream
  private async restartProjectStream(projectName: ProjectName): Promise<void> {
    try {
      const client = this.mongoClients.get(projectName)!
      const projectConfig = this.config.projects[projectName]

      // 关闭旧的Change Stream
      const oldStream = this.changeStreams.get(projectName)
      if (oldStream) {
        await oldStream.close()
      }

      const db = client.db(projectConfig.dbName)
      const collection = db.collection(projectConfig.collectionName)

      const changeStream = collection.watch([{
        $match: {
          $and: [
            { 'fullDocument.is_deleted': { $ne: true } },
            {
              $or: [
                { operationType: 'insert' },
                { operationType: 'update' },
                { operationType: 'replace' }
              ]
            }
          ]
        }
      }], { fullDocument: 'updateLookup' })

      changeStream.on('change', (change) => this.handleChange(change, projectName))
      changeStream.on('error', (error) => this.handleError(error, projectName))

      this.changeStreams.set(projectName, changeStream)
      console.log(`${projectName} Change Stream重启成功`)
    } catch (error) {
      console.error(`重启${projectName} Change Stream失败:`, error)
    }
  }

  async stop(): Promise<void> {
    try {
      // 停止所有Change Stream
      for (const [projectName, changeStream] of this.changeStreams) {
        await changeStream.close()
        console.log(`${projectName} Change Stream已停止`)
      }

      // 关闭所有MongoDB连接
      for (const [projectName, client] of this.mongoClients) {
        await client.close()
      }

      await this.syncQueue.close()
      console.log(' 所有Change Stream服务已停止')
    } catch (error) {
      console.error('停止Change Stream服务失败:', error)
    }
  }

  // 获取队列状态用于监控
  async getQueueStats() {
    return {
      waiting: await this.syncQueue.getWaiting(),
      active: await this.syncQueue.getActive(),
      completed: await this.syncQueue.getCompleted(),
      failed: await this.syncQueue.getFailed()
    }
  }
}