export interface MongoChat {
  _id: string
  wx_id: string
  wx_union_id?: string
  customer_tool_user_id?: string
  conversation_id?: string
  contact: {
    wx_id: string
    wx_name: string
  }
  chat_state: {
    nodeInvokeCount?: Record<string, number>
    nextStage?: string
    userSlots?: Record<string, {
      content: string
      frequency: number
    }>
    state?: Record<string, boolean>
  }
  course_no?: number
  created_at?: Date
  is_deleted?: boolean
  is_human_involved?: boolean
}

export interface SyncJobData {
  operationType: 'insert' | 'update' | 'delete' | 'replace'
  documentId: string
  fullDocument?: MongoChat
  projectName: string // 添加项目名标识
  timestamp: Date
}

export enum SyncStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}