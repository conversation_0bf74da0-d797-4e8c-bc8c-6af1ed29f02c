import { MongoClient } from 'mongodb'
import { DataTransformService } from '../src/services/data-transform-service'
import { PostgreSQLSyncService } from '../src/services/postgresql-sync-service'
import { createSyncConfig } from '../src/config/sync-config'
import { MongoChat } from '../src/types/chat'

async function fullSyncHaogu() {
  console.log('🚀 开始haogu全量数据同步...')

  // 使用data_sync服务的配置
  const config = createSyncConfig('test')
  const transformService = new DataTransformService()
  const postgresService = new PostgreSQLSyncService(config)

  // MongoDB连接
  const mongoUri = config.projects.haogu.fullUri
  const mongoClient = new MongoClient(mongoUri)

  try {
    console.log('📡 连接数据库...')
    await mongoClient.connect()
    await postgresService.connect()

    // 确保表结构存在
    await postgresService.ensureTablesExist('haogu')

    console.log('✅ 数据库连接成功！')

    // 获取haogu所有用户数据
    const collection = mongoClient.db().collection('chat')
    const totalCount = await collection.countDocuments({ is_deleted: { $ne: true } })
    console.log(`📊 找到 ${totalCount} 条haogu用户数据`)

    // 清空现有数据（全量重新导入）
    console.log('🧹 清空现有数据...')
    const pgClient = (postgresService as any).clients.get('haogu')
    await pgClient.query('TRUNCATE users, user_states, user_slots, node_invoke_stats CASCADE')

    // 分批处理数据
    const batchSize = 100
    let processedCount = 0
    let successCount = 0
    let errorCount = 0

    const cursor = collection.find({ is_deleted: { $ne: true } }).batchSize(batchSize)

    console.log('🔄 开始数据转换和同步...')

    while (await cursor.hasNext()) {
      const chat = await cursor.next() as unknown as MongoChat

      try {
        // 使用data_sync服务的数据转换逻辑
        const transformedData = transformService.transformChatDocument(chat, 'haogu')

        // 验证转换后的数据
        if (!transformService.validateTransformedData(transformedData, 'haogu')) {
          console.error(`❌ 数据验证失败: ${chat._id}`)
          errorCount++
          continue
        }

        // 同步到PostgreSQL
        await postgresService.syncUserData(transformedData, 'haogu')
        successCount++

      } catch (error) {
        console.error(`❌ 同步用户 ${chat._id} 失败:`, error)
        errorCount++
      }

      processedCount++

      // 每处理100条输出进度
      if (processedCount % 100 === 0) {
        console.log(`📈 进度: ${processedCount}/${totalCount} (成功: ${successCount}, 失败: ${errorCount})`)
      }
    }

    // 输出最终统计
    console.log('\n========== 全量同步完成！==========')
    console.log(`📊 处理总数: ${processedCount}`)
    console.log(`✅ 成功同步: ${successCount}`)
    console.log(`❌ 失败数量: ${errorCount}`)

    // 获取数据库统计
    const stats = await postgresService.getSyncStats()
    console.log('\n📈 数据库统计:')
    console.log(JSON.stringify(stats.haogu, null, 2))

    // 验证数据完整性
    await validateDataIntegrity(pgClient)

    console.log('\n🎉 haogu全量数据同步成功完成！')

  } catch (error) {
    console.error('❌ 全量同步失败:', error)
    throw error
  } finally {
    await mongoClient.close()
    await postgresService.disconnect()
  }
}

async function validateDataIntegrity(pgClient: any) {
  console.log('\n🔍 验证数据完整性...')

  const results = await Promise.all([
    pgClient.query('SELECT COUNT(*) as count FROM users'),
    pgClient.query('SELECT COUNT(*) as count FROM user_states'),
    pgClient.query('SELECT COUNT(*) as count FROM user_slots'),
    pgClient.query('SELECT COUNT(*) as count FROM node_invoke_stats'),
    pgClient.query(`
      SELECT 
        COUNT(*) FILTER (WHERE state_value IS NOT NULL) as boolean_states,
        COUNT(*) FILTER (WHERE state_value_number IS NOT NULL) as number_states
      FROM user_states
    `),
    pgClient.query(`
      SELECT state_key, COUNT(*) as count 
      FROM user_states 
      WHERE state_value_number IS NOT NULL
      GROUP BY state_key 
      ORDER BY count DESC 
      LIMIT 5
    `)
  ])

  console.log(`👤 用户数: ${results[0].rows[0].count}`)
  console.log(`📊 状态记录: ${results[1].rows[0].count}`)
  console.log(`📝 信息插槽: ${results[2].rows[0].count}`)
  console.log(`🔗 节点统计: ${results[3].rows[0].count}`)
  console.log(`🔢 布尔状态: ${results[4].rows[0].boolean_states}`)
  console.log(`🔢 数字状态: ${results[4].rows[0].number_states}`)

  if (results[5].rows.length > 0) {
    console.log('\n数字状态字段 Top 5:')
    results[5].rows.forEach((row: any) => {
      console.log(`  ${row.state_key}: ${row.count} 条记录`)
    })
  }
}

// 运行全量同步
if (require.main === module) {
  fullSyncHaogu().catch((error) => {
    console.error('全量同步失败:', error)
    process.exit(1)
  })
}