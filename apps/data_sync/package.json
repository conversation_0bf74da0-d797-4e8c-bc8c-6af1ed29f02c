{"name": "@eliza/data-sync", "version": "1.0.0", "description": "MongoDB到PostgreSQL的近实时数据同步服务", "main": "src/main.ts", "scripts": {"start": "ts-node src/main.ts", "start:prod": "ts-node src/main.ts --env=prod", "dev": "NODE_ENV=dev ts-node src/main.ts", "test": "jest", "build": "tsc"}, "dependencies": {"bullmq": "^5.12.9", "ioredis": "^5.4.1", "mongodb": "^6.19.0", "pg": "^8.16.3", "@types/pg": "^8.15.5", "config": "workspace:*", "lib": "workspace:*", "model": "workspace:*", "service": "workspace:*"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.19.8", "jest": "^29.7.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "5.8.2"}}