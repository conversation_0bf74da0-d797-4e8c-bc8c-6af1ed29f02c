import axios from 'axios'
import { Retry } from 'lib/retry/retry'
import logger from 'model/logger/logger'
import { ClientAccountConfig } from 'service/database/config'


export class EventDispatcher {

  private static async getServerAddress(wechatId: string) {
    const serverAddress = await ClientAccountConfig.getServerAddressByWechatId(wechatId)
    if (!serverAddress) {
      throw new Error('没有找到对应的服务器地址')
    }

    return serverAddress
  }

  private static async dispatchEventToServer(serverAddress: string, path:string, event: any) {
    logger.log(JSON.stringify(event, null, 4))
    if (!serverAddress) {
      console.error('没有找到对应的服务器地址')
      return
    }

    try {
      await Retry.retry(4, async () => {
        await axios.post(`${serverAddress}${path}`, event, { insecureHTTPParser: true })
      }, {
        delayFunc :(retryCount) => {
          if (retryCount === 1) return 1 * 60 * 1000  // 1分钟
          if (retryCount === 2) return 2 * 60 * 1000 // 2分钟
          if (retryCount === 3) return 3 * 60 * 1000 // 3分钟
          return 0  // 之后不再进行重试
        }
      })
    } catch (e) {
      console.error('事件分发失败：', serverAddress, e)
    }
  }

  static async dispatch(botId:string, path:string, data:any) {
    try {
      const serverAddress = await this.getServerAddress(botId)

      await this.dispatchEventToServer(serverAddress, path, data)
    } catch (e) {
      logger.warn(`没有找到对应的地址, bot:${botId}`)
    }
  }
}