import * as fs from 'fs'
import { PrismaMongoClient } from '../packages/model/mongodb/prisma'

describe('拉取用户数据', () => {
  jest.setTimeout(60000000)

  test('拉取指定wx_id和course_no范围的用户期数和手机号', async() => {
    const targetWxId = '1688855612600530'
    const startCourseNo = 20250615
    const endCourseNo = 20250615

    const mongoClient = PrismaMongoClient.getInstance()

    // 查询指定wx_id和course_no范围的用户
    const users = await mongoClient.chat.findMany({
      where: {
        wx_id: targetWxId,
        course_no: {
          gte: startCourseNo,
          lte: endCourseNo
        }
      }
    })

    if (users.length === 0) {
      console.log('未找到符合条件的用户数据')
      return
    }

    // 写入CSV
    const headers = ['微信ID', '微信昵称', '期数(course_no)', '手机号', '创建时间', '聊天ID']

    const rows = users.map((user) => [
      user.contact.wx_id,
      user.contact.wx_name,
      user.course_no,
      user.phone || '未设置',
      // @ts-ignore fk u
      user.created_at.toLocaleString(),
    ])

    // 拼接内容
    const csvContent = [
      headers.join(','),
      ...rows.map((row) => row.join(',')),
    ].join('\n')

    fs.writeFileSync('output.csv', csvContent, 'utf8')
    console.log('已写入 output.csv')
  })
})