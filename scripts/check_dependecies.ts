#!/usr/bin/env ts-node

import * as fs from 'fs'
import * as path from 'path'
import { execSync } from 'child_process'
import * as nodeModule from 'module'
import chalk from 'chalk'

// --- Configuration ---
const ROOT_DIR = path.resolve(__dirname, '..')
const PACKAGES_DIR = path.join(ROOT_DIR, 'packages')
const APPS_DIR = path.join(ROOT_DIR, 'apps')

// Define layers and their paths
const LAYERS: Record<string, string> = {
  LIB: path.join(PACKAGES_DIR, 'lib'),
  MODEL: path.join(PACKAGES_DIR, 'model'),
  SERVICE: path.join(PACKAGES_DIR, 'service'),
  APP: APPS_DIR,
  CONFIG: path.join(PACKAGES_DIR, 'config'),
  ADMIN_PLATFORM: path.join(ROOT_DIR, 'admin_platform'),
  // Add other specific packages if they have unique rules
}

type Layer = keyof typeof LAYERS | 'UNKNOWN_INTERNAL' | 'EXTERNAL' | 'UNKNOWN';

// --- Dependency Rules ---
// Key: Layer importing, Value: Array of allowed layers to import from
const ALLOWED_DEPENDENCIES: Record<Layer, Layer[]> = {
  LIB: ['LIB', 'EXTERNAL', 'CONFIG'], // Lib can only import from lib or external
  MODEL: ['LIB', 'MODEL', 'CONFIG', 'EXTERNAL'], // Model can import from lib, model, config, external
  SERVICE: ['LIB', 'MODEL', 'SERVICE', 'CONFIG', 'EXTERNAL'], // Service can import from lib, model, service, config, external
  CONFIG: ['CONFIG', 'EXTERNAL'], // Config should ideally only import itself or external
  APP: ['LIB', 'MODEL', 'SERVICE', 'CONFIG', 'APP', 'EXTERNAL'], // Apps can import from packages, other apps, and external
  ADMIN_PLATFORM: ['LIB', 'MODEL', 'SERVICE', 'CONFIG', 'APP', 'ADMIN_PLATFORM', 'EXTERNAL'], // Admin platform can import from all layers
  UNKNOWN_INTERNAL: [], // Should not import anything by default, might need adjustment
  EXTERNAL: [], // Not applicable as importer
  UNKNOWN: [], // Not applicable as importer
}

// Regex to find import/require statements (simplified for performance)
// Captures the module path in group 1
const IMPORT_REGEX = /(?:import(?:["'\s]*(?:[\w*{}\n\r\t, ]+)from\s*)?|require\s*\(\s*|import\s*\(\s*)["']([^"']+)["']/g
const builtInModules = new Set(nodeModule.builtinModules)

// --- Helper Functions ---

/**
 * Get the layer of a given file path.
 */
function getFileLayer(filePath: string): Layer {
  if (!filePath.startsWith(ROOT_DIR) || filePath.includes(path.join('node_modules'))) {
    return 'UNKNOWN' // External or unexpected path outside project structure (excluding node_modules handled later)
  }

  const relativePath = path.relative(ROOT_DIR, filePath)

  if (relativePath.startsWith(path.relative(ROOT_DIR, LAYERS.LIB))) return 'LIB'
  if (relativePath.startsWith(path.relative(ROOT_DIR, LAYERS.MODEL))) return 'MODEL'
  if (relativePath.startsWith(path.relative(ROOT_DIR, LAYERS.SERVICE))) return 'SERVICE'
  if (relativePath.startsWith(path.relative(ROOT_DIR, LAYERS.CONFIG))) return 'CONFIG'
  if (relativePath.startsWith(path.relative(ROOT_DIR, LAYERS.APP))) return 'APP'
  if (relativePath.startsWith('admin_platform')) return 'ADMIN_PLATFORM'

  // Could be another package not explicitly defined, treat cautiously
  if (relativePath.startsWith('packages')) {
    // console.warn(`Uncategorized package file treated as SERVICE layer: ${relativePath}`);
    return 'SERVICE' // Default assumption for other packages
  }

  // Files directly under ROOT_DIR or in other top-level folders
  // console.warn(`File in unrecognized internal location: ${relativePath}`);
  return 'UNKNOWN_INTERNAL'
}

/**
 * Checks if an import path refers to an external module (node_modules or built-in).
 */
function isExternalImport(importPath: string): boolean {
  // Doesn't start with '.' or '/' (or is an absolute path outside project root)
  // Or is a known node built-in
  const isRelativeOrAbsolute = importPath.startsWith('.') || path.isAbsolute(importPath)
  return (!isRelativeOrAbsolute && !importPath.startsWith('@/')) || builtInModules.has(importPath) // Adjust alias check ('@/') if needed
}

/**
 * Validates dependencies for a single file.
 */
function validateFileDependencies(filePath: string): string[] {
  const errors: string[] = []
  const fileContent = fs.readFileSync(filePath, 'utf8')
  const fileDir = path.dirname(filePath)
  const importerLayer = getFileLayer(filePath)

  if (importerLayer === 'UNKNOWN' || importerLayer === 'EXTERNAL') {
    // console.warn(`Skipping validation for unknown or external file: ${filePath}`);
    return errors
  }

  const allowedImports = ALLOWED_DEPENDENCIES[importerLayer] ?? []

  let match: RegExpExecArray | null
  while ((match = IMPORT_REGEX.exec(fileContent)) !== null) {
    const importPath = match[1]

    if (isExternalImport(importPath)) {
      continue // Skip node_modules and built-ins
    }

    let resolvedPath: string | undefined
    try {
      // Attempt to resolve the path.
      resolvedPath = require.resolve(importPath, { paths: [fileDir, ROOT_DIR] })

      // Check if resolved path is actually in node_modules (can happen with symlinks or complex setups)
      if (resolvedPath.includes(path.join('node_modules'))) {
        continue
      }

    } catch (e) {
      // Ignore resolution errors (e.g., type-only imports, aliases not configured for require.resolve)
      // console.warn(`Could not resolve import "${importPath}" from "${path.relative(ROOT_DIR, filePath)}". Skipping check.`);
      continue
    }

    // Don't check imports within the *exact same* directory (often utilities within the same feature)
    // Adjust this if finer-grained control is needed.
    if (path.dirname(resolvedPath) === path.dirname(filePath)) {
      continue
    }

    const importedLayer = getFileLayer(resolvedPath)

    // ** THE CORE RULE CHECKS **
    if (importPath === '../../../../apps/registry/registry') {
      continue
    }

    if (filePath.includes('admin_platform') || filePath.includes('scripts')) { // 后台不做校验
      continue
    }

    // 1. packages/* cannot import apps/* (unless the importer is also APP or ADMIN_PLATFORM)
    if (importerLayer !== 'APP' && importerLayer !== 'ADMIN_PLATFORM' && importedLayer === 'APP') {
      errors.push(chalk.red(`层级违规：违规导入：${chalk.magenta(importPath)}。 ${importerLayer} （${path.relative(ROOT_DIR, filePath)}）不能 导入 APP 层（${path.relative(ROOT_DIR, resolvedPath)}）。`))
      continue
    }

    // 2. 根据 ALLOWED_DEPENDENCIES 检查层级特定规则
    if (importedLayer !== 'UNKNOWN' && importedLayer !== 'EXTERNAL' && !allowedImports.includes(importedLayer)) {
      errors.push(chalk.red(`层级违规：违规导入：${chalk.magenta(importPath)}。${importerLayer} 文件（${path.relative(ROOT_DIR, filePath)}）不能从 ${importedLayer} 层（${path.relative(ROOT_DIR, resolvedPath)}）导入。允许导入：[${allowedImports.filter((l) => l !== 'EXTERNAL').join(', ')}]。`))
    } else if (importedLayer === 'UNKNOWN' || importedLayer === 'UNKNOWN_INTERNAL') {
      // 如有需要，标记从未知内部位置的导入
      // console.warn(chalk.yellow(`⚠️ 警告：${importerLayer} 文件（${path.relative(ROOT_DIR, filePath)}）从未识别的内部位置导入：${path.relative(ROOT_DIR, resolvedPath)}`));
    }
  }

  return errors
}

// --- Main Execution ---

async function main() {
  console.log(chalk.blue('🔍 开始检查依赖...'))

  try {
    // Get staged .ts/.tsx files
    const output = execSync('git diff --cached --name-only --diff-filter=ACM', { encoding: 'utf8' })
    const stagedFiles = output
      .split('\n')
      .map((file) => file.trim())
      .filter((file) => file && /\.(ts|tsx)$/.test(file) && !/\.test\.(ts|tsx)$/.test(file)) // Filter for TS/TSX, exclude tests
      .map((file) => path.join(ROOT_DIR, file)) // Get absolute paths
      .filter((file) => {
        try {
          return fs.existsSync(file) && fs.statSync(file).isFile()
        } catch {
          return false // Handle potential errors if file disappears between commands
        }
      })

    if (stagedFiles.length === 0) {
      console.log('No relevant TypeScript files staged. Skipping.')
      process.exit(0)
    }

    let totalErrors: string[] = []
    const startTime = Date.now()

    // Process files sequentially for simplicity
    for (const file of stagedFiles) {
      const fileErrors = validateFileDependencies(file)
      totalErrors = totalErrors.concat(fileErrors)
    }

    const endTime = Date.now()
    console.log(`Validation finished in ${endTime - startTime}ms.`)

    if (totalErrors.length > 0) {
      console.error('\n--- ⚠️ 依赖校验失败：---')
      totalErrors.forEach((error) => console.error(`❌ ${error}`))
      console.error('-----------------------------------')
      console.error('提交失败，大侠请重新来过！')
      process.exit(1) // Abort commit
    } else {
      console.log('✅ 依赖检查通过')
      process.exit(0) // Allow commit
    }
  } catch (error) {
    console.error('Error running pre-commit hook:')
    console.error(error instanceof Error ? error.message : error)
    process.exit(1) // Abort commit on script error
  }
}

main()