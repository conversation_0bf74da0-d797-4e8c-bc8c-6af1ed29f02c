<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="get_data" type="JavaScriptTestRunnerJest" nameIsGenerated="true">
    <node-interpreter value="project" />
    <jest-package value="$PROJECT_DIR$/apps/moer_overseas/node_modules/jest" />
    <working-dir value="$PROJECT_DIR$/apps/moer_overseas" />
    <envs />
    <scope-kind value="SUITE" />
    <test-file value="$PROJECT_DIR$/apps/moer_overseas/helper/getter/test/get_data.test.ts" />
    <test-names>
      <test-name value="get_data" />
    </test-names>
    <method v="2" />
  </configuration>
</component>