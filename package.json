{"name": "eliza", "private": true, "scripts": {"tsc-check": "pnpm dlx turbo tsc-check", "check-import": "ts-node scripts/check_dependecies.ts", "circular-check": "madge --circular --extensions ts apps packages", "prisma_generate": "pnpm dlx turbo prisma_generate", "yuhe:test": "export NODE_ENV=dev WECHAT_NAME=yuhe_test && ts-node apps/yuhe/client/client_server.ts", "yuhe:client": "ts-node apps/yuhe/client/client_server.ts", "yuhe:event:server": "ts-node apps/yuhe/server/event_server.ts", "yuhe:deploy": "ts-node apps/yuhe/docker/deploy.ts", "moer_overseas:client": "ts-node apps/moer_overseas/client/client_server.ts", "deploy": "ts-node deploy.ts", "deploy:yuhe": "ts-node deploy.ts --project yuhe", "deploy:moer_overseas": "ts-node deploy.ts --project moer_overseas", "release": "ts-node release-interactive.ts", "docker:build": "docker buildx build --platform linux/amd64 -f Dockerfile .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "client": "ts-node apps/yuhe/client/client_server.ts"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/node": "^20.19.8", "eslint": "^9.31.0", "globals": "^16.3.0", "husky": "^7.0.4", "jest": "^29.7.0", "madge": "^8.0.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "turbo": "^2.5.4", "typescript": "5.8.2", "typescript-eslint": "^8.37.0"}, "dependencies": {"@types/inquirer": "^9.0.8", "@types/js-yaml": "^4.0.9", "axios": "^1.10.0", "chalk": "^4.1.2", "inquirer": "^8.2.6", "js-yaml": "^4.1.0"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=18"}}