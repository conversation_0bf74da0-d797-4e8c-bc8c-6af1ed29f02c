#!/bin/bash

# 远程服务器部署脚本
# 用于在服务器上执行实际的容器部署操作

set -e

# 获取参数
PROJECT=${1:-"yuhe"}
SERVICES=${2:-""}
VERSION=${3:-"latest"}

echo "===== 远程部署脚本开始 ====="
echo "项目: $PROJECT"
echo "服务: $SERVICES"
echo "版本: $VERSION"

# 根据项目选择对应的 compose 文件
case $PROJECT in
  "yuhe")
    COMPOSE_FILE="docker-compose.yuhe.yml"
    REGISTRY_URL="crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/yuhe"
    ;;
  "moer_overseas")
    COMPOSE_FILE="docker-compose.moer_overseas.yml"
    REGISTRY_URL="crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/moer_overseas"
    ;;
  *)
    echo "错误: 未知项目 $PROJECT"
    exit 1
    ;;
esac

echo "使用 Compose 文件: $COMPOSE_FILE"
echo "镜像仓库: $REGISTRY_URL"

# 检查 compose 文件是否存在
if [ ! -f "$COMPOSE_FILE" ]; then
    echo "错误: Compose 文件 $COMPOSE_FILE 不存在"
    exit 1
fi

# 拉取最新镜像
echo "拉取最新镜像..."
docker pull $REGISTRY_URL:$VERSION

# 停止并移除旧容器（如果指定了服务）
if [ -n "$SERVICES" ]; then
    echo "停止指定服务: $SERVICES"
    docker-compose -f $COMPOSE_FILE stop $SERVICES || echo "服务可能未运行"
    docker-compose -f $COMPOSE_FILE rm -f $SERVICES || echo "容器可能不存在"

    # 启动指定服务
    echo "启动服务: $SERVICES"
    docker-compose -f $COMPOSE_FILE up -d $SERVICES
else
    # 重新部署所有服务
    echo "重新部署所有服务..."
    docker-compose -f $COMPOSE_FILE down
    docker-compose -f $COMPOSE_FILE up -d
fi

# 清理未使用的镜像
echo "清理未使用的镜像..."
docker image prune -f

# 显示运行状态
echo "===== 部署完成，当前运行状态 ====="
docker-compose -f $COMPOSE_FILE ps

echo "===== 远程部署脚本完成 ====="
