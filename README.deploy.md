# Eliza Sales Monorepo 部署指南

## 概述

本项目已迁移到 monorepo 架构，支持 `yuhe` 和 `moer_overseas` 两个项目的独立部署。部署系统基于 Docker + docker-compose，支持多环境配置和选择性服务部署。

## 项目结构

```
eliza-sales/
├── apps/
│   ├── yuhe/                    # YUHE 项目
│   │   └── client/
│   │       └── client_server.ts
│   └── moer_overseas/           # MOER_OVERSEAS 项目
│       └── client/
│           └── client_server.ts
├── packages/                    # 共享包
├── Dockerfile.template          # 简化的 Dockerfile 模板
├── docker-compose.yuhe.yml      # YUHE 项目 compose 配置
├── docker-compose.moer_overseas.yml # MOER_OVERSEAS 项目 compose 配置
├── deploy.ts                    # 主部署脚本
├── deploy_remote.sh             # 远程服务器部署脚本
└── release.sh                   # 发布脚本
```

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pnpm install
```

### 2. 本地开发

```bash
# 启动 YUHE 项目
npm run yuhe:client

# 启动 MOER_OVERSEAS 项目
npm run moer_overseas:client
```

### 3. 部署

#### 快速部署（推荐）

```bash
# 快速部署 YUHE 项目（自动构建+部署所有服务）
npm run quick:yuhe

# 快速部署 MOER_OVERSEAS 项目
npm run quick:moer_overseas
```

#### 交互式部署

```bash
# 选择项目和服务进行部署
npm run deploy

# 直接进入指定项目的部署流程
npm run deploy:yuhe
npm run deploy:moer_overseas
```

#### 完整发布流程

```bash
# 交互式发布流程（推荐）- 支持上下箭头选择
npm run release

# 传统发布流程 - 数字选择
./release.sh
```

## 部署流程详解

### 1. 发布脚本 (release.sh)

发布脚本会执行以下操作：

1. **Git 操作**：
   - 检查未提交更改
   - 切换到 develop 分支并拉取最新代码
   - 切换到 main 分支并拉取最新代码
   - 合并 develop 到 main
   - 推送到远程仓库

2. **项目选择**：
   - 交互式选择要部署的项目
   - 支持单项目或全部项目部署

3. **执行部署**：
   - 调用对应的部署脚本

### 2. 部署脚本 (deploy.ts)

部署脚本提供以下功能：

1. **项目选择**：支持 yuhe 和 moer_overseas 项目
2. **环境选择**：开发、预发布、生产环境
3. **版本管理**：自动生成或手动输入版本号
4. **服务选择**：从 docker-compose 文件中选择要部署的服务
5. **镜像构建**：基于 Turborepo 的多阶段构建
6. **远程部署**：SSH 到服务器执行部署

### 3. Docker 构建

使用简化的 `Dockerfile.template`：

- 基于 `node:18-alpine3.20`
- 使用 `pnpm@8.6.6` 包管理器
- 自动生成 Prisma 客户端
- 启动命令：`pnpm run client`

## 配置说明

### Docker Compose 配置

每个项目都有独立的 compose 文件：

- `docker-compose.yuhe.yml`: YUHE 项目服务配置
- `docker-compose.moer_overseas.yml`: MOER_OVERSEAS 项目服务配置

配置特点：
- 简化配置，无需额外的 networks 和 volumes
- 直接使用镜像仓库地址
- 环境变量直接在 compose 文件中配置

### 服务端口分配

- **YUHE 项目**: 5001-5099
- **MOER_OVERSEAS 项目**: 6001-6099

## 命令参考

### 开发命令

```bash
npm run yuhe:client              # 启动 YUHE 客户端
npm run moer_overseas:client     # 启动 MOER_OVERSEAS 客户端
npm run yuhe:test               # YUHE 测试环境
npm run yuhe:event:server       # YUHE 事件服务器
```

### 部署命令

```bash
npm run deploy                  # 交互式部署
npm run deploy:yuhe            # 部署 YUHE 项目
npm run deploy:moer_overseas   # 部署 MOER_OVERSEAS 项目
```

### Docker 命令

```bash
npm run docker:build          # 构建镜像
npm run docker:up             # 启动容器
npm run docker:down           # 停止容器
npm run docker:logs           # 查看日志
```

### 工具命令

```bash
npm run tsc-check             # TypeScript 类型检查
npm run circular-check        # 循环依赖检查
npm run check-import          # 依赖检查
```

## 故障排除

### 常见问题

1. **Docker 构建失败**
   ```bash
   # 清理 Docker 缓存
   docker system prune -a
   
   # 重新构建
   npm run docker:build
   ```

2. **远程部署失败**
   ```bash
   # 检查 SSH 连接
   ssh root@your-server-ip
   
   # 检查服务器上的项目路径
   ls -la /root/eliza-sales
   ```

### 日志查看

部署过程中的日志会保存在 `deploy.log` 文件中，可以通过以下命令查看：

```bash
tail -f deploy.log
```

## 最佳实践

1. **部署前检查**：
   - 确保所有更改已提交
   - 运行测试确保代码质量
   - 检查环境配置

2. **版本管理**：
   - 使用语义化版本号
   - 为重要版本打 Git 标签

3. **监控**：
   - 部署后检查服务状态
   - 监控应用日志
   - 验证功能正常

4. **回滚策略**：
   - 保留前一版本镜像
   - 准备快速回滚方案

## 支持

如有问题，请查看：
1. 部署日志 (`deploy.log`)
2. Docker 容器日志
3. 应用程序日志

或联系开发团队获取支持。
