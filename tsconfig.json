{
  "include": [
    "packages/**/*",
    "apps/**/*",
  ],
  "exclude": [
    "dist",
    "**/*.json",
    "**/*.md",
    "**/*.mdx",
    "**/*.html",
    "**/*.css",
    "**/*.scss",
  ],
  "compileOnSave": true,
  "compilerOptions": {
    "noEmit": true,
    "allowJs": true,
    "strictPropertyInitialization": false,
    "outDir": "./dist/",
    "sourceMap": true,
    "pretty": true,
    "skipLibCheck": true,
    "noImplicitAny": false,
    "module": "commonjs",
    "target": "esnext",
    "strict": true,
    "moduleResolution": "node",
    "lib": [
      "ES2021",
      "es2020",
      "dom",
      "es5",
      "es6"
    ],
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
  }
}
