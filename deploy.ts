#!/usr/bin/env ts-node
/**
 * Monorepo 部署脚本 - 支持多项目选择性部署
 *
 * 功能特性：
 * - 支持 yuhe 和 moer_overseas 项目独立部署
 * - 基于 Turborepo 的 monorepo 架构
 * - 多阶段 Docker 构建优化
 * - 环境配置管理
 * - 交互式项目和服务选择
 */

import * as fs from 'fs'
import { execSync } from 'child_process'
import inquirer from 'inquirer'
import chalk from 'chalk'
import yaml from 'js-yaml'

// ===== 配置常量 =====
const PROJECTS = {
  yuhe: {
    name: 'yuhe',
    displayName: 'YUHE',
    registry: 'crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/yuhe',
    composeFile: 'apps/yuhe/docker/docker-compose.yaml',
    clientCommand: 'ts-node apps/yuhe/client/client_server.ts'
  },
  moer_overseas: {
    name: 'moer_overseas',
    displayName: 'MOER_OVERSEAS',
    registry: 'crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/moer_overseas',
    composeFile: 'apps/moer_overseas/docker/docker-compose.yaml',
    clientCommand: 'ts-node apps/moer_overseas/client/client_server.ts'
  }
}

const REMOTE_CONFIG = {
  user: 'root',
  host: '***************',
  projectPath: '/root/sales',
  deployScript: './deploy_remote.sh'
}

const LOG_FILE = './deploy.log'

// ===== 工具函数 =====

/**
 * 获取默认版本号：格式为 YYYY-MM-DD.HH-MM-SS
 */
function getDefaultVersion(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day}.${hours}-${minutes}-${seconds}`
}

/**
 * 日志函数
 */
function log(message: string, color: keyof typeof chalk = 'white') {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19)
  // @ts-ignore fku
  const coloredMessage = chalk[color] ? chalk[color](`[${timestamp}] ${message}`) : `[${timestamp}] ${message}`
  const plainMessage = `[${timestamp}] ${message}`

  try {
    fs.appendFileSync(LOG_FILE, `${plainMessage}\n`)
  } catch (err) {
    console.error(`写入日志文件失败: ${err}`)
  }
  console.log(coloredMessage)
}

/**
 * 执行命令
 */
function runCommand(command: string, options: { cwd?: string } = {}) {
  log(`执行命令: ${command}`, 'gray')
  try {
    execSync(command, {
      stdio: 'inherit',
      cwd: options.cwd || process.cwd()
    })
  } catch (error) {
    log(`命令执行失败: ${command}`, 'red')
    // @ts-ignore fku
    log(`错误详情: ${error.message || error}`, 'red')
    process.exit(1)
  }
}

/**
 * 从 docker-compose 文件读取服务列表
 */
function getServicesFromCompose(composeFile: string): string[] {
  try {
    if (!fs.existsSync(composeFile)) {
      log(`Docker Compose 文件不存在: ${composeFile}`, 'red')
      return []
    }

    const doc: any = yaml.load(fs.readFileSync(composeFile, 'utf8'))
    if (doc && doc.services) {
      return Object.keys(doc.services)
    }
    return []
  } catch (error) {
    log(`读取 ${composeFile} 失败: ${error}`, 'red')
    return []
  }
}

/**
 * 构建 Docker 镜像
 */
async function buildDockerImage(project: string, version: string) {
  const projectConfig = PROJECTS[project as keyof typeof PROJECTS]
  if (!projectConfig) {
    throw new Error(`未知项目: ${project}`)
  }

  const localTag = `${projectConfig.name}:${version}`
  const remoteTagVersion = `${projectConfig.registry}:${version}`
  const remoteTagLatest = `${projectConfig.registry}:latest`

  log(`开始构建 ${projectConfig.displayName} 镜像...`, 'cyan')

  // 构建镜像
  log(`构建镜像: ${localTag}`, 'blue')
  runCommand('export DOCKER_BUILDKIT=1')
  runCommand(`docker buildx build --platform linux/amd64 -t ${localTag} --build-arg PROJECT=${project} -f Dockerfile .`)

  // 标记版本镜像
  log(`标记版本镜像: ${remoteTagVersion}`, 'magenta')
  runCommand(`docker tag ${localTag} ${remoteTagVersion}`)

  // 推送版本镜像
  log(`推送版本镜像: ${remoteTagVersion}`, 'magenta')
  runCommand(`docker push ${remoteTagVersion}`)

  // 标记 latest 镜像
  log(`标记 latest 镜像: ${remoteTagLatest}`, 'magenta')
  runCommand(`docker tag ${localTag} ${remoteTagLatest}`)

  // 推送 latest 镜像
  log(`推送 latest 镜像: ${remoteTagLatest}`, 'magenta')
  runCommand(`docker push ${remoteTagLatest}`)

  log(`${projectConfig.displayName} 镜像构建完成`, 'green')
}

/**
 * 部署到远程服务器
 */
async function deployToRemote(project: string, services: string[], version: string) {
  const projectConfig = PROJECTS[project as keyof typeof PROJECTS]

  log('开始远程部署...', 'cyan')

  // 使用项目特定的部署脚本
  const remoteCommand = [
    `cd ${REMOTE_CONFIG.projectPath}`,
    'git pull',
    `cd apps/${project}/docker`,
    `./server_deploy.sh ${services.join(' ')}`
  ].join(' && ')

  const sshCommand = `ssh ${REMOTE_CONFIG.user}@${REMOTE_CONFIG.host} "${remoteCommand}"`

  log(`执行远程部署: ${sshCommand}`, 'yellow')
  runCommand(sshCommand)

  log('远程部署完成', 'green')
}

// ===== 主流程 =====
async function main() {
  log('===== Eliza Sales 部署工具 =====', 'cyan')

  // 清空日志文件
  if (fs.existsSync(LOG_FILE)) {
    fs.writeFileSync(LOG_FILE, '')
  }

  // 检查命令行参数
  const args = process.argv.slice(2)
  let selectedProject = ''

  // 解析 --project 参数
  const projectIndex = args.findIndex((arg) => arg === '--project')
  if (projectIndex !== -1 && args[projectIndex + 1]) {
    selectedProject = args[projectIndex + 1]
  }

  // 如果没有指定项目，则交互式选择
  if (!selectedProject || !PROJECTS[selectedProject as keyof typeof PROJECTS]) {
    const { project } = await inquirer.prompt([
      {
        type: 'list',
        name: 'project',
        message: '选择部署项目:',
        choices: Object.values(PROJECTS).map((p) => ({
          name: p.displayName,
          value: p.name
        }))
      }
    ])
    selectedProject = project
  }

  const projectConfig = PROJECTS[selectedProject as keyof typeof PROJECTS]
  log(`选择项目: ${projectConfig.displayName}`, 'green')

  // 2. 输入版本号
  const { versionInput } = await inquirer.prompt([
    {
      type: 'input',
      name: 'versionInput',
      message: '版本号（留空自动生成）:',
      default: getDefaultVersion()
    }
  ])
  const version = versionInput.trim() || getDefaultVersion()
  log(`版本号: ${version}`, 'yellow')

  // 3. 选择服务
  const availableServices = getServicesFromCompose(projectConfig.composeFile)
  if (availableServices.length === 0) {
    log(`${projectConfig.composeFile} 中没有找到服务`, 'red')
    process.exit(1)
  }

  const { selectedServices } = await inquirer.prompt([
    {
      type: 'checkbox',
      name: 'selectedServices',
      message: '选择服务 (空格选择，回车确认):',
      choices: availableServices,
      validate(answer: string[]) {
        if (answer.length < 1) {
          return '请至少选择一个服务！'
        }
        return true
      }
    }
  ])
  log(`选择服务: ${selectedServices.join(', ')}`, 'green')

  try {
    // 4. 构建和推送镜像
    log(`开始部署 ${projectConfig.displayName}...`, 'cyan')
    await buildDockerImage(selectedProject, version)

    // 5. 部署到远程服务器
    await deployToRemote(selectedProject, selectedServices, version)

    log(`===== ${projectConfig.displayName} 部署完成，版本: ${version} =====`, 'cyan')

  } catch (error) {
    log(`部署失败: ${error}`, 'red')
    process.exit(1)
  }
}

// 执行主流程
main().catch((error) => {
  log(`主流程发生错误: ${error.message}`, 'red')
  process.exit(1)
})
