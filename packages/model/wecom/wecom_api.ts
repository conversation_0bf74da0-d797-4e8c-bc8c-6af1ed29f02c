import axios, { AxiosInstance } from 'axios'

interface AccessTokenResponse {
  errcode: number
  errmsg: string
  access_token: string
  expires_in: number
}

interface CustomerDetailResponse {
  errcode: number
  errmsg: string
  external_contact: {
    external_userid: string
    name: string
    position: string
    avatar: string
    corp_name: string
    corp_full_name: string
    type: number
    gender: number
    unionid: string
    // 其他字段...
  }
  follow_user: Array<{
    userid: string
    remark: string
    description: string
    createtime: number
    // 其他字段...
  }>
  next_cursor: string
}

export class WeComAPI {
  private corpId: string
  private corpSecret: string
  private accessToken: string | null = null
  private expiresAt: number = 0
  private axiosInstance: AxiosInstance

  constructor(corpId: string, corpSecret: string) {
    this.corpId = corpId
    this.corpSecret = corpSecret
    this.axiosInstance = axios.create({
      baseURL: 'https://qyapi.weixin.qq.com/cgi-bin',
    })
  }

  private async getAccessToken(): Promise<string> {
    if (this.accessToken && Date.now() < this.expiresAt) {
      return this.accessToken
    }

    try {
      const response = await this.axiosInstance.get<AccessTokenResponse>('/gettoken', {
        params: {
          corpid: this.corpId,
          corpsecret: this.corpSecret,
        },
      })

      if (response.data.errcode !== 0) {
        throw new Error(`Failed to get access token: ${response.data.errmsg}`)
      }

      this.accessToken = response.data.access_token
      this.expiresAt = Date.now() + (response.data.expires_in * 1000) - 60000 // 提前1分钟过期

      return this.accessToken
    } catch (error) {
      console.error('Error getting access token:', error)
      throw error
    }
  }

  async getCustomerDetail(externalUserId: string, cursor?: string): Promise<CustomerDetailResponse> {
    try {
      const accessToken = await this.getAccessToken()
      const response = await this.axiosInstance.get<CustomerDetailResponse>('/externalcontact/get', {
        params: {
          access_token: accessToken,
          external_userid: externalUserId,
          cursor: cursor,
        },
      })

      if (response.data.errcode !== 0) {
        throw new Error(`Failed to get customer detail: ${response.data.errmsg}`)
      }

      return response.data
    } catch (error) {
      console.error('Error getting customer detail:', error)
      throw error
    }
  }

}