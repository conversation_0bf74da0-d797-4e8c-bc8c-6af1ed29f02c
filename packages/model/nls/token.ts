import PopCore from '@alicloud/pop-core'
import { Config } from 'config'


interface GetTokenResponse {
  ErrMsg: string
  Token: {
    UserId: string
    Id: string
    ExpireTime: number
  }
}


export class SlsToken {
  private static client: PopCore | null = null

  public static getClient() {
    if (!this.client) {
      this.client = new PopCore({
        accessKeyId: Config.setting.sls.account.accessKeyId,
        accessKeySecret: Config.setting.sls.account.accessKeySecret,
        endpoint: Config.setting.sls.tokenApi.endpoint,
        apiVersion: Config.setting.sls.tokenApi.apiVersion,
      })
    }

    return this.client
  }

  public static async getToken() {
    const client = this.getClient()

    const getTokenResponse = await client.request('CreateToken', {}) as GetTokenResponse

    return getTokenResponse.Token.Id
  }
}
