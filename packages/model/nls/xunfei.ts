import axios from 'axios'
import * as fs from 'fs'
import * as crypto from 'crypto'
import * as querystring from 'querystring'
import path from 'path'
import { randomSleep } from 'lib/schedule/schedule'
import { JSONHelper } from 'lib/json/json'

interface XunfeiASRConfig {
  appId: string
  secretKey: string
  uploadFilePath?: string
  uploadFileUrl?: string
}

class XunfeiASR {
  private appId: string
  private secretKey: string
  private uploadFilePath: string
  private uploadFileUrl: string
  private ts: string
  private signa: string
  private host = 'https://raasr.xfyun.cn/v2/api'

  constructor(config: XunfeiASRConfig) {
    this.appId = config.appId
    this.secretKey = config.secretKey
    this.uploadFilePath = config.uploadFilePath || ''
    this.uploadFileUrl = config.uploadFileUrl || ''
    this.ts = Math.floor(Date.now() / 1000).toString()
    this.signa = this.getSigna()
  }

  private getSigna(): string {
    const md5 = crypto.createHash('md5').update(`${this.appId}${this.ts}`).digest('hex')
    const sha1 = crypto.createHmac('sha1', this.secretKey).update(md5).digest()
    return Buffer.from(sha1).toString('base64')
  }

  private async upload() {
    if (this.uploadFilePath) {
      return this.uploadFromFile()
    } else if (this.uploadFileUrl) {
      return this.uploadFromUrl()
    } else {
      throw new Error('No upload source provided.')
    }
  }

  private async uploadFromFile() {
    const fileSize = fs.statSync(this.uploadFilePath).size
    const fileName = path.basename(this.uploadFilePath)

    const params = {
      appId: this.appId,
      signa: this.signa,
      ts: this.ts,
      fileSize: fileSize,
      fileName: fileName,
      duration: 200
    }

    const data = fs.readFileSync(this.uploadFilePath)
    const response = await axios.post(`${this.host}/upload?${querystring.stringify(params)}`, data, {
      headers: { 'Content-Type': 'application/octet-stream' }
    })

    return response.data
  }

  private async uploadFromUrl() {
    const params = {
      appId: this.appId,
      signa: this.signa,
      ts: this.ts,
      fileName: 'test.mp3',
      fileSize: 999,
      duration: 999,
      audioUrl: this.uploadFileUrl,
      audioMode: 'urlLink'
    }

    const response = await axios.post(`${this.host}/upload?${querystring.stringify(params)}`)

    return response.data
  }

  async getResult() {
    const uploadResp = await this.upload()
    const orderId = uploadResp.content.orderId

    let response
    let status = 3
    while (status === 3) {
      const params = {
        appId: this.appId,
        signa: this.signa,
        ts: this.ts,
        orderId: orderId,
        resultType: 'transfer,predict'
      }

      response = await axios.post(`${this.host}/getResult?${querystring.stringify(params)}`, null, {
        headers: { 'Content-Type': 'application/json' }
      })

      status = response.data.content.orderInfo.status

      if (status === 4) {
        break
      }

      // Wait for 5 seconds before checking again
      await randomSleep(1000, 3000)
    }

    if (response && response.data) {
      return this.parseResult(response.data)
    }
    return ''
  }

  private parseResult(result:any) {
    // 将 json 字符串解析为对象
    let resultObj
    try {
      resultObj = JSON.parse(result.content.orderResult)
    } catch (error) {
      console.error('Error parsing JSON:', error)
      return ''
    }

    // 用于存储所有的词
    const words: string[] = []

    if (resultObj && resultObj.lattice) {
      for (const lattice of resultObj.lattice) {
        if (lattice.json_1best) {
          try {
            const json1best = typeof lattice.json_1best === 'object' ? lattice.json_1best : JSONHelper.parse(lattice.json_1best)
            if (json1best.st && json1best.st.rt) {
              for (const rt of json1best.st.rt) {
                for (const ws of rt.ws) {
                  for (const cw of ws.cw) {
                    words.push(cw.w)
                  }
                }
              }
            }
          } catch (err) {
            console.error('Error parsing internal JSON:', err)
          }
        }
      }
    }

    // 将所有的词拼接成一个句子
    return words.join('')
  }
}

export default XunfeiASR