// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import * as Nls from 'alibabacloud-nls'
import { SlsToken } from './token'
import { PathHelper } from 'lib/path'
import axios from 'axios'
import { FileHelper } from 'lib/file'
import { Config } from 'config'

interface NlsSpeechRecognitionRes {
  payload: {
    result: string
  }
}

export class NlsSpeechRecognition {
  private client: any = null

  public async getClient() {
    const TOKEN = await SlsToken.getToken()

    this.client = new Nls.SpeechRecognition({
      url: Config.setting.sls.speechRecognition.url,
      appkey: Config.setting.sls.speechRecognition.appKey,
      token: TOKEN,
    })

    return this.client
  }


  public async recognize(filepath: string): Promise<string> {
    const url = 'https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/asr'
    const appkey = Config.setting.sls.speechRecognition.appKey
    const format = PathHelper.getFileExt(filepath)
    const sample_rate = 16000
    const enable_punctuation_prediction = true
    const enable_inverse_text_normalization = true
    const X_NLS_Token = await SlsToken.getToken()

    const audioData = await FileHelper.readFileAsBuffer(filepath)

    const response = await axios({
      method: 'post',
      url: `${url}?appkey=${appkey}&format=${format}&sample_rate=${sample_rate}&enable_punctuation_prediction=${enable_punctuation_prediction}&enable_inverse_text_normalization=${enable_inverse_text_normalization}`,
      headers: {
        'X-NLS-Token': X_NLS_Token,
        'Content-type': 'application/octet-stream',
        'Content-Length': audioData.byteLength
      },
      data: audioData.buffer
    })

    return response.data.result as string
  }
}
