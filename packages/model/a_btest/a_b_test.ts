export class ABTest {
  private static userCount: number = 0
  private static usersInGroups: Map<string, number> = new Map()

  static getStrategyForUser(user_id: string, numGroups: number): number {
    // // 如果该 user_id 已被分配到某个策略，直接返回对应策略
    // if (this.usersInGroups.has(user_id)) {
    //   return this.usersInGroups.get(user_id)!
    // }

    // 根据当前客户数计算分配的组别
    const group = this.userCount % numGroups
    this.usersInGroups.set(user_id, group)
    this.userCount++

    return group
  }
}