import { MoerOverseasAPI } from '../moer_overseas_api'


describe('Test', function () {
  const userPhone = '8613458697059'
  const userId = '39814602'
  const vodId = '7336'
  const sku = '20250303003001'
  const systemCourseSKU = '20250107003973'
  const email = '<EMAIL>'

  beforeAll(() => {

  })
  it('system course', async () => {
    console.log(await MoerOverseasAPI.getSystemCourseInfo())
  }, 60000)

  it('get user by phone', async () => {
    console.log(JSON.stringify(await MoerOverseasAPI.getUserByPhone('17356786793'), null, 4))
  }, 60000)

  it('get order by phone', async () => {
    console.log(JSON.stringify(await MoerOverseasAPI.getOrderByPhone('17356786793'), null, 4))
  }, 60000)

  it('get user by Id', async () => {
    console.log(JSON.stringify(await MoerOverseasAPI.getUserById(userId), null, 4))
  }, 60000)

  it('get user by email', async () => {
    console.log(JSON.stringify(await MoerOverseasAPI.getUserByEmail(email), null, 4))
  }, 60000)

  it('get course Info', async () => {
    console.log(JSON.stringify(await MoerOverseasAPI.getUserCourses(userId), null, 4))
  }, 60000)

  it('get chapter info', async () => {
    console.log(JSON.stringify(await MoerOverseasAPI.getUserChapterStatus({
      userId: userId,
      vodId: vodId,
      sku: sku
    }), null, 4))
  }, 60000)

  it('get energyMark mark', async () => {
    console.log(JSON.stringify(await MoerOverseasAPI.getUserEnergyMark(userId), null, 4))
  }, 60000)

  it('is own course', async () => {
    console.log(JSON.stringify(await MoerOverseasAPI.isOwnedCourse(userId, sku), null, 4))
  }, 60000)

  // it('get current week course info', async () => {
  //   const currentWeekCourseNo = DataService.getNextWeekCourseNo()
  //   console.log(JSON.stringify(await MoerOverseasAPI.getCurrentCourseInfo(currentWeekCourseNo), null, 4))
  // }, 60000)

  it('getCourseNoByMoreId', async () => {
    const userInfo = await MoerOverseasAPI.getUserById(userId)
    const courseNo = userInfo.data.userGoodsSeries.find((item) => item.type === 1)?.stage
    console.log(courseNo)
  }, 60000)

  it('getCourseInfo', async () => {
    const courseInfo = await MoerOverseasAPI.getCurrentCourseInfo(36, 37)
    console.log(JSON.stringify(courseInfo), null, 4)
  }, 60000)

  it('isPaidSystemCourse', async () => {
    console.log(await MoerOverseasAPI.getUserCoursePaid(systemCourseSKU, userId))
  }, 60000)

  // it('get email by userId', async () => {
  //   const chat_id = '8613301849894_818025375076'  // 8618279289527_818025375076
  //   const user = await ChatDB.getById(chat_id)
  //   const moerId = user?.moer_id
  //   if (!moerId) {
  //     logger.error('客户无法进入直播间，无法查到客户moreId：', error)
  //     return null
  //   }
  //   const userInfo = await MoerOverseasAPI.getUserById(moerId)
  //   const email = userInfo?.data?.email
  //   const messageTemplate = '給咱們一個鏈接哈，咱們一定要用報名email和報名手機號後六位登錄哦'
  //   const ai_msg = email ? `${messageTemplate}：${email}` : messageTemplate
  //   const currentTime = await DataService.getCurrentTime(chat_id)
  //   const liveLink = await DataService.getCourseLink(1, chat_id)
  //   console.log(ai_msg)
  //   console.log(liveLink)
  // }, 60000)
})