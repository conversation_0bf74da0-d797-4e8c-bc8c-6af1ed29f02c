import axios from 'axios'
import { Retry } from 'lib/retry/retry'
import { URLHelper } from 'lib/url/url'
import { RandomHelper } from 'lib/random/random'
import { HashSum } from 'lib/hash/hash'

interface courseInfo188{
  shortLink:string
}
interface MeditationClass {
  name: string // 活动名称
  sku: string  // SKU 唯一标识
  start_time: string // 开营时间，格式：YYYY-MM-DD HH:mm:ss
  settlement_time: string // 结营时间，格式：YYYY-MM-DD HH:mm:ss
  stage: number // 阶段数
}


interface ApiResponse<T> {
  code : number
  msg: string
  data: T
}
interface IMoerLesson {
  vodId: number         // VOD ID
  liveId: number        // 直播 ID
  day: number           // 资源对应的天数
  liveShortUrl: string      // 资源的短链接
  vodShortUrl: string       // 资源的短链接
  resourceName: string  // 资源名称
  resourceType: number  // 资源类型
}

interface IMoerCourse {
  courseNo: string       // 课程编号
  sku: string            // SKU 编号
  name: string           // 课程名称
  startTime: string      // 开始时间，ISO 日期格式
  resource: IMoerLesson[]   // 资源数组，表示与课程相关的所有资源
}

interface UserGoodSeries {
  sku: string
  stage: number
  name: string
  type: number // 描述字段可选： "类型：1入门营 2一阶 3二阶 4三阶 5四阶"
}

export interface IMoerUserInfo {
  id: string
  mobile: string
  name: string
  headImg: number
  userGoodsSeries: UserGoodSeries[]
}

interface IMoerCourseIsPaid {
  isPaid: boolean
}

interface IIsBindWechat {
  unionid: string
  result: boolean
}

interface Resource {
  vodId: number
  liveId: number
  day: number
  resourceName: string
  resourceType: number
  duration: number
  playbackTime: number | string
}

interface Course {
  id: number
  sku: string
  name: string
  type: number // 1入门营 2一阶 3二阶 4三阶 5四阶
  resource: Resource[]
  sid: number // 课程系列，跟 type 配合，系统班入门营，水晶班入门营
  start_time: string // 开营时间，格式为 2024-10-14 20:00:00
  stage: number // 期数

  is_refund: number // 0 未退款，1 已退款
}

interface CourseData {
  list: Course[]
  total_page: number
  current_page: number
  count: number
}

interface IGetChapterStatusParams {
  liveId?: string
  vodId?: string
  sku: string
  userId: string
}


interface IEnergyMark {
  id: number
  mobile: string
  examScore: number
  createdAt: string
  userId: number
}

interface IEnergyMarks {
  list: IEnergyMark[]
}

interface ICourseCompleteStatus {
  sku: string
  vodId: string
  liveId: string
  duration: number
  playbackTime: number
}

export interface IMoerUserDetail {
  contact_id: string
  name: string
  avatar: string
  mobile: string
}

export class MoerAPI {
  private static baseUrl = 'https://api.esnewcollege.com'

  private static getHeader() {
    const securit = 'yTnbPqdX223423FSS2DF2394Wt9JG1fb'
    const timestamp = Date.now().toString().slice(0, 10)
    const noncestr = RandomHelper.randomString(32)
    const user_token = ''
    const signature_params = [securit, timestamp, noncestr, user_token]
    const signature = HashSum.hash(signature_params.sort().join(''))

    return {
      timestamp,
      noncestr,
      signature,
      user_token
    }
  }

  private static async get<T>(requestUri: string, queries: object): Promise<ApiResponse<T>> {
    try {
      const response = await Retry.retry(3, async () => {
        return await axios.get<ApiResponse<T>>(URLHelper.join(this.baseUrl, requestUri), {
          params: queries,
          headers: MoerAPI.getHeader()
        })
      })

      if (response.status !== 200) {
        throw new Error(`请求失败: ${  requestUri  } status: ${response.status} ${response} \n ${JSON.stringify(queries, null, 2)}`)
      }

      if (response.data && response.data.code !== 0) {
        throw new Error(`请求失败：${response.data.code} ${response.data.msg}`)
      }

      return response.data
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败${  requestUri  }${e}`)
    }
  }

  public static async getUserPhone(params: {avatar?: string; externalUserId?: string }): Promise<IMoerUserDetail> {
    const url = '/chatgpt/get/user/info'

    const response = await this.get<IMoerUserDetail>(url, params)

    if (response.code !== 0) {
      throw new Error(`获取客户信息失败：${response.msg}`)
    }

    return response.data
  }


  public static async getSimpleIdList() {
    const url = '/chatgpt/get/enterprise/wechat/simplelist'

    const response = await this.get(url, {})

    if (response.code !== 0) {
      throw new Error(`获取客户信息失败：${response.msg}`)
    }

    return response.data
  }


  public static async get188CourseInfo() {
    const response = await this.get<courseInfo188>('/chatgpt/get/short/link', {})

    return response.data
  }


  public static async getSystemCourseInfo() {
    const response = await this.get<MeditationClass>('/chatgpt/get/course/system', {})

    return response.data
  }

  public static async getUserByPhone(phone: string) {
    const response = await this.get<IMoerUserInfo>('/chatgpt/get/user/list', {
      mobile: phone
    })

    return response.data
  }

  public static async getUserById(id: string) {
    return await this.get<IMoerUserInfo>('/chatgpt/get/user/list', {
      userId: id
    })
  }

  public static async getCurrentCourseInfo(no: number) {
    return await this.get<IMoerCourse>('/chatgpt/get/course/info/by/course/no', {
      courseNo: no
    })
  }

  public static async getUserCourses(id: string) {
    const response =  await this.get<CourseData>('/chatgpt/get/user/course', {
      userId: id
    })

    if (response.code !== 0) {
      throw new Error(`获取课程列表失败：${response.msg}`)
    }

    return response.data.list
  }

  public static async getUserChapterStatus(param: IGetChapterStatusParams) {
    const response =  await this.get<ICourseCompleteStatus>('/chatgpt/get/user/course/resource', {
      userId: param.userId,
      liveId: param.liveId,
      vodId: param.vodId,
      sku: param.sku
    })

    return response.data
  }

  public static async getUserCoursePaid(sku: string, userId: string) {
    return await this.get('/chatgpt/user/sku/paid', {
      userId,
      sku
    })
  }

  public static async getUserEnergyMark(userId: string) {
    return await this.get<IEnergyMarks>('/chatgpt/get/jinshuju/user', {
      userId
    })
  }

  public static async isOwnedCourse(userId: string, sku: string) {
    const res =  await this.get<IMoerCourseIsPaid>('/chatgpt/user/sku/paid', {
      userId,
      sku
    })

    if (res.data.isPaid) {
      return true
    }

    return false
  }

  public static async isBindWechat(userId: string) {
    const res =  await this.get<IIsBindWechat>('/chatgpt/get/user/wechatStatus', {
      userId
    })

    return Boolean(res.data.result)
  }


}