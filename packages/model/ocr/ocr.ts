import { Retry } from 'lib/retry/retry'
import axios from 'axios'
import logger from '../logger/logger'

export interface RegionData {
    confidence: number // 置信度
    text: string // 文本内容
    text_region: number[][] // 文本区域的四个顶点
}

export class FreeSpiritOCR {
  public static async recognizeUrl(url: string): Promise<RegionData[]> {
    try {
      return await Retry.retry(3, async () => {
        const response =  await axios.post('http://47.98.117.107:8000/ocr', {
          url: url
        })

        return response.data
      }) as RegionData[]
    } catch (e:any) {
      if (e.data && e.data.detail) {
        logger.error('ocr Error', e.data.detail)
      } else {
        logger.error('ocr Error', e.data.detail)
      }

      return []
    }
  }
}