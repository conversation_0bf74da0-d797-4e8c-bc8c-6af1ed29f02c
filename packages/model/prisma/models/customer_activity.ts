/**
 * 客户活跃时段相关的类型定义和枚举
 * 基于Prisma的数据模型
 */

/**
 * 时段枚举 - 8-24点分成8个时段，每2小时一个bin
 */
export enum TimeSlot {
  SLOT_08_10 = '08-10',  // 8:00-10:00
  SLOT_10_12 = '10-12',  // 10:00-12:00
  SLOT_12_14 = '12-14',  // 12:00-14:00
  SLOT_14_16 = '14-16',  // 14:00-16:00
  SLOT_16_18 = '16-18',  // 16:00-18:00
  SLOT_18_20 = '18-20',  // 18:00-20:00
  SLOT_20_22 = '20-22',  // 20:00-22:00
  SLOT_22_24 = '22-24'   // 22:00-24:00
}

/**
 * 时段权重信息接口
 * 对应Prisma schema中的TimeSlotWeight类型
 */
export interface TimeSlotWeight {
  slot: TimeSlot          // 时段枚举
  weight: number          // 权重值 (0.1-1.0)
  is_predicted: boolean   // 是否为AI预测的时段
  last_updated: Date      // 最后更新时间
}

/**
 * 客户活跃时段数据接口
 * 对应Prisma schema中的customer_activity模型
 */
export interface CustomerActivity {
  id: string                          // MongoDB ObjectId
  chat_id: string                     // 聊天ID
  time_slots: TimeSlotWeight[]        // 时段权重数组
  weight_increase_factor: number      // 权重增加系数
  weight_decrease_factor: number      // 权重减少系数
  min_weight: number                  // 最小权重
  max_weight: number                  // 最大权重
  cooldown_ms: number                 // 冷却时间（毫秒）
  created_at: Date                    // 创建时间
  updated_at: Date                    // 更新时间
}

/**
 * 创建客户活跃时段数据的输入接口
 */
export interface CreateCustomerActivityInput {
  chat_id: string
  time_slots: TimeSlotWeight[]
  weight_increase_factor?: number
  weight_decrease_factor?: number
  min_weight?: number
  max_weight?: number
  cooldown_ms?: number
}

/**
 * 更新客户活跃时段数据的输入接口
 */
export interface UpdateCustomerActivityInput {
  time_slots?: TimeSlotWeight[]
  weight_increase_factor?: number
  weight_decrease_factor?: number
  min_weight?: number
  max_weight?: number
  cooldown_ms?: number
}

/**
 * 权重调整配置接口
 */
export interface IWeightAdjustmentConfig {
  coolDown?: number        // 冷却时间（毫秒）
  increaseAmount?: number  // 权重增加量
  decreaseAmount?: number  // 权重减少量
  minWeight?: number       // 最小权重
  maxWeight?: number       // 最大权重
}

/**
 * 默认配置常量
 */
export const DEFAULT_CONFIG = {
  COOLDOWN_MS: 5 * 60 * 1000,  // 5分钟冷却时间
  INCREASE_AMOUNT: 0.2,
  DECREASE_AMOUNT: 0.1,
  MIN_WEIGHT: 0.1,
  MAX_WEIGHT: 1.0
} as const

/**
 * 时段相关的工具类型
 */
export type TimeSlotInfo = {
  slot: TimeSlot
  displayName: string
  startHour: number
  endHour: number
}

/**
 * 查询条件接口
 */
export interface CustomerActivityWhereInput {
  id?: string
  chat_id?: string
  created_at?: {
    gte?: Date
    lte?: Date
  }
  updated_at?: {
    gte?: Date
    lte?: Date
  }
}

/**
 * 排序条件接口
 */
export interface CustomerActivityOrderByInput {
  created_at?: 'asc' | 'desc'
  updated_at?: 'asc' | 'desc'
  chat_id?: 'asc' | 'desc'
}

/**
 * 分页查询参数接口
 */
export interface CustomerActivityFindManyArgs {
  where?: CustomerActivityWhereInput
  orderBy?: CustomerActivityOrderByInput | CustomerActivityOrderByInput[]
  skip?: number
  take?: number
  select?: Partial<Record<keyof CustomerActivity, boolean>>
}

/**
 * 查找单个记录参数接口
 */
export interface CustomerActivityFindUniqueArgs {
  where: {
    id?: string
    chat_id?: string
  }
  select?: Partial<Record<keyof CustomerActivity, boolean>>
}

/**
 * 创建记录参数接口
 */
export interface CustomerActivityCreateArgs {
  data: CreateCustomerActivityInput
  select?: Partial<Record<keyof CustomerActivity, boolean>>
}

/**
 * 更新记录参数接口
 */
export interface CustomerActivityUpdateArgs {
  where: {
    id?: string
    chat_id?: string
  }
  data: UpdateCustomerActivityInput
  select?: Partial<Record<keyof CustomerActivity, boolean>>
}

/**
 * 删除记录参数接口
 */
export interface CustomerActivityDeleteArgs {
  where: {
    id?: string
    chat_id?: string
  }
}

/**
 * Upsert记录参数接口
 */
export interface CustomerActivityUpsertArgs {
  where: {
    id?: string
    chat_id?: string
  }
  create: CreateCustomerActivityInput
  update: UpdateCustomerActivityInput
  select?: Partial<Record<keyof CustomerActivity, boolean>>
}
