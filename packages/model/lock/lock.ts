import { RedisCacheDB } from '../redis/redis_cache'
import RedLock from 'redlock'

export class AsyncLock {
  private redLock: RedLock

  constructor(options?: {
        retryCount?: number
        retryDelay?: number
    }) {
    this.redLock = new RedLock([RedisCacheDB.getInstance()], {
      retryCount: options?.retryCount ?? 3,
      retryDelay: options?.retryDelay ?? 200,
      automaticExtensionThreshold: 500,
    })
  }

  /**
   * 获取分布式锁并执行任务
   * @param key 锁住的资源名称
   * @param fn 需要锁住的执行函数
   * @param opts
   *
   * @example
   * const lock = new DistributedLock();
   *
   * // 回调方式
   * lock.acquire(
   *   'resourceKey',
   *   (done) => {
   *     // 异步任务
   *     done(null, result);
   *   },
   *   (err, result) => {
   *     // 处理结果
   *   },
   *   { ttl: 5000 }
   * );
   */
  async acquire<T>(
    key: string,
    fn: any,
    opts?: {timeout: number} // 毫秒
  ): Promise<T> {
    const ttl = opts?.timeout ?? 60 * 1000
    let lock
    try {
      lock = await this.redLock.acquire([key], ttl)
    } catch (e) {
      console.log('ignore error from acquire:', e)
      // 如果获取锁失败，直接执行函数（降级处理）
      return await fn()
    }

    try {
      return await fn()
    } catch (e) {
      console.error('error from fn:', e)
      throw e // 继续往上抛，避免吞掉异常
    } finally {
      if (lock) {
        try {
          // 添加超时机制防止 release 卡住
          await Promise.race([
            lock.release(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Release timeout')), 3000)
            )
          ])
        } catch (e) {
          console.log('ignore error from release:', e)
        }
      }
    }
  }
}
