import { AliyunOssBucket } from 'lib/oss'
import { AliyunCredentials } from 'lib/cer'
import { Config } from 'config'


export class FreeSpiritOss extends AliyunOssBucket {
  constructor(bucketName: string) {
    const ossConfig = Config.setting.oss
    if (!Object.keys(ossConfig).includes(bucketName)) {
      throw new Error(`bucketName ${bucketName} is not exist`)
    }

    const configure = ossConfig[bucketName]

    super(
      {
        bucket: configure.bucket,
        internal: configure.internal, // 内网访问
        region: configure.region,
        domain: configure.domain,
      },
      AliyunCredentials.getInstance(),
    )
  }
}
