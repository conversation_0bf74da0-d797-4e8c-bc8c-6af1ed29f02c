import { PrismaClient } from '../prisma_client'
import { Config } from 'config'

export class PrismaMongoClient {
  private static instance: PrismaClient | undefined
  private static configInstance: PrismaClient | undefined

  public static getInstance(dbName:string = Config.setting.projectName ?? 'yuhe'): PrismaClient {
    if (!PrismaMongoClient.instance) {
      PrismaMongoClient.instance = PrismaMongoClient.newInstance(dbName)
    }
    return PrismaMongoClient.instance
  }

  public static getConfigInstance(): PrismaClient {
    if (!PrismaMongoClient.configInstance) {
      PrismaMongoClient.configInstance = PrismaMongoClient.newInstance('bot_config')
    }
    return PrismaMongoClient.configInstance
  }

  public static newInstance(dbName:string):PrismaClient {
    if (dbName == '') {
      console.log('禁止没有dbName')
      process.exit(0)
    }
    return new PrismaClient({
      datasources: {
        db: {
          url: `mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/${dbName}?authSource=admin`,
        },
      },
    })
  }
}