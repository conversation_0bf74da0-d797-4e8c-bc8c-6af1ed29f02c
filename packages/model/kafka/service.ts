import { KafkaProducer } from './producer'
import { KafkaConsumer } from './consumer'
import logger from '../logger/logger'

interface KafkaConfig {
  brokers: string[]
  sasl: {
    username: string
    password: string
    mechanism: string
  }
  clientId: string
  groupId: string
  ssl: {
    rejectUnauthorized?: boolean
    [key: string]: any
  }
  connectionTimeout?: number
  requestTimeout?: number
  topics: {
    [key: string]: string
  }
}

export class KafkaService {
  private producer: KafkaProducer
  private consumers: Map<string, KafkaConsumer> = new Map()
  private config: KafkaConfig

  constructor(config: KafkaConfig) {
    this.config = config
    this.producer = new KafkaProducer(config as any)
  }

  async initProducer(): Promise<void> {
    await this.producer.connect()
    logger.log('Kafka producer connected')
  }

  //处理消息
  async subscribeToTopic(
    topics: string[],
    handler: (message: any) => void,
    consumerGroupId?: string
  ): Promise<void> {
    const topicKey = topics.join(',')
    const consumer = new KafkaConsumer(this.config as any, consumerGroupId)

    await consumer.connect()
    await consumer.subscribe(topics, handler)
    this.consumers.set(topicKey, consumer)
    logger.log(`Subscribed to topics: ${topics.join(', ')}`)
  }

  // async sendMessage(topic: string, message: any, key?: string): Promise<void> {
  //   await this.producer.send(topic, message, key)
  //   logger.log(`Message sent to topic: ${topic}`)
  // }
  //
  // async sendMessages(topic: string, messages: Array<{ value: any; key?: string }>): Promise<void> {
  //   await this.producer.sendBatch(topic, messages)
  //   logger.log(`${messages.length} messages sent to topic: ${topic}`)
  // }

  async unsubscribeFromTopic(topics: string[]): Promise<void> {
    const topicKey = topics.join(',')
    const consumer = this.consumers.get(topicKey)

    if (consumer) {
      await consumer.disconnect()
      this.consumers.delete(topicKey)
      logger.log(`Unsubscribed from topics: ${topics.join(', ')}`)
    }
  }

  async cleanup(): Promise<void> {
    await this.producer.disconnect()

    for (const [topicKey, consumer] of this.consumers) {
      await consumer.disconnect()
      logger.log(`Disconnected consumer for topics: ${topicKey}`)
    }

    this.consumers.clear()
  }

  getTopics() {
    return this.config.topics
  }

  getTopic(topicKey: string): string {
    return this.config.topics[topicKey] || topicKey
  }
}