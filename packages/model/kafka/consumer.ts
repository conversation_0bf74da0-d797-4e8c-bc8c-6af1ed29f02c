import { Kafka, Consumer } from 'kafkajs'
import logger from '../logger/logger'

interface KafkaConfig {
  brokers: string[]
  sasl: {
    username: string
    password: string
    mechanism: 'plain' | 'scram-sha-256' | 'scram-sha-512' | 'aws' | 'oauthbearer'
  }
  clientId: string
  groupId: string
  ssl: {
    rejectUnauthorized?: boolean
    [key: string]: any
  }
  connectionTimeout?: number
  requestTimeout?: number
}

export class KafkaConsumer {
  private kafka: Kafka
  private consumer: Consumer
  private connected: boolean = false

  constructor(config: KafkaConfig, groupId?: string) {
    this.kafka = new Kafka({
      clientId: config.clientId,
      brokers: config.brokers,
      ssl: config.ssl,
      sasl: {
        mechanism: config.sasl.mechanism,
        username: config.sasl.username,
        password: config.sasl.password,
      } as any,
      connectionTimeout: config.connectionTimeout || 10000,
      requestTimeout: config.requestTimeout || 30000,
    })

    this.consumer = this.kafka.consumer({
      groupId: groupId || config.groupId,
    })
  }

  async connect(): Promise<void> {
    if (!this.connected) {
      await this.consumer.connect()
      this.connected = true
      logger.log('Kafka consumer connected')
    }
  }

  async subscribe(topics: string[], handler: (message: any) => void): Promise<void> {
    if (!this.connected) {
      await this.connect()
    }

    for (const topic of topics) {
      await this.consumer.subscribe({ topic, fromBeginning: false })
    }

    await this.consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        try {
          const value = message.value?.toString()
          if (value) {
            const parsedMessage = JSON.parse(value)
            logger.log(`Received message from ${topic}:${partition}:${message.offset}`)
            await handler(parsedMessage)
          }
        } catch (error) {
          logger.error('Error processing message:', error)
        }
      },
    })
  }

  async disconnect(): Promise<void> {
    if (this.connected) {
      await this.consumer.disconnect()
      this.connected = false
      logger.log('Kafka consumer disconnected')
    }
  }
}