import { <PERSON><PERSON><PERSON>, Producer, Partitioners } from 'kafkajs'
import logger from '../logger/logger'

interface KafkaConfig {
  brokers: string[]
  sasl: {
    username: string
    password: string
    mechanism: 'plain' | 'scram-sha-256' | 'scram-sha-512' | 'aws' | 'oauthbearer'
  }
  clientId: string
  ssl: {
    rejectUnauthorized?: boolean
    [key: string]: any
  }
  connectionTimeout?: number
  requestTimeout?: number
}

export class KafkaProducer {
  private kafka: Kafka
  private producer: Producer
  private connected: boolean = false

  constructor(config: KafkaConfig) {
    this.kafka = new Kafka({
      clientId: config.clientId,
      brokers: config.brokers,
      ssl: config.ssl,
      sasl: {
        mechanism: config.sasl.mechanism,
        username: config.sasl.username,
        password: config.sasl.password,
      } as any,
      connectionTimeout: config.connectionTimeout || 10000,
      requestTimeout: config.requestTimeout || 30000,
    })

    this.producer = this.kafka.producer({
      createPartitioner: Partitioners.LegacyPartitioner,
    })
  }

  async connect(): Promise<void> {
    if (!this.connected) {
      await this.producer.connect()
      this.connected = true
      logger.log('Kafka producer connected')
    }
  }

  async send(topic: string, message: any, key?: string): Promise<void> {
    if (!this.connected) {
      await this.connect()
    }

    await this.producer.send({
      topic,
      messages: [
        {
          key: key || null,
          value: JSON.stringify(message),
          timestamp: Date.now().toString(),
        },
      ],
    })
  }

  async sendBatch(topic: string, messages: Array<{ value: any; key?: string }>): Promise<void> {
    if (!this.connected) {
      await this.connect()
    }

    await this.producer.send({
      topic,
      messages: messages.map(msg => ({
        key: msg.key || null,
        value: JSON.stringify(msg.value),
        timestamp: Date.now().toString(),
      })),
    })
  }

  async disconnect(): Promise<void> {
    if (this.connected) {
      await this.producer.disconnect()
      this.connected = false
      logger.log('Kafka producer disconnected')
    }
  }
}