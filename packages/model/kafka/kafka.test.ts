import { KafkaConsumer } from './consumer'
import { KafkaService } from './service'

jest.mock('../logger/logger')

describe('KafkaService', () => {
  const mockConfig = {
    brokers: [
      'alikafka-serverless-cn-db14e5zfm02-1000.alikafka.aliyuncs.com:9093',
      'alikafka-serverless-cn-db14e5zfm02-2000.alikafka.aliyuncs.com:9093',
      'alikafka-serverless-cn-db14e5zfm02-3000.alikafka.aliyuncs.com:9093'
    ],
    sasl: {
      username: 'alikafka_serverless-cn-db14e5zfm02',
      password: 'UgOTLdDdbyDctbSBT9TnaVb8h5Xw4Xu8',
      mechanism: 'plain' as const
    },
    clientId: 'haogu-kafka-client',
    groupId: 'haogu-kafka-group-1',
    ssl: {
      rejectUnauthorized: false
    },
    connectionTimeout: 10000,
    requestTimeout: 30000,
    topics: {
      scrmWorkToAiStaff: 'scrm_work_to_ai_staff',
      scrmMessageToAiStaff: 'scrm_message_to_ai_staff',
      srcmReadMarkToAiStaff: 'scrm_readMark_to_ai_staff',
      scrmCrmOrderPlacedToAiStaff: 'scrm_crm_order_placed_to_ai_staff'
    }
  }

  let kafkaConsumer: KafkaConsumer
  let kafkaService: KafkaService

  beforeEach(() => {
    jest.clearAllMocks()
    kafkaConsumer = new KafkaConsumer(mockConfig)
  })
  beforeAll(async () => {
    kafkaService = new KafkaService(mockConfig)
    await kafkaService.initProducer()
  })

  afterAll(async () => {
    await kafkaService.cleanup()
  })

  test('should consume real messages from remote topic', async () => {
    const receivedMessages: any[] = []
    const expectedMessageCount = 5 // 预期接收的消息数量

    // 消息处理函数
    const messageHandler = (message: any) => {
      console.log('Received message:', message)
      receivedMessages.push(message)
    }

    // 订阅topic
    await kafkaService.subscribeToTopic(
      [mockConfig.topics.srcmReadMarkToAiStaff],
      messageHandler,
      'integration-test-group'
    )

    // 等待接收消息 (根据你的topic消息频率调整时间)
    await new Promise((resolve) => setTimeout(resolve, 10000))

    // 验证
    expect(receivedMessages.length).toBeGreaterThan(0)
    console.log(`Received ${receivedMessages.length} messages`)

    // 取消订阅
    await kafkaService.unsubscribeFromTopic([mockConfig.topics.srcmReadMarkToAiStaff])
  }, 30000) // 30秒超时

  test('should handle message processing errors gracefully', async () => {
    const errorHandler = jest.fn((message: any) => {
      throw new Error('Processing failed')
    })

    await kafkaService.subscribeToTopic(
      [mockConfig.topics.scrmMessageToAiStaff],
      errorHandler,
      'error-test-group'
    )

    await new Promise((resolve) => setTimeout(resolve, 5000))

    await kafkaService.unsubscribeFromTopic([mockConfig.topics.scrmMessageToAiStaff])
  })


  afterEach(async () => {
    // 增加超时时间并添加错误处理
    try {
      await Promise.race([
        kafkaConsumer.disconnect(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Disconnect timeout')), 10000)
        )
      ])
    } catch (error) {
      console.warn('Disconnect failed:', error)
      // 不抛出错误，避免影响其他测试
    }
  }, 15000) // 增加 afterEach 超时时间

  describe('connect', () => {
    it('should connect to kafka successfully', async () => {
      await expect(kafkaConsumer.connect()).resolves.not.toThrow()
    }, 10000) // 增加连接超时时间
  })

  describe('subscribe', () => {
    it('should subscribe to topics and handle messages', async () => {
      const topics = ['scrm_work_to_ai_staff']
      const mockMessages = [
        {
          topic: 'scrm_work_to_ai_staff',
          partition: 0,
          message: {
            key: Buffer.from('test-key'),
            value: Buffer.from(JSON.stringify({
              type: 'work_assignment',
              staffId: 'staff_001',
              workId: 'work_123',
              timestamp: Date.now()
            })),
            offset: '0'
          }
        }
      ]

      const messageHandler = jest.fn().mockImplementation((message) => {
        console.log('📨 Received message:', {
          topic: message.topic,
          partition: message.partition,
          offset: message.message.offset,
          key: message.message.key?.toString(),
          value: JSON.parse(message.message.value.toString())
        })
      })

      await kafkaConsumer.connect()
      kafkaConsumer.subscribe(topics, messageHandler)

      // 给一点时间让订阅生效
      await new Promise((resolve) => setTimeout(resolve, 1000))
      // 模拟消息处理
      messageHandler(mockMessages[0])

      expect(messageHandler).toHaveBeenCalledTimes(1)
      expect(messageHandler).toHaveBeenCalledWith(mockMessages[0])
    }, 15000)

    it('should handle multiple topic subscriptions', async () => {
      // 为这个测试创建独立的消费者，使用不同的groupId
      const uniqueConfig = {
        ...mockConfig,
        groupId: `haogu-kafka-group-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }
      const uniqueConsumer = new KafkaConsumer(uniqueConfig)

      const topics = ['scrm_work_to_ai_staff', 'scrm_message_to_ai_staff']
      const receivedMessages: any[] = []

      const messageHandler = jest.fn().mockImplementation((message) => {
        const parsedMessage = {
          topic: message.topic,
          key: message.message.key?.toString(),
          value: JSON.parse(message.message.value.toString())
        }
        receivedMessages.push(parsedMessage)
        console.log('📨 Multi-topic message:', parsedMessage)
      })

      try {
        await uniqueConsumer.connect()
        uniqueConsumer.subscribe(topics, messageHandler)

        // 给订阅一点时间生效
        await new Promise((resolve) => setTimeout(resolve, 2000))

        // 模拟不同主题的消息
        const mockMessages = [
          {
            topic: 'scrm_work_to_ai_staff',
            message: {
              key: Buffer.from('work-key'),
              value: Buffer.from(JSON.stringify({ type: 'work', data: 'work data' }))
            }
          },
          {
            topic: 'scrm_message_to_ai_staff',
            message: {
              key: Buffer.from('msg-key'),
              value: Buffer.from(JSON.stringify({ type: 'message', content: 'hello' }))
            }
          }
        ]

        // 直接调用处理器而不是forEach
        messageHandler(mockMessages[0])
        messageHandler(mockMessages[1])

        expect(messageHandler).toHaveBeenCalledTimes(2)
        expect(receivedMessages).toHaveLength(2)
        console.log('📊 Total received messages:', receivedMessages.length)
      } finally {
        // 确保清理
        try {
          await Promise.race([
            uniqueConsumer.disconnect(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Disconnect timeout')), 8000)
            )
          ])
        } catch (error) {
          console.warn('Multi-topic test disconnect failed:', error)
        }
      }
    }, 20000) // 增加超时时间
  })

  describe('error handling', () => {
    it('should handle malformed JSON messages', async () => {
      const topics = ['scrm_work_to_ai_staff']
      const errorHandler = jest.fn()

      const messageHandler = jest.fn().mockImplementation((message) => {
        try {
          const value = message.message.value.toString()
          console.log('📨 Raw message value:', value)

          if (value === 'invalid-json') {
            throw new Error('Invalid JSON')
          }

          const parsed = JSON.parse(value)
          console.log('✅ Parsed message:', parsed)
        } catch (error) {
          console.error('❌ Message parsing error:', error)
          errorHandler(error)
        }
      })

      await kafkaConsumer.connect()
      await kafkaConsumer.subscribe(topics, messageHandler)

      // 模拟无效JSON消息
      const invalidMessage = {
        topic: 'scrm_work_to_ai_staff',
        message: {
          key: Buffer.from('error-key'),
          value: Buffer.from('invalid-json')
        }
      }

      messageHandler(invalidMessage)

      expect(errorHandler).toHaveBeenCalledTimes(1)
    })
  })

  describe('disconnect', () => {
    it('should disconnect gracefully', async () => {
      await kafkaConsumer.connect()
      await expect(kafkaConsumer.disconnect()).resolves.not.toThrow()
    })
  })
})