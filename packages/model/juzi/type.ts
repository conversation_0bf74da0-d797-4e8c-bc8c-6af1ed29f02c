export interface CreateGroupParams {
    botUserId: string
    userIds: string[] // 拉入成员的企业微信员工userId
    externalUserIds: string[] // 拉入客户的企业微信外部联系人externalUserId
    name: string
    greeting: string

    unionIds?: string[]
    banChangeName?: boolean //是否禁止修改群名
}

export enum IWecomMsgType {
    File = 1,
    Voice = 2,
    Emoticon = 5,
    Image = 6,
    Text = 7,
    Location = 8,
    MiniProgram = 9,
    Link = 12,
    Video = 13,
    VideoChannel = 14 // 视频号
}

export enum IWecomReceivedMsgType {
    Unknown = 0,
    File = 1,
    Voice = 2,
    BusinessCard = 3,
    ChatHistory = 4,
    Emoticon = 5, // 表情
    Image = 6,
    Text = 7,
    Location = 8,
    MiniProgram = 9,
    MoneyRelated = 10,
    MessageRecall = 11,
    GraphicMessage = 12,
    Video = 13,
    VideoAccount = 14,
    VoiceOrVideoCall = 15, // 语音/视频通话
    GroupInvitation = 9999,
    SystemMessage = 10000, // 系统消息
    WeChatWorkSystemMessage = 10001 // 企业微信系统消息
}

export type IWecomMessage = IWecomTextMsg | IWecomImageMsg | IWecomVoiceMsg | IWecomEmoticonMsg | IWecomVideoMsg | IWecomFileMsg | IWecomMiniProgramMsg | IWecomVideoAccountMsg | IWecomLinkMsg


export interface IWecomLinkMsg {
    type: IWecomMsgType.Link
    sourceUrl: string
    title: string
    summary: string
    imageUrl: string
}

export interface IWecomFileMsg {
    type: IWecomMsgType.File
    name: string // 文件名
    url: string
    size?: number // 文件大小
}

export interface IWecomVideoMsg {
    type: IWecomMsgType.Video
    url: string
}

export interface IWecomMiniProgramMsg {
    type: IWecomMsgType.MiniProgram
    appId: string          // 小程序原始ID
    description: string    // 描述
    pagePath: string       // 跳转地址
    thumbUrl: string       // 封面图地址
    title: string          // 标题
    username: string       // 小程序ID
    iconUrl?: string       // icon地址，非必需
}

// 视频号消息接口
export interface IWecomVideoAccountMsg {
    type: IWecomMsgType.VideoChannel
    avatarUrl: string      // 头像地址
    coverUrl: string       // 封面地址
    description: string    // 描述
    feedType: number       // 未知，目前固定为4
    nickname: string       // 昵称
    thumbUrl: string       // 缩略图地址
    url: string            // 视频号地址
    extras: string         // 未知，请使用收到的字段信息
}

export interface IWecomImageMsg {
    type: IWecomMsgType.Image
    url: string
    size?: number // 图片大小
}

export interface IWecomVoiceMsg {
    type: IWecomMsgType.Voice
    voiceUrl: string
    duration?: number // 语音时长(秒）
}

export interface IWecomEmoticonMsg {
    type: IWecomMsgType.Emoticon
    imageUrl: string
}
export interface IWecomTextMsg {
    type: IWecomMsgType.Text
    text: string
    mention?: string[] //@人的wxid列表, @all 可以@所有人
}

export enum IReceivedMessageSource {
    MobilePush = 0, // 手机推送过来的消息
    TeamConsoleManual = 1, // 小组级控制台手动发送消息
    MassBroadcast = 2, // 群发
    AutoReply = 3, // 自动回复
    GroupCreation = 4, // 创建群聊
    OtherBotReply = 5, // 其他机器人回复
    APIMessage = 6, // API 发消息
    SOPFeature = 7 // SOP 功能
}

export interface IReceivedTextMsg {
    text: string
    mention?: string[] //@人的wxid列表
    quoteMessage?: {
        content: {
            mention: string    // 被@的人的wxid
            text: string       // 被引用消息
        }
        messageId: string  // 被引用消息id
        nickname: string   // 被引用消息人员昵称
        timestamp: number  // 时间戳
        type: number       // 被引用消息类型
        wxid: string       // 被引用人系统id
    }
}

export interface IReceivedImageMsg {
    imageUrl: string // 压缩图片地址
    size?: number // 图片大小
    artwork?: {  // 原始图片信息
        height: number
        width: number
    }
}

export interface IReceivedVoiceMsg {
    text?: string
    voiceUrl: string
    duration: number // 语音时长(秒）
}

export interface IReceivedRecallMsg {
    content: string
}

export interface IReceivedEmoticonMsg {
    imageUrl: string // 表情地址
}

export interface IReceivedVideoChannelMsg {
    avatarUrl: string
    coverUrl: string
    description: string
    extras: string
    feedType: number
    nickname: string
    objectId: string
    objectNonceId: string
    thumbUrl: string
    url: string
}

export interface IReceivedVideoFileMsg {
  videoUrl: string
}

export interface IReceivedLinkMsg {
  description: string
  thumbnailUrl: string
  title: string
  url: string
}

interface IChatHistoryItem {
    avatar: string // 头像
    corpName: string // 企业名称
    message: any
    senderName: string // 发送者名称
    time: number
    type: number
}

export interface IReceivedChatHistoryMsg {
    chatHistoryList: IChatHistoryItem[]
    content: string
}

export type IReceivedMessagePayload = IReceivedTextMsg | IReceivedImageMsg | IReceivedVoiceMsg | IReceivedEmoticonMsg | IReceivedVideoChannelMsg | IReceivedVideoFileMsg | IReceivedLinkMsg | IReceivedChatHistoryMsg

export interface IReceivedMessage {
    avatar: string // 客户或者群头像
    botId: string // bot的账号id
    botUserId: string  // bot的企业微信userId
    chatId: string // 对话id（表示一个bot和一个客户）
    contactName: string // 客户姓名
    contactType: number // 客户的类型
    coworker: boolean // 是否为内部员工
    customerExternalUserId?: string // 客户的企业微信externalUserId，只在私聊中存在
    externalUserId: string  // 客户的企业微信externalUserId
    imBotId: string // 托管账号对应成员的系统wxid
    imContactId?: string // 客户的系统wxid
    imRoomId?: string // 群聊的系统wxid
    isSelf: boolean // 消息是否是托管账号自己发送的消息
    messageId?: string // 消息id
    messageType: IWecomReceivedMsgType // 消息的payload类型
    orgId: string // 企业id
    payload?: IReceivedMessagePayload  // 消息内容
    roomTopic?: string // 群名
    roomWecomChatId?: string // 群的企微chatId
    sendBy?: string // 消息发送者的秒回id，仅当消息为消息为托管账号发送的，且消息来源为小组级控制台手动发消息时有效
    source?: IReceivedMessageSource // 消息来源
    token: string   // 企业级token
    timestamp: number // 消息时间戳
}

export interface ISendMessageResult {
    orgId: string
    token: string
    requestId: string
    externalRequestId?: string
    timestamp: number
    botId: string
    imBotId: string
    imContactId: string
    imRoomId: string
    messageType: number
    payload?: any
    sendCode: number
    errorcode: number
    errormsg: string
    messageId: string
}
