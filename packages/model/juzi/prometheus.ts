import { Counter } from 'lib/prometheus_client/index'

export const juziSendMessageCounter = new Counter({
  name: 'juzi_send_message',
  help: 'The times of sending message by juzi',
  labelNames:['bot_id']
})

export const juziSendMessageErrorCounter = new Counter({
  name: 'juzi_send_message_error',
  help: 'The times of occurring error when sending message by juzi',
  labelNames:['bot_id']
})

export const juziSendGroupMessageCounter = new Counter({
  name: 'juzi_send_group_message',
  help: 'The times of sending message by juzi',
  labelNames:['bot_id']
})

export const juziSendGroupMessageErrorCounter = new Counter({
  name: 'juzi_send_group_message_error',
  help: 'The times of occurring error when sending group message by juzi',
  labelNames:['bot_id']
})