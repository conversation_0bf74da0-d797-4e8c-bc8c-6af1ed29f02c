import axios from 'axios'
import { Retry } from 'lib/retry/retry'
import { Config } from 'config'


interface ApiResponse<T> {
    status: number
    data: T
}

export class JuZiWecomClient {
  private baseUrl: URL
  private readonly token: string

  constructor() {
    this.baseUrl = new URL(Config.setting.juziWecom.baseUrl)
    this.token = Config.setting.juziWecom.token
  }

  async get<T>(requestUri: string, payload: object): Promise<ApiResponse<T>> {
    payload = Object.assign(payload, { token: this.token })

    try {
      const response = await Retry.retry(3, async () => {
        return await axios.get<T>(this.baseUrl.href + requestUri, {
          params: payload,
        })
      })

      JuZiWecomClient.logError(requestUri, payload, response)

      return response
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败：${  requestUri  }\n${e}`)
    }
  }

  async post<T>(requestUri: string, payload: any): Promise<ApiResponse<T>> {
    try {
      const response = await Retry.retry(3, async () => {
        return await axios.post<T>(this.baseUrl.href + requestUri, payload,  {
          params: {
            token: this.token
          }
        })
      }, {
        delayFunc: (count) => count * 200
      })

      JuZiWecomClient.logError(requestUri, payload, response)

      return response
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败：${  requestUri  }\n${e}`)
    }
  }


  async put<T>(requestUri: string, payload: any): Promise<ApiResponse<T>> {

    try {
      const response = await Retry.retry(3, async () => {
        return await axios.put<T>(this.baseUrl.href + requestUri, payload, {
          params: {
            token: this.token
          }
        })
      }, {
        delayFunc: (count) => count * 200
      })

      JuZiWecomClient.logError(requestUri, payload, response)

      return { status: response.status, data: response.data }
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败：${  requestUri  }\n${e}`)
    }
  }

  private static logError<T>(requestUri: string, payload: any, response: ApiResponse<T>) {
    const data = response.data as any
    if (data && data.errcode && data.errcode !== 0) {
      console.log(`请求失败：${  requestUri  }\n请求参数：${JSON.stringify(payload)}\n返回结果：${JSON.stringify(data)}`)
    }
  }
}