import axios from 'axios'
import { Config } from 'config'
import { CreateGroupParams, IWecomMessage } from './type'
import { UUID } from 'lib/uuid/uuid'
import { JuZiWecomClient } from './client'
import { Retry } from 'lib/retry/retry'
import logger from '../logger/logger'
import { juziSendGroupMessageCounter, juziSendGroupMessageErrorCounter, juziSendMessageCounter, juziSendMessageErrorCounter } from './prometheus'

interface ICommonResponse {
  errcode: number
  errmsg: string
  data: any
}

interface IChangeRoomNameParam {
  imRoomId: string     // The ID of the room to be renamed
  imBotId: string      // The ID of the bot responsible for the room
  name: string         // The new name for the room
}

interface IWxIdToExternalIdResponse {
  errcode: number
  errmsg: string
  externalUserId: string
}

interface IGroupInfoData {
  errcode: number // 错误码
  errmsg: string // 错误原因
  data: {
    wecomChatId: string // 群聊系统id
    name: string // 群名称
    owner: string // 群主系统id
    createTime: number // 群创建时间
    memberList: Array<{
      type?: number // 群成员类型 (微信、企微)
      imContactId?: string // 群成员系统id
      joinTime?: number // 加入群聊时间
      joinScene?: number // 入群方式
      externalUserId?: string // 群成员企微联系人id
      identity?: 1 | 2 // 群成员身份 (枚举值)
      nickName?: string // 昵称
      avatarUrl?: string // 客户头像
    }>
    chatAvatar: string // 群头像
    imRoomId: string // 系统群聊ID
    systemTag?: Array<{
      // 群自定义标签
      [key: string]: any
    }>
    notice?: string // 群公告
  }
}

interface IExternalIdToWxIdResponse {
  errcode: number
  errmsg: string
  wxid: string
}

interface ICreateRoomResponse {
    errcode: number
    errmsg: string
    data: {
        chatId: string
        roomWxid: string
    }
}

interface Tag {
  tagName: string
  type: string
  groupName: string
  tagId: string
}


interface IGetCustomerInfoResponse {
  errcode: number
  errmsg: string
  data: {
    imContactId: string
    name: string
    avatar: string
    gender: string
    remark: string
    createTimestamp: string
    remarkMobiles: string[]
    imInfo: {
      type: string
      tags: Tag[]
      externalUserId: string
      unionId: string
      followUser: {
        wecomUserId: string
        state: string
      }
    }
    botInfo: any
    systemTags?: any
  }
}

interface AddToGroupParams {
  botUserId: string
  contactWxid: string // 客户的 imContactId
  roomWxid: string // 群聊的 wxId
}

interface RemoveFromGroupParams {
  botUserId: string
  contactWxid: string // 客户的 imContactId
  roomWxid: string // 群聊的 wxId
}

interface IJuziSendMsgParam {
  imBotId: string
  imRoomId?: string
  imContactId?: string
  msg: IWecomMessage
  isAtAll?: boolean
  isAnnouncement?: boolean

  externalRequestId?: string
}

interface GetGroupDetailParams {
  imBotId: string
  imRoomId: string
}

interface IsInGroupParams {
  imBotId: string
  imRoomId: string
  imContactId: string
}

interface IAddFriendParam {
  imBotId: string
  phone: string
  hello: string
  type?: 1 | 3 // 1 个微，3 企微
}

interface IAddToGroupResponse {
  errcode?: number
  errmsg?: string
}

/**
 * 句子接口，注意要自己处理报错
 */
export class JuziAPI {
  public static token = Config.setting.juziWecom.token

  public static async addFriendByPhone(param: IAddFriendParam) {
    const url = 'v2/customer/addByPhone'
    const client = new JuZiWecomClient()
    return await client.post(url, param)
  }


  public static async getOriginalImage(chatId: string, messageId: string) {
    const url = 'https://ah-bg.ddregion.com/stream-api/message/getArtworkImage'

    try {
      const response = await axios.post(url, {
        token: Config.isOnlineTestAccount() ? '661cfbe507ea8c7bacfe5c8c' : Config.setting.wechatConfig?.orgToken as string,
        chatId,
        messageId
      })


      return response.data
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }

  public static async pullChatHistory(date: string, pageSize: string, seq?: string) {
    let url = `https://aa-api.ddregion.com/message/history?token=661cfbe507ea8c7bacfe5c8c&snapshotDay=${date}&pageSize=${pageSize}`

    if (seq) {
      url += `&seq=${seq}`
    }

    try {
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }

  public static async createRoom(createGroupParams: CreateGroupParams) {
    const url = 'v1/instantReply/createRoom'
    const client = new JuZiWecomClient()

    const response = await client.post<ICommonResponse>(url, createGroupParams)

    return response.data.data

  }

  public static async addToGroup(params: AddToGroupParams) {
    const url = 'v1/instantReply/addFromRoom'
    const client = new JuZiWecomClient()

    const response =  await client.post<IAddToGroupResponse>(url, params)

    return response.data
  }

  /**
   * 修改群主
   * @param imRoomId
   * @param imBotId
   * @param newOwnerImContactId
   */
  public static async changeRoomOwner(imRoomId: string, imBotId: string, newOwnerImContactId: string) {
    const url = 'v1/instantReply/changeRoomOwner'
    const client = new JuZiWecomClient()

    const response = await client.post<ICommonResponse>(url, {
      imRoomId,
      imBotId,
      newOwnerImContactId
    })

  }

  /**
   * 查询群详情
   */
  public static async groupDetail(params: GetGroupDetailParams) {
    const url = 'v2/groupChat/detail'
    const client = new JuZiWecomClient()

    return await client.get<IGroupInfoData>(url, params)
  }

  public static async isInGroup(param: IsInGroupParams) {
    const response = await this.groupDetail(param)
    const data = response.data as IGroupInfoData

    if (data && data.errcode === 0 && data.data) {
      return data.data.memberList.some((item) => item.imContactId === param.imContactId)
    }

    return false
  }

  public static async listCustomers(imBotId: string) {
    const url = 'v2/customer/list'
    const client = new JuZiWecomClient()
    const response = await client.get(url, {
      current: 2,
      pageSize: 200,
      imBotId
    })

    return response.data
  }

  public static async sendMsg(param: IJuziSendMsgParam) {
    try {
      juziSendMessageCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)

      if (!param.imContactId && !param.imRoomId) {
        throw new Error('imContactId 和 imRoomId 不能同时为空')
      }

      const imBotId = param.imBotId
      const externalRequestId = param.externalRequestId ?? UUID.short()
      const url = 'v2/message/send'
      const messageType = param.msg.type

      return await new JuZiWecomClient().post(url, {
        imBotId,
        externalRequestId,
        imContactId: param.imContactId,
        imRoomId: param.imRoomId,
        messageType: messageType,
        payload: param.msg,
        isAtAll: param.isAtAll,
        isAnnouncement: param.isAnnouncement
      })
    } catch (e) {
      juziSendMessageErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
      throw e
    }
  }


  public static async sendGroupMsg(from: string, groupId: string, msg: IWecomMessage) {
    try {
      juziSendGroupMessageCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
      const imBotId = from
      const externalRequestId = UUID.short()
      const url = 'v2/message/send'
      const messageType = msg.type


      return await new JuZiWecomClient().post(url, {
        imBotId,
        externalRequestId,
        imRoomId: groupId,
        messageType: messageType,
        payload: msg
      })
    } catch (e) {
      juziSendGroupMessageErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
      throw e
    }
  }

  static async externalIdToWxId(wx_id: string, imBotId: string) {
    const url = 'v1/customer/externalUserId_to_wxid'

    const client = new JuZiWecomClient()
    const response = await client.get<IExternalIdToWxIdResponse>(url, {
      externalUserId: wx_id,
      imBotId: imBotId
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.wxid
    }

    return null
  }

  static async getMembersList(current: number, pageSize: number) {
    const url = 'v1/user/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse >(url, {
      current,
      pageSize
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async getCustomerInfo(botId: string, customerId: string) {
    const url = 'v2/customer/detail'
    const client = new JuZiWecomClient()
    const response = await client.post<IGetCustomerInfoResponse>(url, {
      systemData: {
        imBotId: botId,
        imContactId: customerId
      }
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async getMemberInfo(customerId: string) {
    const url = 'v2/user/detail'
    const client = new JuZiWecomClient()
    const response = await client.get<IGetCustomerInfoResponse>(url,
      {
        uid: customerId
      }
    )

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }


  /**
   * 这个接口有多账号 externalUserId 不一致的风险，采用 customer/detail 接口
   * @param user_id
   */
  static async wxIdToExternalUserId(user_id: string) {
    const customerInfo = await this.getCustomerInfo(Config.setting.wechatConfig?.id as string, user_id)

    if (customerInfo) {
      return customerInfo.imInfo.externalUserId
    }

    return null
  }

  static async updateUserAlias(botId: string, userId: string, newAlias: string) {
    const url = 'v2/customer/updateContactRemark'

    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, {
      imBotId: botId,
      imContactId: userId,
      remark: newAlias
    })

    if (response.data && response.data.errcode === 0) {
      return response.data
    }

    return null
  }

  static async getTags() {
    const url = 'v2/tag/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {})

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  /**
   * https://apifox.com/apidoc/shared-71b1c1e2-7473-4473-9e25-d2091b22199e/api-147790387
   * @param externalUserId
   * @param botUserId
   * @param addTags 标签 ID
   * @param removeTags 标签 ID
   */
  static async updateUserTags(externalUserId: string, botUserId: string, addTags: string[], removeTags: string[]) {
    const url = 'v2/tag/mark'

    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, {
      externalUserId,
      botUserId: botUserId,
      addTagId: addTags,
      removeTagId: removeTags
    })

    if (response.data && response.data.errcode === 0) {
      return response.data
    }

    return null
  }


  static async getGroups() {
    const url = 'v2/group/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {})

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async addPhoneNumber(imBotId: string, imContactId: string, phoneNumbers: string[]) {
    const url = 'v2/customer/addContactPhoneNumbers'
    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, {
      imBotId,
      imContactId,
      addPhoneNumbers: phoneNumbers,
      overrideExistPhoneNumbers: true
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async getCustomerPhoneNumber(botId: string, customerId: string) {
    const customerInfo = await this.getCustomerInfo(botId, customerId)
    if (customerInfo) {
      return customerInfo.remarkMobiles
    }

    return null
  }

  static async listGroup(imBotId: string) {
    const url = 'v2/groupChat/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {
      current: 1,
      pageSize: 100,
      imBotId
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  public static async removeFromGroup(params: RemoveFromGroupParams) {
    const url = 'v1/instantReply/removeFromRoom'
    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, params)

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    } else {
      logger.error('群聊踢人失败', response.data.errcode, response.data.errmsg, JSON.stringify(params, null, 2))
      throw (`踢人失败 ${JSON.stringify(params, null, 2)}`)
    }
  }

  public static async kickNonWhitelistMembers(botUserId: string, imBotId: string, imRoomId: string, whitelist:string[]) {
    if (!imBotId || !imRoomId) {
      throw new Error('imBotId and imRoomId are required')
    }

    try {
      const groupDetail = await this.groupDetail({ imBotId, imRoomId })
      const data = groupDetail.data as IGroupInfoData

      if (data && data.errcode === 0 && data.data) {
        const members = data.data.memberList
          .filter((member) =>
            member.imContactId && !whitelist.includes(member.imContactId)
          ) // 移除掉白名单中的客户

        const promises = members.map((member) => Retry.retry(4, async () => {
          return this.removeFromGroup({
            botUserId: botUserId,
            contactWxid: member.imContactId as string,
            roomWxid: imRoomId
          })
        }, {
          delayFunc :(retryCount) => {
            if (retryCount === 1) return getRandomInt(0, 1 * 60 * 1000)
            if (retryCount === 2) return getRandomInt(1 * 60 * 1000, 2 * 60 * 1000)
            if (retryCount === 3) return getRandomInt(2 * 60 * 1000, 5 * 60 * 1000)
            if (retryCount === 4) return getRandomInt(5 * 60 * 1000, 10 * 60 * 1000)
            return 0  // 之后不再进行重试
          }
        }))
        await Promise.allSettled(promises)
      }
    } catch (error) {
      console.error('Error kicking non-whitelist members:', error)
      throw new Error('Failed to kick non-whitelist members')
    }
  }

  private static chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  public static async changeRoomName(param: IChangeRoomNameParam) {
    if (!param.imRoomId || !param.imBotId || !param.name) {
      throw new Error('token, imRoomId, imBotId, and name are required')
    }

    const url = 'v1/instantReply/changeRoomName'  // The API URL for changing room name

    try {
      return await new JuZiWecomClient().post(url,  {
        imRoomId: param.imRoomId,
        imBotId: param.imBotId,
        name: param.name
      })
    } catch (error) {
      console.error('Error changing room name:', error)
      throw new Error('Failed to change room name')
    }
  }

  static async listAccounts(token: string) {
    const url = `https://ah-bg.ddregion.com/stream-api/bot/list?token=${token}`

    try {
      const response = await axios.get(url)

      return response.data
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }
}

function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}