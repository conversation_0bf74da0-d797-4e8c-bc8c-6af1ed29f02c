import { RedisDB } from './redis'
import { RedisCacheDB } from './redis_cache'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(JSON.stringify(await RedisDB.getInstance().set('testKey', 'testValue'), null, 4))

    console.log(JSON.stringify(await RedisDB.getInstance().get('testKey'), null, 4))

    console.log(JSON.stringify(await RedisDB.getInstance().get('testKey1s'), null, 4))
  }, 60000)

  it('cacheDB', async () => {
    // 建立 moer externalId 的映射
    const redis = new RedisCacheDB('1688855025632783')

    // await redis.set({
    //   event: '123',
    //   userId: '123'
    // }, 24 * 60 * 60)

    console.log(JSON.stringify(await redis.get(), null, 4))
  }, 60000)
})