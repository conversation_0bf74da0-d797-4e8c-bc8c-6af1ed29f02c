import logger from '../logger/logger'

export function catchGlobalError() {
  // 捕获未捕获的同步异常
  process.on('uncaughtException', (err) => {
    logger.warn(err, 'Uncaught Exception')
  })

  // 捕获未处理的 Promise 拒绝
  process.on('unhandledRejection', (reason, promise) => {
    logger.warn({ promise,  reason: reason instanceof Error ? reason.message + reason.stack : reason }, 'Unhandled Rejection at: Promise')
  })
}