/**
 * Types for Ai营销直播接口
 */

export interface ApiResponse<T> {
  code: number
  msg: string
  data: T
  success?: boolean
  notSuccess?: boolean
}

// token response is represented as ApiResponse<string>

export interface ColumnItem {
  courseId: string
  courseName: string
  img: string
  createTime: string
}

export interface CartItem {
  id: number
  channelId: string
  productId: number
  name: string
  cover: string
  productDesc: string
  realPrice: number
  link: string
}

export interface LiveItem {
  img: string
  liveId: string
  liveName: string
  aliveState: number
  url: string
  shortUrl: string
  teacher: string
  watchUserNum: number
  upvoteNum: number
  cartList: CartItem[]
  createTime: string
  aliveStartAt: string
  aliveStopAt?: string
}

export interface GoodsDetailItem {
  unifiedId: string
  unionId: string
  isDeal: boolean
  createDate: string
  payDate: string | null
  nickName: string
  avatar: string
  createBy: any
  createByName: any
  createByOfficeCode: string
  createByOfficeName: string
}

export interface ProductPushPayload {
  id: number
}

export interface BookItem {
  id: number
  liveId: string
  userId: string
  unionId: string
  status: number
  createTime: string
  updateTime: string
}

export interface QueryUserWatchListDataItem {
  channelId: string
  cmsUserId: string
  unifiedId: string
  userNickName: string
  totalStudyTime: number
  liveStudyTime: number
  backStudyTime: number
  lastInTime: string
  firstInTime: string
  wxUnionId: string
}

export interface QueryUserWatchListData {
  pageSize: number
  pageIndex: number
  data: QueryUserWatchListDataItem[]
  resultCount: number
  resultUserCount: number
  start: number
  pageCount: number
}

export interface LiveMessageItem {
  id: string
  content: string
  image: string
  quote: string
  channelId: string
  time: string
  userType: string
  status: string
  sourceType: string
  userId?: string
  nickName?: string
  unionId?: string
}

export interface QueryLiveMessagePageData {
  pageSize: number
  pageIndex: number
  data: LiveMessageItem[]
  resultCount?: number
  resultUserCount?: number
  start?: number
  pageCount?: number
}

// Request payload shapes
export interface GetTokenPayload {
  appId: string
  appSecret: string
}

export interface GetBookByLiveIdPayload {
  liveId: string
  unionId?: string
}

export interface QueryUserWatchListPayload {
  data: {
    channelId: string
    wxUnionId?: string
  }
  pageIndex?: number
  pageSize?: number
}

export interface QueryLiveMessagePagePayload {
  pageSize: number
  pageIndex: number
  data: {
    startDay?: string
    endDay?: string
    channelId: string
  }
}

export type ApiColumnsRes = ColumnItem[]
export type ApiLiveListRes = LiveItem[]
export type ApiGoodsDetailRes = GoodsDetailItem[]
export type ApiProductPushRes = boolean
export type ApiBookListRes = BookItem[]
export type ApiQueryUserWatchListRes = QueryUserWatchListData
export type ApiQueryLiveMessagePageRes = QueryLiveMessagePageData

export type Maybe<T> = T | null
