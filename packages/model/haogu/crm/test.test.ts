import { Haogu<PERSON><PERSON> } from './client'
import logger from '../../logger/logger'
import dayjs from 'dayjs'

describe('test haogu api', () => {
  jest.setTimeout(30000)
  // const baseUrl = 'http://172.16.14.66:54001'
  // const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQjH+wQhar1449U4kMJv9TkHJMd2QyDlSXRMl6r9vLA1uMljtktfGiiuoaP6Y4BJKDnh10IhQOrz8eC2d1I7iRRqEIZXm/88rWyI6UX1IeZ9aFL/VZlensT9qH8dW062hMzApvfGtXCHVmLIL++WAXb/1+MzWRQ4oTgF7WuU1KwIDAQAB'
  const baseUrl = 'https://wecom-api.integrity.com.cn'
  const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCeF5P8b+0oaf1ukVPYfKGHDv+c51qUz+hrn23OEuXxSVbai+iEVJhn9YOmpLBjYeTaYje4u32j7uu87Bv+1E455e5Sat9Qs/DHeYbaTA/geojL6PgTLo/CMmlTP7Ac7oF4UQ3CmQY3M/OOVtA0Pje+fa1z+242oeX0WyQNYY0PiQIDAQAB'
  afterAll((done) => {
    logger.getInstance().flush()
    done()
  }, 5000)

  it('queryAiConversationStaffList', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const response = await client.queryAiConversationStaffList()
    console.log('queryAiConversationStaffList response:', response)
  })

  it('getCustByStaffId', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    // replace with a real staff id that exists in your environment
    const staffId = 2300
    const response = await client.getCustByStaffId(staffId, 1, 20)
    console.log('getCustByStaffId response:', response)
  })
  it('getCustomInfo', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const custUnifiedUserId = '592193926873089053'
    const response = await client.getCustomInfo(1, 20, custUnifiedUserId)
    console.log('getCustomInfo response:', response)
  })

  it('queryChatPage', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const staffId = 113
    const custUnifiedUserId = '154967222046722050'
    const response = await client.queryChatPage(1, 20, staffId, undefined, undefined, custUnifiedUserId)
    console.log('queryChatPage response:', response)
  })

  it('getCustomerLabel', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const staffId = 2300
    const custUnifiedUserId = '8960851390472194019'
    const labelRes = await client.getCustomerLabel(staffId, custUnifiedUserId)
    console.log('getCustomerLabel response:', labelRes)
  })

  it('getCustomerAILabel', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const staffId = 113
    const custUnifiedUserId = 123
    const aiLabelRes = await client.getCustomerAILabel(staffId, [custUnifiedUserId])
    console.log('getCustomerAILabel response:', aiLabelRes)
  })

  it('markAILabel', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const staffId = 1
    const custUnifiedUserId = 123
    const markRes = await client.markAILabel(staffId, custUnifiedUserId, [], [])
    console.log('markAILabel response:', markRes)
  })

  it('sendTextMessage', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const toolUserId = '1688857493732239'
    const conversationId = 'S:1688857493732239_7881302146051227'
    const mockId = 'mock-1213123123213'
    const textRes = await client.sendTextMessage(toolUserId, 'hello world', conversationId, mockId)
    console.log('sendTextMessage response:', textRes)
  })

  it('sendImageMessage', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const toolUserId = 'tool-1'
    const conversationId = 'conv-1'
    const mockId = 'mock-1'
    const imageRes = await client.sendImageMessage(toolUserId, conversationId, mockId, 'http://example.com/image.jpg', 1024, 640, 480, 'md5sum')
    console.log('sendImageMessage response:', imageRes)
  })

  it('sendEmotionMessage', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const toolUserId = 'tool-1'
    const conversationId = 'conv-1'
    const mockId = 'mock-1'
    const emotionRes = await client.sendEmotionMessage(toolUserId, conversationId, mockId, 'http://example.com/emo.png', 1)
    console.log('sendEmotionMessage response:', emotionRes)
  })

  it('sendVideoMessage', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const toolUserId = 'tool-1'
    const conversationId = 'conv-1'
    const mockId = 'mock-1'
    const videoRes = await client.sendVideoMessage(toolUserId, conversationId, mockId, 'http://example.com/video.mp4', 2048, 1280, 720, 'md5sum')
    console.log('sendVideoMessage response:', videoRes)
  })

  it('sendFileMessage', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const toolUserId = '1688856290606721'
    const conversationId = 'S:1688856290606721_7881302146051227'
    const mockId = 'mock-1123123123123'
    const link = 'https://zhyt-scrm.oss-cn-hangzhou.aliyuncs.com/uploads/frontend/2025/03/19/a7ad9d8bbf8b4bebb4989835091e6659.pdf'
    const fileSize = 198262
    const md5 = '25e6dbf34f0d254424763acf3701244a'
    const fileRes = await client.sendFileMessage(toolUserId, conversationId, mockId, link, fileSize, md5, 'hello.pdf')
    console.log('sendFileMessage response:', fileRes)
  })

  it('sendLinkMessage', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const toolUserId = '1688856290606721'
    const conversationId = 'S:1688856290606721_7881302146051227'
    const mockId = 'mock-1123123123'
    const title = 'hello'
    const desc = 'hello_desc'
    const url = 'url'
    const image = 'image_url'
    const linkRes = await client.sendLinkMessage(toolUserId, conversationId, mockId, title, desc, url, image)
    console.log('sendLinkMessage response:', linkRes)
  })

  it('sendMpvVideoMessage', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const toolUserId = '1688856290606721'
    const conversationId = 'S:1688856290606721_7881302146051227'
    const mockId = 'mock-11231231234'
    const miniRes = await client.sendMpvVideoMessage(toolUserId, conversationId, mockId, 39324)
    console.log('sendMpvVideoMessage response:', miniRes)
  })

  it('contentList', async () => {
    const client = new HaoguApi(baseUrl, publicKey)
    const pageNo = 1
    const pageSize = 10
    const staffId = 113
    const type = 300 // 多媒体
    const mediaType = 1 // 图片
    const shared = 0
    const res = await client.contentList(pageNo, pageSize, staffId, type, mediaType, shared)
    console.log('contentList response:', res)
  })

})