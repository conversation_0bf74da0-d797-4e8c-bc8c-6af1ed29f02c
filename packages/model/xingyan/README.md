# XingyanClient 依赖注入重构

## 概述

XingyanClient 已经重构为支持外部注入 YuHeAccount 类型配置，提供更灵活的账户管理方式。

## 主要变更

### 1. 新增接口和类型

```typescript
// YuHe 账户类型枚举
export enum YuHeAccountType {
  ZhongShenTong = 0,
  ZhiCheng = 1
}

// 星炎云账户配置接口
export interface IXingyanAccountConfig {
  accountType: YuHeAccountType
  baseUrl: string
  cropId: number
  secretKey: string
}
```

### 2. XingyanClient 支持依赖注入

```typescript
// 使用默认配置（向后兼容）
const client = new XingyanClient()

// 使用外部注入的配置
const accountConfig: IXingyanAccountConfig = {
  accountType: YuHeAccountType.ZhiCheng,
  baseUrl: 'https://open-api.marscrm.cn/openApi',
  cropId: ****************,
  secretKey: '733d96254487afbbb8892cdfd3b82e61'
}
const client = new XingyanClient(accountConfig)
```

### 3. XingyanAPI 支持实例化和依赖注入

```typescript
// 使用默认配置
const api = new XingyanAPI()

// 使用外部注入的配置
const api = new XingyanAPI(accountConfig)

// 使用工厂方法创建特定账户类型的实例
const zhiChengApi = XingyanAPI.createZhiChengInstance(
  'https://open-api.marscrm.cn/openApi',
  ****************,
  '733d96254487afbbb8892cdfd3b82e61'
)

const zhongShenTongApi = XingyanAPI.createZhongShenTongInstance(
  'https://open-api.marscrm.cn/openApi',
  ****************,
  '0dd9889bb44646c072a0bb8d44002d9b'
)
```

## 使用示例

### 基本用法（向后兼容）

```typescript
// 静态方法调用（保持向后兼容）
const groupInfo = await XingyanAPI.getRoomGroupInfo('25/7/22中神通')
const watchTime = await XingyanAPI.getLiveStreamWatchTime('********', '***********')
```

### 新的实例化用法

```typescript
// 创建特定账户的 API 实例
const zhiChengApi = XingyanAPI.createZhiChengInstance(
  'https://open-api.marscrm.cn/openApi',
  ****************,
  '733d96254487afbbb8892cdfd3b82e61'
)

// 使用实例方法
const groupInfo = await zhiChengApi.getRoomGroupInfo('25/7/22智诚')
const watchTime = await zhiChengApi.getLiveStreamWatchTime('********', '***********')
```

### 动态账户切换

```typescript
// 根据业务逻辑动态选择账户配置
function createXingyanAPI(accountType: YuHeAccountType): XingyanAPI {
  const configs = {
    [YuHeAccountType.ZhiCheng]: {
      accountType: YuHeAccountType.ZhiCheng,
      baseUrl: 'https://open-api.marscrm.cn/openApi',
      cropId: ****************,
      secretKey: '733d96254487afbbb8892cdfd3b82e61'
    },
    [YuHeAccountType.ZhongShenTong]: {
      accountType: YuHeAccountType.ZhongShenTong,
      baseUrl: 'https://open-api.marscrm.cn/openApi',
      cropId: ****************,
      secretKey: '0dd9889bb44646c072a0bb8d44002d9b'
    }
  }
  
  return new XingyanAPI(configs[accountType])
}

// 使用
const api = createXingyanAPI(YuHeAccountType.ZhiCheng)
const result = await api.getRoomGroupInfo('测试分组')
```

## 向后兼容性

所有原有的静态方法调用方式都保持不变，确保现有代码无需修改即可正常运行。静态方法内部会创建默认配置的实例来执行操作。

## 迁移建议

1. **新项目**：建议使用实例化的方式，通过依赖注入传入配置
2. **现有项目**：可以继续使用静态方法，也可以逐步迁移到实例化方式
3. **多账户场景**：强烈建议使用实例化方式，可以同时管理多个不同的账户配置

## 优势

1. **灵活性**：支持运行时动态配置账户信息
2. **可测试性**：便于单元测试时注入模拟配置
3. **多账户支持**：可以同时操作多个不同的星炎云账户
4. **向后兼容**：现有代码无需修改
5. **类型安全**：通过 TypeScript 接口确保配置的正确性
