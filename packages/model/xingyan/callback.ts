import {
  IOrderCloseParam,
  IProductClickParam,
  IProductPurchaseParam,
  IUserEnterRoomParam,
  IUserLeaveRoomParam,
  IXingyanPushCallback,
  OrderCloseCallback,
  ProductClickCallback,
  ProductPurchaseCallback,
  UserEnterRoomCallback,
  UserLeaveRoomCallback,
  XingyanPushType
} from './type'

/**
 * 星炎云回调处理类
 * 用于处理星炎云推送的各种回调
 */
export class XingyanCallback {
  /**
   * 解析回调数据
   * @param body 回调请求体
   * @returns 解析后的回调数据
   */
  public static parseCallback(body: any): IXingyanPushCallback<any> {
    if (!body || typeof body !== 'object') {
      throw new Error('无效的回调数据')
    }

    if (typeof body.type !== 'number') {
      throw new Error('无效的回调类型')
    }

    return body as IXingyanPushCallback<any>
  }

  /**
   * 处理学员进入房间回调
   * @param callback 回调数据
   */
  public static handleUserEnterRoom(callback: UserEnterRoomCallback): IUserEnterRoomParam {
    this.validateCallbackType(callback, XingyanPushType.USER_ENTER_ROOM)
    return callback.param
  }

  /**
   * 处理学员离开房间回调
   * @param callback 回调数据
   */
  public static handleUserLeaveRoom(callback: UserLeaveRoomCallback): IUserLeaveRoomParam {
    this.validateCallbackType(callback, XingyanPushType.USER_LEAVE_ROOM)
    return callback.param
  }

  /**
   * 处理商品购买回调
   * @param callback 回调数据
   */
  public static handleProductPurchase(callback: ProductPurchaseCallback): IProductPurchaseParam {
    this.validateCallbackType(callback, XingyanPushType.PRODUCT_PURCHASE)
    return callback.param
  }

  /**
   * 处理商品点击回调
   * @param callback 回调数据
   */
  public static handleProductClick(callback: ProductClickCallback): IProductClickParam {
    this.validateCallbackType(callback, XingyanPushType.PRODUCT_CLICK)
    return callback.param
  }

  /**
   * 处理关闭订单回调
   * @param callback 回调数据
   */
  public static handleOrderClose(callback: OrderCloseCallback): IOrderCloseParam {
    this.validateCallbackType(callback, XingyanPushType.ORDER_CLOSE)
    return callback.param
  }

  /**
   * 根据回调类型处理回调
   * @param callback 回调数据
   * @returns 处理结果
   */
  public static handleCallback(callback: IXingyanPushCallback<any>): any {
    switch (callback.type) {
      case XingyanPushType.USER_ENTER_ROOM:
        return this.handleUserEnterRoom(callback as UserEnterRoomCallback)
      case XingyanPushType.USER_LEAVE_ROOM:
        return this.handleUserLeaveRoom(callback as UserLeaveRoomCallback)
      case XingyanPushType.PRODUCT_CLICK:
        return this.handleProductClick(callback as ProductClickCallback)
      case XingyanPushType.PRODUCT_PURCHASE:
        return this.handleProductPurchase(callback as ProductPurchaseCallback)
      case XingyanPushType.ORDER_CLOSE:
        return this.handleOrderClose(callback as OrderCloseCallback)
      default:
        throw new Error(`不支持的回调类型: ${callback.type}`)
    }
  }

  /**
   * 验证回调类型
   * @param callback 回调数据
   * @param expectedType 期望的类型
   */
  private static validateCallbackType(callback: IXingyanPushCallback<any>, expectedType: XingyanPushType): void {
    if (callback.type !== expectedType) {
      throw new Error(`回调类型不匹配，期望 ${expectedType}，实际 ${callback.type}`)
    }
  }
}
