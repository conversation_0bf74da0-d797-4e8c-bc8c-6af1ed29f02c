import { XingyanClient } from './client'
import {
  IAddImportUserRequest,
  IGetRoomGroupInfoRequest,
  IGetRoomWatchTimeRequest,
  IGetRoomDemandTimeRequest,
  IGetRoomMsgPageRequest,
  IRoomGroupInfo,
  IWhitelistUser,
  IGetOrderInfoPageRequest,
  OrderStatus,
  IOrderInfoPage,
  IRoomMessage,
  IGetImportUserPageRequest,
  ISimplifiedWhitelistUser,
  IDeleteImportUserRequest,
  IXingyanAccountConfig
} from './type'
import { UUID } from '../../lib/uuid/uuid'
import { Config, YuHeAccountType } from 'config'

interface IRoomStatus {
  roomStatus: number
  onlineUserNum: number
}

/**
 * 星炎云 API 封装
 * 提供星炎云直播相关接口的调用方法
 */
export class XingyanAPI {
  private client: XingyanClient

  constructor(accountConfig?: IXingyanAccountConfig) {
    this.client = new XingyanClient(accountConfig)
  }

  /**
   * 获取直播间分组信息
   * @param groupName 分组名称，可选
   * @returns 直播间分组信息
   */
  public async getRoomGroupInfo(groupName: string): Promise<IRoomGroupInfo | null> {
    try {
      const params: IGetRoomGroupInfoRequest = {
        groupName
      }

      const response = await this.client.post<IRoomGroupInfo>('/group/getRoomGroupInfo', params)

      if (response.data.code === 200) {
        return response.data.data
      }

      console.error('获取直播间分组信息失败:', response.data.msg)
      return null
    } catch (error) {
      console.error('获取直播间分组信息异常:', error)
      return null
    }
  }

  /**
   * 获取直播间分组信息（静态方法，向后兼容）
   * @param groupName 分组名称，可选
   * @returns 直播间分组信息
   * @deprecated 建议使用实例方法
   */
  public static async getRoomGroupInfo(groupName: string): Promise<IRoomGroupInfo | null> {
    return await new XingyanAPI().getRoomGroupInfo(groupName)
  }

  /**
   * 获取直播观看时长
   * @param roomId 直播间ID
   * @param mobile 手机号（与userId二选一）
   * @param userId 客户ID（与mobile二选一）
   * @returns 直播观看时长（秒）
   */
  public async getLiveStreamWatchTime (roomId: string, mobile?: string, userId?: number): Promise<number | null> {
    try {
      if (!mobile && !userId) {
        throw new Error('手机号和客户ID至少需要提供一个')
      }

      const params: IGetRoomWatchTimeRequest = {
        roomId,
        mobile,
        userId
      }

      const response = await this.client.post<number>('/user/roomWatchTime', params)

      if (response.data.code === 200) {
        return response.data.data
      }

      console.error('获取直播观看时长失败:', response.data.msg)
      return null
    } catch (error) {
      console.error('获取直播观看时长异常:', error)
      throw error
    }
  }

  /**
   * 获取直播观看时长（静态方法，向后兼容）
   * @param roomId 直播间ID
   * @param mobile 手机号（与userId二选一）
   * @param userId 客户ID（与mobile二选一）
   * @returns 直播观看时长（秒）
   *
   */
  public static async getLiveStreamWatchTime (roomId: string, mobile?: string, userId?: number): Promise<number | null> {
    return await new XingyanAPI().getLiveStreamWatchTime(roomId, mobile, userId)
  }

  /**
   * 获取回放观看时长
   * @param roomId 直播间ID
   * @param mobile 手机号（与userId二选一）
   * @param userId 客户ID（与mobile二选一）
   * @returns 回放观看时长（秒）
   */
  public async getRoomRecordingWatchingTime(roomId: string, mobile?: string, userId?: number): Promise<number | null> {
    try {
      if (!mobile && !userId) {
        throw new Error('手机号和客户ID至少需要提供一个')
      }

      const params: IGetRoomDemandTimeRequest = {
        roomId,
        mobile,
        userId
      }

      const response = await this.client.post<number>('/user/roomDemandTime', params)

      if (response.data.code === 200) {
        return response.data.data
      }

      console.error('获取回放观看时长失败:', response.data.msg)
      return null
    } catch (error) {
      console.error('获取回放观看时长异常:', error)
      throw error
    }
  }

  /**
   * 获取回放观看时长（静态方法，向后兼容）
   * @param roomId 直播间ID
   * @param mobile 手机号（与userId二选一）
   * @param userId 客户ID（与mobile二选一）
   * @returns 回放观看时长（秒）
   */
  public static async getRoomRecordingWatchingTime(roomId: string, mobile?: string, userId?: number): Promise<number | null> {
    return await new XingyanAPI().getRoomRecordingWatchingTime(roomId, mobile, userId)
  }

  /**
   * 获取学员发言记录
   * @param roomId 直播间ID
   * @param mobile 手机号（与userId二选一）
   * @param userId 客户ID（与mobile二选一）
   * @param pageNum 页码，默认1
   * @param pageSize 每页条数，默认10
   * @returns 学员发言记录分页结果
   */
  public async getRoomMsgPage(
    roomId: string,
    mobile?: string,
    userId?: number,
    pageNum: number = 1,
    pageSize: number = 100
  ): Promise<IRoomMessage[] | null> {
    try {
      if (!mobile && !userId) {
        throw new Error('手机号和客户ID至少需要提供一个')
      }

      const params: IGetRoomMsgPageRequest = {
        roomId,
        mobile,
        userId,
        pageNum,
        pageSize
      }

      const response = await this.client.post('/user/roomMsgPage', params)

      if (response.data.code === 200) {
        return response.data.data as IRoomMessage[]
      }

      console.error('获取学员发言记录失败:', response.data.msg)
      return null
    } catch (error) {
      console.error('获取学员发言记录异常:', error)
      throw error
    }
  }

  /**
   * 获取学员发言记录（静态方法，向后兼容）
   * @param roomId 直播间ID
   * @param mobile 手机号（与userId二选一）
   * @param userId 客户ID（与mobile二选一）
   * @param pageNum 页码，默认1
   * @param pageSize 每页条数，默认10
   * @returns 学员发言记录分页结果
   */
  public static async getRoomMsgPage(
    roomId: string,
    mobile?: string,
    userId?: number,
    pageNum: number = 1,
    pageSize: number = 100
  ): Promise<IRoomMessage[] | null> {
    return await new XingyanAPI().getRoomMsgPage(roomId, mobile, userId, pageNum, pageSize)
  }

  /**
   * 分批导入客户到直播间白名单，每批最多 batchSize 个
   * @param {string} roomId  直播间 ID
   * @param {string[]} users 全部待导入客户数组
   */
  public async importUsersInBatches(roomId: number, users: IWhitelistUser[]) {
    const batchSize = 100

    for (let i = 0; i < users.length; i += batchSize) {
      const batch = users.slice(i, i + batchSize)
      try {
        const res = await this.addImportUser(roomId, batch)

        if (!res) {
          return false
        }
      } catch (e) {
        return false
      }
    }
  }

  /**
   * 新增白名单客户
   * @param roomId 直播间ID
   * @param users 白名单客户列表，单次不超过100个
   * @returns 是否添加成功
   */
  public async addImportUser(roomId: number, users: IWhitelistUser[]): Promise<boolean> {
    try {
      if (!roomId) {
        throw new Error('直播间ID不能为空')
      }

      if (!users || users.length === 0) {
        throw new Error('白名单客户列表不能为空')
      }

      if (users.length > 100) {
        throw new Error('白名单客户列表数量不能超过100个')
      }

      const params: IAddImportUserRequest = {
        roomId,
        accountList: users.filter((user) => user.account.length === 11).map((user) => ({
          account: user.account,
          name: user.name.slice(0, 15) === '' ? UUID.short().slice(0, 15) : user.name.slice(0, 15) // name 字段长度限制为20
        }))
      }

      const response = await this.client.post<boolean>('/addImportUser', params)

      if (response.data.code === 200) {
        return response.data.data
      }

      console.error('新增白名单客户失败:', response.data.msg)
      return false
    } catch (error) {
      console.error('新增白名单客户异常:', error)
      throw error
    }
  }

  /**
   * 新增白名单客户（静态方法，向后兼容）
   * @param roomId 直播间ID
   * @param users 白名单客户列表，单次不超过100个
   * @returns 是否添加成功
   * @deprecated 建议使用实例方法
   */
  public static async addImportUser(roomId: number, users: IWhitelistUser[]): Promise<boolean> {
    return await new XingyanAPI().addImportUser(roomId, users)
  }

  /**
   * 获取订单列表
   * 简化版接口，只需要提供直播间ID、手机号和订单状态（默认为已支付）
   * @param roomId 直播间ID
   * @param mobile 手机号
   * @param pageNum 页码，默认1
   * @param pageSize 每页条数，默认20
   * @returns 订单列表分页结果
   */
  public async getOrderInfoPage(
    mobile: string,
    pageNum: number = 1,
    pageSize: number = 100,
    roomId = undefined,
  ): Promise<IOrderInfoPage | null> {
    try {
      if (!mobile) {
        throw new Error('手机号不能为空')
      }

      const params: IGetOrderInfoPageRequest = {
        roomId,
        mobile,
        payStatus: OrderStatus.PAID, // 默认查询已支付订单
        pageNum,
        pageSize
      }

      const response = await this.client.post<IOrderInfoPage>('/getOrderInfoPage', params)

      if (response.data.code === 200) {
        return response.data.data
      }

      console.error('获取订单列表失败:', response.data.msg)
      return null
    } catch (error) {
      console.error('获取订单列表异常:', error)
      throw error
    }
  }

  /**
   * 获取订单列表（静态方法，向后兼容）
   * 简化版接口，只需要提供直播间ID、手机号和订单状态（默认为已支付）
   * @param roomId 直播间ID
   * @param mobile 手机号
   * @param pageNum 页码，默认1
   * @param pageSize 每页条数，默认20
   * @returns 订单列表分页结果
   * @deprecated 建议使用实例方法
   */
  public static async getOrderInfoPage(
    mobile: string,
    pageNum: number = 1,
    pageSize: number = 100,
    roomId = undefined,
  ): Promise<IOrderInfoPage | null> {
    return await new XingyanAPI().getOrderInfoPage(mobile, pageNum, pageSize, roomId)
  }

  public async getAllWhiteListUsers(roomId: number) {
    const pageSize = 100
    let pageNum = 1
    let total = 0
    let allUsers: ISimplifiedWhitelistUser[] = []

    do {
      const params = {
        roomId,
        pageNum,
        pageSize,
      }

      const { data: response } = await this.client.post<ISimplifiedWhitelistUser[]>(
        '/getImportUserPage',
        params
      )

      if (response.code !== 200) {
        console.error('获取白名单客户列表失败:', roomId, response.msg)
        break
      }

      if (Array.isArray(response.data)) {
        allUsers = allUsers.concat(response.data)
      } else if (response.data) {
        // 兼容接口返回单个对象的情况
        allUsers.push(response.data as ISimplifiedWhitelistUser)
      }

      total = response.total || 0
      pageNum++
    } while (allUsers.length < total)

    return allUsers
  }


  /**
   * 获取白名单客户列表
   * @param roomId 直播间ID（必填）
   * @param account 白名单手机号（可选）
   * @param name 白名单名称（可选）
   * @param inRoomFlag 在房间标识（1-在房间中）（可选）
   * @param pageNum 页码，默认1
   * @param pageSize 每页条数，默认20，最大100
   * @returns 白名单客户列表
   */
  public async getImportUserPage(
    roomId: number,
    account?: string,
    name?: string,
    inRoomFlag?: number,
    pageNum: number = 1,
    pageSize: number = 100
  ): Promise<ISimplifiedWhitelistUser[] | null> {
    try {
      if (!roomId) {
        throw new Error('直播间ID不能为空')
      }

      // 限制页长最大值
      if (pageSize > 100) {
        pageSize = 100
      }

      const params: IGetImportUserPageRequest = {
        roomId,
        account,
        name,
        inRoomFlag,
        pageNum,
        pageSize
      }

      const response = await this.client.post<ISimplifiedWhitelistUser[]>('/getImportUserPage', params)

      if (response.data.code === 200) {
        return response.data.data
      }

      console.error('获取白名单客户列表失败:', response.data.msg)
      return null
    } catch (error) {
      console.error('获取白名单客户列表异常:', error)
      throw error
    }
  }

  /**
   * 获取白名单客户列表（静态方法，向后兼容）
   * @param roomId 直播间ID（必填）
   * @param account 白名单手机号（可选）
   * @param name 白名单名称（可选）
   * @param inRoomFlag 在房间标识（1-在房间中）（可选）
   * @param pageNum 页码，默认1
   * @param pageSize 每页条数，默认20，最大100
   * @returns 白名单客户列表
   * @deprecated 建议使用实例方法
   */
  public static async getImportUserPage(
    roomId: number,
    account?: string,
    name?: string,
    inRoomFlag?: number,
    pageNum: number = 1,
    pageSize: number = 100
  ): Promise<ISimplifiedWhitelistUser[] | null> {
    return await new XingyanAPI().getImportUserPage(roomId, account, name, inRoomFlag, pageNum, pageSize)
  }

  public async deleteImportUser(roomId:number, accountList:string[]):Promise<void> {
    const params:IDeleteImportUserRequest = {
      roomId,
      accountList
    }
    const response = await this.client.post<boolean>('/delImportUser', params)
    if (response.data.code !== 200) {
      throw ('删除白名单没成功')
    }
  }

  /**
   * 删除白名单客户（静态方法，向后兼容）
   * @param roomId 直播间ID
   * @param accountList 账户列表
   * @deprecated 建议使用实例方法
   */
  public static async deleteImportUser(roomId:number, accountList:string[]):Promise<void> {
    return await new XingyanAPI().deleteImportUser(roomId, accountList)
  }

  async getRoomStatus(roomId: number) {
    const response =  await this.client.post<IRoomStatus>('/getRoomStatus', {
      roomId
    })

    if (response.data.code !== 200) {
      throw ('获取直播间状态失败')
    }

    return response.data.data
  }

  /**
   * 获取直播间状态（静态方法，向后兼容）
   * @param roomId 直播间ID
   * @returns 直播间状态
   * @deprecated 建议使用实例方法
   */
  static async getRoomStatus(roomId: number) {
    return await new XingyanAPI().getRoomStatus(roomId)
  }

  static getXingYanClientByBotId(botId:string):XingyanAPI {
    const accountType = Config.getYuHeAccountType(botId)
    if (accountType == YuHeAccountType.ZhiCheng) {
      return new XingyanAPI(
        {
          accountType: YuHeAccountType.ZhiCheng,
          cropId: Config.setting.xingyan.zhicheng.cropId,
          secretKey: Config.setting.xingyan.zhicheng.secretKey
        }
      )
    } else {
      return new XingyanAPI()
    }
  }
}
