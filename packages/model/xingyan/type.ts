import { YuHeAccountType } from 'config'

/**
 * 星炎云 API 类型定义
 */

// 重新导出 YuHeAccountType
export { YuHeAccountType }


// 星炎云账户配置接口
export interface IXingyanAccountConfig {
  accountType: YuHeAccountType
  cropId: number
  secretKey: string
}

// 推送类型枚举
export enum XingyanPushType {
  USER_ENTER_ROOM = 1,       // 学员进入房间
  USER_LEAVE_ROOM = 2,       // 学员离开房间
  PRODUCT_CLICK = 3,         // 商品点击
  PRODUCT_PURCHASE = 4,      // 商品购买
  WHITELIST_ADD = 5,         // 白名单新增
  WHITELIST_MODIFY = 6,      // 白名单修改
  WHITELIST_DELETE = 7,      // 白名单删除
  WATCH_TIME = 8,            // 观看时长推送
  LIVE_ROOM_STATUS_CHANGE = 9, // 直播状态变更（直播结束）
  ORDER_CLOSE = 10           // 关闭订单推送
}

// 通用响应结构
export interface IXingyanResponse<T> {
  code: number
  msg: string
  total: number
  data: T
  obj: any
  traceId: string
}

// 获取 Token 请求参数
export interface IGetTokenRequest {
  cropId: number
  secretKey: string
}

// 获取 Token 响应数据
export interface IGetTokenData {
  token: string
  expiresIn: number
}

// 直播间信息
export interface ILiveRoom {
  roomId: number
  roomName: string
  watchUrl: string
  shortUrl: string
  recordCode: string
  startTime: string
  liveStartTime: string
  liveEndTime: string
  authType: number // 客户观看类型 0-无限制 1-密码登录 2-手机验证观看 3-微信授权观看【废弃】 4-参数验证观看 5-报名观看
}

// 直播间分组信息
export interface IRoomGroupInfo {
  groupName: string
  groupId: number
  detailList: ILiveRoom[]
}

// 获取直播间分组请求参数
export interface IGetRoomGroupInfoRequest {
  groupName: string
}

// 客户标识参数（可以是手机号或客户ID）
export interface IUserIdentifier {
  roomId: string
  mobile?: string
  userId?: number
}

// 获取直播观看时长请求参数
export type IGetRoomWatchTimeRequest = IUserIdentifier

// 获取回放观看时长请求参数
export type IGetRoomDemandTimeRequest = IUserIdentifier

// 获取学员发言记录请求参数
export interface IGetRoomMsgPageRequest extends IUserIdentifier {
  pageNum: number
  pageSize: number
}

// 白名单客户信息
export interface IWhitelistUser {
  account: string
  name: string
}

// 新增白名单客户请求参数
export interface IAddImportUserRequest {
  roomId: number
  accountList: IWhitelistUser[]
}

// 学员发言记录项
export interface IRoomMessage {
  roomId: number
  msgTime: string
  content: string
}

// 学员发言记录分页结果
export interface IRoomMessagePage {
  list: IRoomMessage[]
  pageNum: number
  pageSize: number
  total: number
}

// 推送回调通用结构
export interface IXingyanPushCallback<T> {
  type: XingyanPushType
  param: T
  sign: string
  pushTime: number
  roomId: number
}

// 学员进入房间推送参数
export interface IUserEnterRoomParam {
  userId: number
  openId: string
  unionId: string
  mobile: string
  headImgUrl: string
  nickname: string
  roomId: number
  loginTime: string
  channelId: number
  channelName: string
  ip: string
  browserType: string
  account: string
}

// 学员离开房间推送参数
export interface IUserLeaveRoomParam {
  userId: number
  roomId: number
  logoutTime: string
  watchTime: number
  nickname: string
  account: string
  mobile: string
}

// 商品购买推送参数
export interface IProductPurchaseParam {
  roomId: number
  roomName: string
  productId: number
  productName: string
  userId: number
  payMethod: number
  payAmt: string
  payTime: string
  nickname: string
  account: string
  mobile: string
  transactionId: string
  orderPhone: string
}

// 直播状态变更推送参数
export interface ILiveRoomStatusChangeParam {
  roomId: number
  changeTime: string
  status: number
}

// 关闭订单推送参数
export interface IOrderCloseParam {
  roomId: number
  roomName: string
  productId: number
  productName: string
  userId: number
  payMethod: number
  payAmt: string
  cancelTime: string
  nickname: string
  account: string
  mobile: string
}

// 商品点击推送参数
export interface IProductClickParam {
  roomId: number
  productId: number
  userId: number
  nickname: string
  account: string
  mobile: string
}

// 类型化的推送回调
export type UserEnterRoomCallback = IXingyanPushCallback<IUserEnterRoomParam>
export type UserLeaveRoomCallback = IXingyanPushCallback<IUserLeaveRoomParam>
export type ProductClickCallback = IXingyanPushCallback<IProductClickParam>
export type ProductPurchaseCallback = IXingyanPushCallback<IProductPurchaseParam>
export type OrderCloseCallback = IXingyanPushCallback<IOrderCloseParam>
export type LiveRoomStatusChangeCallback = IXingyanPushCallback<ILiveRoomStatusChangeParam>

// 订单状态枚举
export enum OrderStatus {
  PENDING = 0,      // 待支付
  PAID = 1,         // 支付成功
  CANCELLED = 2,    // 支付取消
  FAILED = -1,      // 支付失败
  REFUNDED = 3      // 退款
}

// 付款渠道枚举
export enum PaymentChannel {
  WECHAT = 1,       // 微信
  ALIPAY = 2        // 支付宝
}

// 获取订单列表请求参数
export interface IGetOrderInfoPageRequest {
  roomId?: number            // 直播间id
  orderId?: number          // 订单id
  nickname?: string         // 昵称
  mobile?: string           // 手机号
  payStatus?: OrderStatus   // 订单状态
  startDate?: string        // 开始时间
  endDate?: string          // 结束时间
  pageNum: number           // 页码
  pageSize: number          // 页长
}

// 订单信息
export interface IOrderInfo {
  orderId: number           // 订单id
  roomId: number            // 直播间id
  productId: number         // 商品id
  productName: string       // 商品名称
  payAmount: string         // 支付金额
  productNum: number        // 商品数量
  mobile: string            // 手机号
  nickname: string          // 昵称
  contactInfo: string       // 留资信息
  payType: PaymentChannel   // 付款渠道
  payStatus: OrderStatus    // 订单状态
  payTime: string           // 支付时间
  createAt: string          // 创建时间
}

// 订单列表分页结果
export interface IOrderInfoPage {
  list: IOrderInfo[]
  pageNum: number
  pageSize: number
  total: number
}

// 获取白名单客户列表请求参数
export interface IGetImportUserPageRequest {
  roomId: number            // 直播间id（必填）
  account?: string          // 白名单手机号
  name?: string             // 白名单名称
  inRoomFlag?: number       // 在房间标识（1-在房间中）
  pageNum: number           // 页码（必填）
  pageSize: number          // 页长，max=100（必填）
}

// 简化的白名单客户信息
export interface ISimplifiedWhitelistUser {
  roomId: number | null     // 直播间ID，可能为 null
  account: string           // 手机号
  name: string              // 客户名称
  createTime: string | null // 创建时间，可能为 null
}

// 白名单客户详细信息
export interface IWhitelistUserDetail extends IWhitelistUser {
  id: number                // 白名单ID
  roomId: number            // 直播间ID
  inRoomFlag: number        // 是否在房间中（1-在房间中）
  createTime: string        // 创建时间
  updateTime: string        // 更新时间
}

// 白名单客户列表分页结果
export interface IWhitelistUserPage {
  list: IWhitelistUserDetail[]
  pageNum: number
  pageSize: number
  total: number
}

export interface IDeleteImportUserRequest {
  roomId: number,
  accountList: string[]
}
