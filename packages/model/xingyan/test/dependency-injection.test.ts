import { XingyanClient } from '../client'
import { XingyanAPI } from '../api'
import { YuHeAccountType, IXingyanAccountConfig } from '../type'

describe('XingyanClient 依赖注入测试', () => {
  const mockAccountConfig: IXingyanAccountConfig = {
    accountType: YuHeAccountType.ZhiCheng,
    cropId: *********,
    secretKey: 'test-secret-key'
  }

  describe('XingyanClient', () => {
    it('应该支持默认配置（向后兼容）', () => {
      const client = new XingyanClient()
      expect(client).toBeInstanceOf(XingyanClient)
    })

    it('应该支持外部注入配置', () => {
      const client = new XingyanClient(mockAccountConfig)
      expect(client).toBeInstanceOf(XingyanClient)
    })

    it('应该根据注入的配置设置正确的 tokenCacheKey', () => {
      // 测试智诚账户
      const zhiChengConfig: IXingyanAccountConfig = {
        ...mockAccountConfig,
        accountType: YuHeAccountType.ZhiCheng
      }
      const zhiChengClient = new XingyanClient(zhiChengConfig)
      expect(zhiChengClient).toBeInstanceOf(XingyanClient)

      // 测试中神通账户
      const zhongShenTongConfig: IXingyanAccountConfig = {
        ...mockAccountConfig,
        accountType: YuHeAccountType.ZhongShenTong
      }
      const zhongShenTongClient = new XingyanClient(zhongShenTongConfig)
      expect(zhongShenTongClient).toBeInstanceOf(XingyanClient)
    })
  })

  describe('XingyanAPI', () => {
    it('应该支持默认配置（向后兼容）', () => {
      const api = new XingyanAPI()
      expect(api).toBeInstanceOf(XingyanAPI)
    })

    it('应该支持外部注入配置', () => {
      const api = new XingyanAPI(mockAccountConfig)
      expect(api).toBeInstanceOf(XingyanAPI)
    })
  })

  describe('向后兼容性', () => {
    it('静态方法应该继续工作', async () => {
      // 这些方法应该不会抛出错误（虽然可能因为网络问题失败）
      expect(typeof XingyanAPI.getRoomGroupInfo).toBe('function')
      expect(typeof XingyanAPI.getLiveStreamWatchTime).toBe('function')
      expect(typeof XingyanAPI.getRoomRecordingWatchingTime).toBe('function')
      expect(typeof XingyanAPI.getRoomMsgPage).toBe('function')
      expect(typeof XingyanAPI.addImportUser).toBe('function')
      expect(typeof XingyanAPI.getOrderInfoPage).toBe('function')
      expect(typeof XingyanAPI.getImportUserPage).toBe('function')
      expect(typeof XingyanAPI.deleteImportUser).toBe('function')
      expect(typeof XingyanAPI.getRoomStatus).toBe('function')
    })

    it('实例方法应该存在', () => {
      const api = new XingyanAPI()

      expect(typeof api.getRoomGroupInfo).toBe('function')
      expect(typeof api.getLiveStreamWatchTime).toBe('function')
      // expect(typeof api.getRoomRecordingWatchingTime).toBe('function')
      expect(typeof api.getRoomMsgPage).toBe('function')
      expect(typeof api.addImportUser).toBe('function')
      expect(typeof api.getOrderInfoPage).toBe('function')
      expect(typeof api.getImportUserPage).toBe('function')
      expect(typeof api.deleteImportUser).toBe('function')
      expect(typeof api.getRoomStatus).toBe('function')
    })
  })

  describe('类型安全性', () => {
    it('应该正确导出 YuHeAccountType 枚举', () => {
      expect(YuHeAccountType.ZhiCheng).toBe(1)
      expect(YuHeAccountType.ZhongShenTong).toBe(0)
    })
  })
})
