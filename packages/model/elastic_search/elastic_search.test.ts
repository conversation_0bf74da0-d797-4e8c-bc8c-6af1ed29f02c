// import ElasticSearchService from './elastic_search'
// import { MemoryStore } from '../../service/moer/components/memory/memory_store'
//
// describe ('elasticSearch embedding search', () => {
//   it('embedding阈值', async () => {
//     try{
//       const query =  '如何解决合作中的冲突'
//       const filter = [
//         {
//           operator: 'match',
//           field: 'doc',
//           value: '0221第三课 禅宗行走',
//         },
//       ]
//       const results = await ElasticSearchService.embeddingSearch('moer_test', query, 10, 0.6, filter)
//       console.log('results', results)
//       // const filteredResults = results.filter(([_, score]) => score > 0.6)
//       // console.log('filteredResults', filteredResults)
//     } catch (error) {
//       console.error('Error in embedding search:', error)
//     }
//   }, 30000)
//
//   it('search', async () => {
//     const results = await MemoryStore.getMemoriesByChatId('user_memory', '7881300804965673_1688856322643146')
//     console.log(JSON.stringify(results, null, 4))
//   }, 60000)
// })