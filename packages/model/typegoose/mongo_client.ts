import 'reflect-metadata'
import { getMongoConnection, closeMongoConnections } from './connection/mongo_connection'
import { getModelForClass } from '@typegoose/typegoose'
import { Chat, ChatMoerOverseas } from './models/chat'
import type { FilterQuery, UpdateQuery } from 'mongoose'

/**
 * 将 Prisma 样式 args 翻译为 Mongoose 查询
 * 支持 where / select / orderBy / skip / take / include 等常用参数
 */
function translateArgs(args: any = {}) {
  const { where = {}, select, orderBy, skip, take, include } = args

  const query: any = { filter: where }

  // 处理 select/projection
  if (select) {
    // Prisma select 格式: { field1: true, field2: true }
    // Mongoose projection 格式: { field1: 1, field2: 1 } 或 'field1 field2'
    if (typeof select === 'object') {
      const projection: any = {}
      Object.keys(select).forEach((key) => {
        if (select[key] === true) {
          projection[key] = 1
        }
      })
      query.projection = projection
    } else {
      query.projection = select
    }
  }

  // 处理 orderBy
  if (orderBy) {
    // Prisma orderBy 格式: { field: 'asc' | 'desc' } 或 [{ field: 'asc' }]
    // Mongoose sort 格式: { field: 1 | -1 } 或 'field -field'
    if (Array.isArray(orderBy)) {
      const sort: any = {}
      orderBy.forEach((item) => {
        Object.keys(item).forEach((key) => {
          sort[key] = item[key] === 'desc' ? -1 : 1
        })
      })
      query.sort = sort
    } else if (typeof orderBy === 'object') {
      const sort: any = {}
      Object.keys(orderBy).forEach((key) => {
        sort[key] = orderBy[key] === 'desc' ? -1 : 1
      })
      query.sort = sort
    } else {
      query.sort = orderBy
    }
  }

  if (skip !== undefined) query.skip = skip
  if (take !== undefined) query.limit = take

  // include 暂时不处理，需要根据具体关联关系实现
  if (include) {
    console.warn('[TypeGooseMongoClient] include parameter not yet implemented')
  }

  return query
}

/**
 * Prisma-like 适配器，提供与 Prisma 兼容的 API
 */
class PrismaLikeAdapter<T = any> {
  constructor(private model: any) {}

  async findMany(args?: any): Promise<T[]> {
    const q = translateArgs(args)
    let query = this.model.find(q.filter, q.projection)

    if (q.sort) query = query.sort(q.sort)
    if (q.skip) query = query.skip(q.skip)
    if (q.limit) query = query.limit(q.limit)

    return query.exec()
  }

  async findFirst(args?: any): Promise<T | null> {
    const q = translateArgs(args)
    return this.model.findOne(q.filter, q.projection).sort(q.sort).exec()
  }

  async findUnique(args: { where: FilterQuery<T> }): Promise<T | null> {
    return this.model.findOne(args.where).exec()
  }

  async create(args: { data: Partial<T> }): Promise<T> {
    return this.model.create(args.data)
  }

  async createMany(args: { data: Partial<T>[] }) {
    return this.model.insertMany(args.data)
  }

  async delete(args: { where: FilterQuery<T> }): Promise<T | null> {
    return this.model.findOneAndDelete(args.where).exec()
  }

  async update(args: { where: FilterQuery<T>; data: any }): Promise<T | null> {
    // 处理 Prisma 特有的数组操作，如 { round_ids: { push: "value" } }
    const processedData = this.processPrismaUpdateData(args.data)
    return this.model.findOneAndUpdate(args.where, processedData, { new: true }).exec()
  }

  /**
   * 处理 Prisma 风格的更新数据，转换为 Mongoose 格式
   */
  private processPrismaUpdateData(data: any): any {
    const processed: any = {}

    for (const [key, value] of Object.entries(data)) {
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        // 检查是否是 Prisma 的特殊操作
        if ('push' in value) {
          processed['$push'] = processed['$push'] || {}
          processed['$push'][key] = (value as any).push
        } else if ('set' in value) {
          processed[key] = (value as any).set
        } else if ('increment' in value) {
          processed['$inc'] = processed['$inc'] || {}
          processed['$inc'][key] = (value as any).increment
        } else {
          // 普通嵌套对象
          processed[key] = value
        }
      } else {
        // 普通字段
        processed[key] = value
      }
    }

    return processed
  }

  async deleteMany(args?: { where?: FilterQuery<T> }) {
    return this.model.deleteMany(args?.where ?? {}).exec()
  }

  async updateMany(args: { where?: FilterQuery<T>; data: UpdateQuery<T> }) {
    return this.model.updateMany(args.where ?? {}, args.data).exec()
  }

  async upsert(args: { where: FilterQuery<T>; update: UpdateQuery<T>; create: Partial<T> }) {
    return this.model.findOneAndUpdate(args.where, args.update, {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true,
    }).exec()
  }

  async count(args?: { where?: FilterQuery<T> }): Promise<number> {
    return this.model.countDocuments(args?.where ?? {}).exec()
  }

  // 原样暴露 raw/aggregate 方法，兼容现有代码
  findRaw({ filter, options }: { filter: any; options?: any }) {
    if (options?.limit) {
      return this.model.collection.find(filter).limit(options.limit).toArray()
    }
    return this.model.collection.find(filter).toArray()
  }

  aggregateRaw({ pipeline }: { pipeline: any[] }) {
    return this.model.aggregate(pipeline).exec()
  }

  // 支持 Prisma 的数组操作
  async pushToArray(where: FilterQuery<T>, field: string, value: any) {
    const updateQuery: any = {}
    updateQuery[field] = { $push: value }
    return this.model.findOneAndUpdate(where, updateQuery, { new: true }).exec()
  }
}

// 懒加载模型实例
let chatModel: any = null
let chatMoerOverseasModel: any = null

/**
 * TypeGoose MongoDB 客户端，采用与 PrismaMongoClient 相同的单例模式
 * 使用方式: TypeGooseMongoClient.getInstance().chat.findMany()
 */
export class Mongo_client {
  private static instance: Mongo_client | undefined
  private static moerOverseasInstance: Mongo_client | undefined

  private chatAdapter: PrismaLikeAdapter<any> | null = null
  private dbName: string
  private isConnected: boolean = false

  private constructor(dbName: string = 'yuhe') {
    this.dbName = dbName
  }

  /**
   * 获取主数据库实例 (yuhe)
   */
  public static getInstance(dbName: string = 'yuhe'): Mongo_client {
    if (!Mongo_client.instance) {
      Mongo_client.instance = new Mongo_client(dbName)
    }
    return Mongo_client.instance
  }

  /**
   * 获取 Moer Overseas 数据库实例
   */
  public static getMoerOverseasInstance(): Mongo_client {
    if (!Mongo_client.moerOverseasInstance) {
      Mongo_client.moerOverseasInstance = new Mongo_client('moer_overseas')
    }
    return Mongo_client.moerOverseasInstance
  }

  /**
   * 确保数据库连接
   */
  private async ensureConnection(): Promise<void> {
    if (!this.isConnected) {
      try {
        await getMongoConnection(this.dbName)
        this.isConnected = true
        console.log(`[TypeGooseMongoClient] Connected to database: ${this.dbName}`)
      } catch (error) {
        console.error(`[TypeGooseMongoClient] Failed to connect to ${this.dbName}:`, error)
        throw error
      }
    }
  }

  /**
   * 获取 chat 模型适配器
   */
  public get chat(): PrismaLikeAdapter<any> {
    if (!this.chatAdapter) {
      // 触发连接建立
      this.ensureConnection().catch((err) => {
        console.error(`[TypeGooseMongoClient] Connection error for ${this.dbName}:`, err)
      })

      if (this.dbName === 'moer_overseas') {
        if (!chatMoerOverseasModel) {
          chatMoerOverseasModel = getModelForClass(ChatMoerOverseas)
        }
        this.chatAdapter = new PrismaLikeAdapter(chatMoerOverseasModel)
      } else {
        if (!chatModel) {
          chatModel = getModelForClass(Chat)
        }
        this.chatAdapter = new PrismaLikeAdapter(chatModel)
      }
    }
    return this.chatAdapter
  }

  /**
   * 优雅关闭连接
   */
  public static async disconnect(): Promise<void> {
    await closeMongoConnections()
    if (Mongo_client.instance) {
      Mongo_client.instance.isConnected = false
      Mongo_client.instance = undefined
    }
    if (Mongo_client.moerOverseasInstance) {
      Mongo_client.moerOverseasInstance.isConnected = false
      Mongo_client.moerOverseasInstance = undefined
    }
  }

  /**
   * 检查连接状态
   */
  public isConnectionReady(): boolean {
    return this.isConnected
  }
}

// 导出类型以便使用
export type ChatAdapter = PrismaLikeAdapter<any>
export type ChatMoerOverseasAdapter = PrismaLikeAdapter<any>
