import { prop, index, modelOptions } from '@typegoose/typegoose'
import { Types } from 'mongoose'
import { ChatState } from './chat_state'


@modelOptions({
  schemaOptions: {
    collection: 'chat_history',
    timestamps: false
  }
})

@index({ chat_id: 1, created_at: 1 }, { name: 'chat_id_1_created_at_1' })
@index({ message_id: 1 })
@index({ role: 1 })
export class Chat_history {
  @prop({ type: () => Types.ObjectId, default: () => new Types.ObjectId() })
  public _id!: Types.ObjectId

  @prop({ required: true })
  public chat_id!: string

  @prop({ required: true })
  public content!: string

  @prop({ required: true, type: Date })
  public created_at!: Date

  @prop({ required: true })
  public role!: string

  @prop()
  public is_send_by_human?: boolean

  @prop()
  public short_description?: string

  @prop()
  public round_id?: string

  @prop()
  public is_recalled?: boolean

  @prop()
  public message_id?: string

  @prop({ type: () => ChatState })
  public chat_state?: ChatState

  @prop()
  public sop_id?: string

  @prop()
  public state?: string
}

// Moer Overseas version with additional fields
@modelOptions({
  schemaOptions: {
    collection: 'chat_history',
    timestamps: false
  }
})
@index({ chat_id: 1, created_at: 1 }, { name: 'chat_id_1_created_at_1' })
@index({ message_id: 1 })
export class ChatHistoryMoerOverseas {
  @prop({ type: () => Types.ObjectId, default: () => new Types.ObjectId() })
  public _id!: Types.ObjectId

  @prop({ required: true })
  public chat_id!: string

  @prop({ required: true })
  public content!: string

  @prop({ required: true, type: Date })
  public created_at!: Date

  @prop({ required: true })
  public role!: string

  @prop({ type: () => Object })
  public api_content?: Record<string, any>

  @prop()
  public is_read?: boolean

  @prop()
  public is_send_by_human?: boolean

  @prop()
  public short_description?: string

  @prop()
  public round_id?: string

  @prop()
  public sop_id?: string

  @prop()
  public is_recalled?: boolean

  @prop()
  public message_id?: string

  @prop({ type: () => ChatState })
  public chat_state?: ChatState

  @prop()
  public state?: string
}
