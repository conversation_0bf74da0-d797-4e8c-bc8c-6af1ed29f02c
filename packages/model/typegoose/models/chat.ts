import 'reflect-metadata'
import { prop, index, modelOptions } from '@typegoose/typegoose'
import { ChatContact, ChatState } from './chat_state'


@modelOptions({
  schemaOptions: {
    collection: 'chat',
    timestamps: false
  }
})

@index({ phone: 1 })
@index({ wx_id: 1, course_no: 1 })
@index({ course_no: 1 })
@index({ 'contact.wx_name': 1 })
@index({ created_at: 1 })
export class Chat {
  @prop({ required: true })
  public _id!: string

  @prop({ required: true, type: () => ChatContact })
  public contact!: ChatContact

  @prop({ type: () => [String] })
  public round_ids!: string[]

  @prop({ required: true })
  public wx_id!: string

  @prop()
  public is_human_involved?: boolean

  @prop({ type: Date })
  public created_at?: Date

  @prop({ required: true, type: () => ChatState })
  public chat_state!: ChatState

  @prop()
  public course_no?: number

  @prop()
  public course_no_ori?: number

  @prop({ type: Date })
  public pay_time?: Date

  @prop()
  public is_deleted?: boolean

  @prop()
  public is_stop_group_push?: boolean

  @prop()
  public phone?: string

  @prop()
  public ip?: string
}

// Moer Overseas version with different fields
@modelOptions({
  schemaOptions: {
    collection: 'chat',
    timestamps: false
  }
})
@index({ moer_id: 1 })
@index({ phone_number: 1 })
export class ChatMoerOverseas {
  @prop({ required: true })
  public _id!: string

  @prop({ required: true, type: () => ChatContact })
  public contact!: ChatContact

  @prop({ type: () => [String] })
  public round_ids!: string[]

  @prop({ required: true })
  public wx_id!: string

  @prop()
  public is_human_involved?: boolean

  @prop({ type: Date })
  public created_at?: Date

  @prop({ required: true, type: () => ChatState })
  public chat_state!: ChatState

  @prop()
  public course_no?: number

  @prop()
  public is_deleted?: boolean

  @prop()
  public moer_id?: string

  @prop()
  public phone_number?: string

  @prop()
  public is_stop_group_push?: boolean

  @prop()
  public is_test?: boolean
}
