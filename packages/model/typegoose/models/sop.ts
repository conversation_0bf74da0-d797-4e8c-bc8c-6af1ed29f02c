import { prop, modelOptions } from '@typegoose/typegoose'
import { Types } from 'mongoose'

@modelOptions({
  schemaOptions: {
    collection: 'sop',
    timestamps: false
  }
})
export class Sop {
  @prop({ type: () => Types.ObjectId, default: () => new Types.ObjectId() })
  public _id!: Types.ObjectId

  @prop({ required: true })
  public title!: string

  @prop({ required: true })
  public week!: number

  @prop({ required: true })
  public day!: number

  @prop({ required: true })
  public time!: string

  // @prop({ type: () => [Situation] })
  // public situations!: Situation[]

  @prop({ required: true })
  public enable!: boolean

  @prop({ required: true })
  public tag!: string

  @prop({ required: true })
  public topic!: string
}

// Moer Overseas version with time_anchor
@modelOptions({
  schemaOptions: {
    collection: 'sop',
    timestamps: false
  }
})
export class SopMoerOverseas {
  @prop({ type: () => Types.ObjectId, default: () => new Types.ObjectId() })
  public _id!: Types.ObjectId

  @prop({ required: true })
  public time_anchor!: string

  @prop({ required: true })
  public title!: string

  @prop({ required: true })
  public week!: number

  @prop({ required: true })
  public day!: number

  @prop({ required: true })
  public time!: string

  // @prop({ type: () => [Situation] })
  // public situations!: Situation[]

  @prop({ required: true })
  public enable!: boolean

  @prop({ required: true })
  public tag!: string

  @prop({ required: true })
  public topic!: string
}
