import { prop, index, modelOptions } from '@typegoose/typegoose'
import { Types } from 'mongoose'

@modelOptions({
  schemaOptions: {
    collection: 'event_track',
    timestamps: false
  }
})
@index({ type: 1 })
@index({ type: 1, timestamp: 1 }, { name: 'type_1_timestamp_1' })
export class Event_track {
  @prop({ type: () => Types.ObjectId, default: () => new Types.ObjectId() })
  public _id!: Types.ObjectId

  @prop({ required: true })
  public chat_id!: string

  @prop({ required: true })
  public type!: string

  @prop({ type: () => Object })
  public meta?: Record<string, any>

  @prop({ required: true, type: Date })
  public timestamp!: Date
}
