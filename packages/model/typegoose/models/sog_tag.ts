import { prop, modelOptions } from '@typegoose/typegoose'
import { Types } from 'mongoose'

@modelOptions({
  schemaOptions: {
    collection: 'sop_tag',
    timestamps: false
  }
})
export class Sog_tag {
  @prop({ type: () => Types.ObjectId, default: () => new Types.ObjectId() })
  public _id!: Types.ObjectId

  @prop({ required: true })
  public name!: string

  @prop({ required: true })
  public enable!: boolean

  @prop({ type: () => [String] })
  public enable_account!: string[]
}
