import 'reflect-metadata'
import { prop, modelOptions, Severity } from '@typegoose/typegoose'

export class ChatContact {
  @prop({ required: true })
  public wx_id!: string

  @prop({ required: true })
  public wx_name!: string
}

@modelOptions({
  options: {
    allowMixed: Severity.ALLOW  // 允许 Mixed 类型，消除警告
  }
})

export class ChatState {
  @prop({ type: () => Object, default: {} })
  public nodeInvokeCount!: Record<string, any>

  @prop({ required: true })
  public nextStage!: string

  @prop({ type: () => Object, default: {} })
  public userSlots!: Record<string, any>

  @prop({ type: () => Object, default: {} })
  public state!: Record<string, any>
}
