import { prop, index, modelOptions } from '@typegoose/typegoose'
import { Types } from 'mongoose'

@modelOptions({
  schemaOptions: {
    collection: 'log_store',
    timestamps: false
  }
})
@index({ chat_id: 1, timestamp: 1 })
export class Log_store {
  @prop({ type: () => Types.ObjectId, default: () => new Types.ObjectId() })
  public _id!: Types.ObjectId

  @prop()
  public chat_id?: string

  @prop({ required: true })
  public level!: string

  @prop({ required: true, type: Date })
  public timestamp!: Date

  @prop({ required: true })
  public msg!: string
}
