/**
 * MongoInstance 功能测试
 *
 * 运行测试: npm test mongo-instance.test.ts
 */

import { Mongo_instance } from '../mongo_instance'
import { getMongoConnection, closeMongoConnections } from '../connection/mongo_connection'

describe('MongoInstance Chat Operations', () => {

  beforeAll(async () => {
    // 确保数据库连接
    await getMongoConnection()
  })

  afterAll(async () => {
    // 清理连接
    await closeMongoConnections()
  })

  describe('Basic CRUD Operations', () => {

    test('should count existing chats', async () => {
      const count = await Mongo_instance.chat.count()
      console.log(count)
    })

    test('should find chats with pagination', async () => {
      const chats = await Mongo_instance.chat.findMany({
        take: 5,
        orderBy: { created_at: 'desc' }
      })

      console.log(JSON.stringify(chats, null, 4))

      expect(Array.isArray(chats)).toBe(true)
      expect(chats.length).toBeLessThanOrEqual(5)
    })

    test('should find chat by id if exists', async () => {
      // 先获取一个存在的 chat
      const chats = await Mongo_instance.chat.findMany({ take: 1 })

      if (chats.length > 0) {
        const chatId = chats[0]._id
        const foundChat = await Mongo_instance.chat.findUnique({
          where: { _id: chatId }
        })

        expect(foundChat).toBeTruthy()
        expect(foundChat?._id).toBe(chatId)
      }
    })

    test('should handle complex where conditions', async () => {
      // const chats = await Mongo_instance.chat.findMany({
      //   where: {
      //     AND: [
      //       { course_no: { $exists: true } },
      //       { created_at: { $exists: true } }
      //     ]
      //   },
      //   take: 3
      // })
      //
      // expect(Array.isArray(chats)).toBe(true)
    })

    test('should support select/projection', async () => {
      const chats = await Mongo_instance.chat.findMany({
        select: {
          _id: true,
          contact: true,
          course_no: true
        },
        take: 2
      })

      if (chats.length > 0) {
        const chat = chats[0]
        expect(chat._id).toBeDefined()
        expect(chat.contact).toBeDefined()
        // 确保没有选择的字段不存在
        expect(chat.round_ids).toBeUndefined()
      }
    })
  })

  describe('Prisma-style Array Operations', () => {

    test('should handle push operation in update', async () => {
      // 这个测试需要一个实际的 chat 记录
      const chats = await Mongo_instance.chat.findMany({ take: 1 })

      if (chats.length > 0) {
        const chatId = chats[0]._id
        const newRoundId = `test-round-${Date.now()}`

        const updatedChat = await Mongo_instance.chat.update({
          where: { _id: chatId },
          data: {
            round_ids: { push: newRoundId }
          }
        })

        expect(updatedChat).toBeTruthy()
        expect(updatedChat?.round_ids).toContain(newRoundId)
      }
    })
  })

  describe('Raw Operations', () => {

    test('should support findRaw operation', async () => {
      const result = await Mongo_instance.chat.findRaw({
        filter: { course_no: { $exists: true } },
        options: { limit: 3 }
      })

      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBeLessThanOrEqual(3)
    })

    test('should support aggregateRaw operation', async () => {
      const pipeline = [
        { $match: { course_no: { $exists: true } } },
        { $group: { _id: '$course_no', count: { $sum: 1 } } },
        { $limit: 5 }
      ]

      const result = await Mongo_instance.chat.aggregateRaw({ pipeline })

      expect(Array.isArray(result)).toBe(true)
    })
  })

  describe('Error Handling', () => {

    test('should handle invalid model access', () => {
      expect(() => {
        // @ts-ignore - 故意访问不存在的模型
        Mongo_instance.nonExistentModel
      }).toThrow('Model "nonExistentModel" not registered')
    })

    test('should handle invalid query gracefully', async () => {
      try {
        await Mongo_instance.chat.findUnique({
          where: { _id: 'invalid-id-format' }
        })
      } catch (error) {
        // 应该能够处理无效的查询
        expect(error).toBeDefined()
      }
    })
  })
})

describe('MongoInstance MoerOverseas Operations', () => {

  test('should access moer overseas chat model', async () => {
    const count = await Mongo_instance.chatMoerOverseas.count()
    expect(typeof count).toBe('number')
    expect(count).toBeGreaterThanOrEqual(0)
  })

  test('should find moer overseas chats', async () => {
    const chats = await Mongo_instance.chatMoerOverseas.findMany({
      take: 3,
      orderBy: { created_at: 'desc' }
    })

    expect(Array.isArray(chats)).toBe(true)
    expect(chats.length).toBeLessThanOrEqual(3)
  })
})

/**
 * 集成测试 - 验证与现有 Prisma 代码的兼容性
 */
describe('Prisma Compatibility', () => {

  test('should work as drop-in replacement for Prisma', async () => {
    // // 模拟现有的 Prisma 调用模式
    // const prisma = Mongo_instance // 这就是迁移时需要做的唯一改变
    //
    // // 测试常见的 Prisma 操作
    // const count = await prisma.chat.count()
    // expect(typeof count).toBe('number')
    //
    // const chats = await prisma.chat.findMany({
    //   where: { course_no: { $exists: true } },
    //   orderBy: { created_at: 'desc' },
    //   take: 5
    // })
    //
    // expect(Array.isArray(chats)).toBe(true)
  })
})

/**
 * 性能测试
 */
describe('Performance Tests', () => {

  test('should handle concurrent requests', async () => {
    const promises = Array.from({ length: 10 }, () =>
      Mongo_instance.chat.count()
    )

    const results = await Promise.all(promises)

    expect(results).toHaveLength(10)
    results.forEach((count) => {
      expect(typeof count).toBe('number')
    })
  })

  it('1 ', async () => {
    const chats = await Mongo_instance.chat.findMany({ take: 2 })

    console.log(JSON.stringify(chats, null, 4))
  }, 30000)
})