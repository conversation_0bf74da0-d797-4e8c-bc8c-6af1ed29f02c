import 'reflect-metadata'
import { getMongoConnection } from './connection/mongo_connection'
import { getModelForClass } from '@typegoose/typegoose'
import { Chat, ChatMoerOverseas } from './models/chat'
/**
 * 将 Prisma 样式 args 翻译为 Mongoose 查询
 * 支持 where / select / orderBy / skip / take / include 等常用参数
 */
function translateArgs(args: any = {}) {
  const { where = {}, select, orderBy, skip, take, include } = args

  const query: any = { filter: where }

  // 处理 select/projection
  if (select) {
    // Prisma select 格式: { field1: true, field2: true }
    // Mongoose projection 格式: { field1: 1, field2: 1 } 或 'field1 field2'
    if (typeof select === 'object') {
      const projection: any = {}
      Object.keys(select).forEach((key) => {
        if (select[key] === true) {
          projection[key] = 1
        }
      })
      // 确保总是包含 _id 字段，除非明确排除
      if (!('_id' in select) && !('id' in select)) {
        projection._id = 1
      }
      // 如果明确选择了 id 但没有 _id，也要包含 _id
      if ('id' in select && select.id === true && !('_id' in select)) {
        projection._id = 1
      }
      query.projection = projection
    } else {
      query.projection = select
    }
  }

  // 处理 orderBy
  if (orderBy) {
    // Prisma orderBy 格式: { field: 'asc' | 'desc' } 或 [{ field: 'asc' }]
    // Mongoose sort 格式: { field: 1 | -1 } 或 'field -field'
    if (Array.isArray(orderBy)) {
      const sort: any = {}
      orderBy.forEach((item) => {
        Object.keys(item).forEach((key) => {
          sort[key] = item[key] === 'desc' ? -1 : 1
        })
      })
      query.sort = sort
    } else if (typeof orderBy === 'object') {
      const sort: any = {}
      Object.keys(orderBy).forEach((key) => {
        sort[key] = orderBy[key] === 'desc' ? -1 : 1
      })
      query.sort = sort
    } else {
      query.sort = orderBy
    }
  }

  if (skip !== undefined) query.skip = skip
  if (take !== undefined) query.limit = take

  // include 暂时不处理，需要根据具体关联关系实现
  if (include) {
    console.warn('[MongoInstance] include parameter not yet implemented')
  }

  return query
}

/**
 * 为返回的数据添加 id 字段（映射自 _id），保持与 Prisma 的兼容性
 */
function addIdField<T>(data: T): T & { id: string } {
  if (!data || typeof data !== 'object') {
    return data as T & { id: string }
  }

  // 如果是 Mongoose 文档，直接在文档上添加 id 属性并映射所有字段
  if ('_doc' in data && data._doc) {
    const doc = data as any
    const docData = doc._doc

    // 映射所有字段到文档根级别
    Object.keys(docData).forEach((key) => {
      doc[key] = docData[key]
    })

    // 添加 id 字段
    if (docData._id) {
      doc.id = docData._id.toString()
    } else if (docData._id === '') {
      doc.id = ''
    } else {
      doc.id = 'unknown'
    }

    return doc
  } else {
    // 普通对象，直接添加 id 字段
    const result = { ...data } as any
    if (result._id) {
      result.id = result._id.toString()
    } else if (result._id === '') {
      result.id = ''
    } else {
      result.id = 'unknown'
    }
    return result
  }
}

/**
 * 为数组中的每个对象添加 id 字段
 */
function addIdFieldToArray<T>(dataArray: T[]): Array<T & { id: string }> {
  return dataArray.map((item) => addIdField(item))
}

/**
 * 异步 Prisma-like 适配器，提供与 Prisma 兼容的 API
 * 支持完整的 TypeScript 类型推断
 */
class AsyncPrismaLikeAdapter<T> {
  constructor(private getModelFn: () => Promise<any>) {}

  async findMany(args?: {
    where?: Partial<T>
    select?: Partial<Record<keyof T, boolean>>
    orderBy?: Partial<Record<keyof T, 'asc' | 'desc'>> | Array<Partial<Record<keyof T, 'asc' | 'desc'>>>
    skip?: number
    take?: number
    include?: any
  }): Promise<Array<T & { id: string }>> {
    const model = await this.getModelFn()
    const q = translateArgs(args)
    let query = model.find(q.filter, q.projection)

    if (q.sort) query = query.sort(q.sort)
    if (q.skip) query = query.skip(q.skip)
    if (q.limit) query = query.limit(q.limit)

    const results = await query.lean().exec()
    return addIdFieldToArray(results)
  }

  async findFirst(args?: {
    where?: Partial<T>
    select?: Partial<Record<keyof T, boolean>>
    orderBy?: Partial<Record<keyof T, 'asc' | 'desc'>> | Array<Partial<Record<keyof T, 'asc' | 'desc'>>>
    skip?: number
    include?: any
  }): Promise<(T & { id: string }) | null> {
    const model = await this.getModelFn()
    const q = translateArgs(args)
    const result = await model.findOne(q.filter, q.projection).sort(q.sort).lean().exec()
    return result ? addIdField(result) : null
  }

  async findUnique(args: {
    where: Partial<T> & { _id?: string; id?: string }
    select?: Partial<Record<keyof T, boolean>>
  }): Promise<(T & { id: string }) | null> {
    const model = await this.getModelFn()
    // 如果传入的是 id，转换为 _id
    const where = { ...args.where }
    if ('id' in where && where.id) {
      where._id = where.id
      delete where.id
    }
    const result = await model.findOne(where, args.select).lean().exec()
    return result ? addIdField(result) : null
  }

  async create(args: { data: Omit<T, '_id' | 'id'> & { _id?: string; id?: string } }): Promise<T & { id: string }> {
    const model = await this.getModelFn()
    const data = { ...args.data }
    // 如果传入的是 id，转换为 _id
    if ('id' in data && data.id) {
      data._id = data.id
      delete data.id
    }
    const result = await model.create(data)
    return addIdField(result)
  }

  async createMany(args: { data: Array<Omit<T, '_id' | 'id'> & { _id?: string; id?: string }> }): Promise<Array<T & { id: string }>> {
    const model = await this.getModelFn()
    const processedData = args.data.map((item) => {
      const data = { ...item }
      if ('id' in data && data.id) {
        data._id = data.id
        delete data.id
      }
      return data
    })
    const results = await model.insertMany(processedData)
    return addIdFieldToArray(results)
  }

  async delete(args: { where: Partial<T> & { _id?: string; id?: string } }): Promise<(T & { id: string }) | null> {
    const model = await this.getModelFn()
    const where = { ...args.where }
    if ('id' in where && where.id) {
      where._id = where.id
      delete where.id
    }
    const result = await model.findOneAndDelete(where).lean().exec()
    return result ? addIdField(result) : null
  }

  async update(args: {
    where: Partial<T> & { _id?: string; id?: string }
    data: Partial<T> | {
      [K in keyof T]?: T[K] | { push?: T[K] extends Array<infer U> ? U : never }
    }
  }): Promise<(T & { id: string }) | null> {
    const model = await this.getModelFn()
    const where = { ...args.where }
    if ('id' in where && where.id) {
      where._id = where.id
      delete where.id
    }
    // 处理 Prisma 特有的数组操作，如 { round_ids: { push: "value" } }
    const processedData = this.processPrismaUpdateData(args.data)
    const result = await model.findOneAndUpdate(where, processedData, { new: true }).lean().exec()
    return result ? addIdField(result) : null
  }

  /**
   * 处理 Prisma 风格的更新数据，转换为 Mongoose 格式
   */
  private processPrismaUpdateData(data: any): any {
    const processed: any = {}

    for (const [key, value] of Object.entries(data)) {
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        // 检查是否是 Prisma 的特殊操作
        if ('push' in value) {
          processed['$push'] = processed['$push'] || {}
          processed['$push'][key] = value.push
        } else if ('set' in value) {
          processed[key] = value.set
        } else if ('increment' in value) {
          processed['$inc'] = processed['$inc'] || {}
          processed['$inc'][key] = value.increment
        } else {
          // 普通嵌套对象
          processed[key] = value
        }
      } else {
        // 普通字段
        processed[key] = value
      }
    }

    return processed
  }

  async deleteMany(args?: { where?: Partial<T> }): Promise<{ deletedCount: number }> {
    const model = await this.getModelFn()
    return model.deleteMany(args?.where ?? {}).lean().exec()
  }

  async updateMany(args: {
    where?: Partial<T>
    data: Partial<T>
  }): Promise<{ modifiedCount: number; matchedCount: number }> {
    const model = await this.getModelFn()
    const processedData = this.processPrismaUpdateData(args.data)
    return model.updateMany(args.where ?? {}, processedData).lean().exec()
  }

  async upsert(args: {
    where: Partial<T> & { _id?: string; id?: string }
    update: Partial<T>
    create: Omit<T, '_id' | 'id'> & { _id?: string; id?: string }
  }): Promise<T & { id: string }> {
    const model = await this.getModelFn()
    const where = { ...args.where }
    if ('id' in where && where.id) {
      where._id = where.id
      delete where.id
    }
    const processedUpdate = this.processPrismaUpdateData(args.update)
    const result = await model.findOneAndUpdate(where, processedUpdate, {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true,
    }).lean().exec()
    return addIdField(result)
  }

  async count(args?: { where?: Partial<T> }): Promise<number> {
    const model = await this.getModelFn()
    return model.countDocuments(args?.where ?? {}).exec()
  }

  // 原样暴露 raw/aggregate 方法，兼容现有代码
  async findRaw({ filter, options }: { filter: any; options?: any }): Promise<Array<T & { id: string }>> {
    const model = await this.getModelFn()
    let results
    if (options?.limit) {
      results = await model.collection.find(filter).limit(options.limit).toArray()
    } else {
      results = await model.collection.find(filter).toArray()
    }
    return addIdFieldToArray(results)
  }

  async aggregateRaw({ pipeline }: { pipeline: any[] }): Promise<any[]> {
    const model = await this.getModelFn()
    const results = await model.aggregate(pipeline).exec()
    // 聚合结果可能不是标准的文档格式，所以只在有 _id 字段时才添加 id
    return results.map((item) => {
      if (item && typeof item === 'object' && '_id' in item) {
        return addIdField(item)
      }
      return item
    })
  }

  // 支持 Prisma 的数组操作
  async pushToArray(
    where: Partial<T> & { _id?: string; id?: string },
    field: keyof T,
    value: T[keyof T] extends Array<infer U> ? U : never
  ): Promise<(T & { id: string }) | null> {
    const model = await this.getModelFn()
    const whereClause = { ...where }
    if ('id' in whereClause && whereClause.id) {
      whereClause._id = whereClause.id
      delete whereClause.id
    }
    const updateQuery: any = {}
    updateQuery[field] = { $push: value }
    const result = await model.findOneAndUpdate(whereClause, updateQuery, { new: true }).lean().exec()
    return result ? addIdField(result) : null
  }
}

/**
 * MongoDB 实例管理器类，封装所有模型和连接的管理逻辑
 */
class MongoInstanceManager {
  private static instance: MongoInstanceManager | null = null

  // 私有属性，封装原来的全局变量
  private chatModel: any = null
  private chatMoerOverseasModel: any = null
  private chatConnection: any = null
  private moerOverseasConnection: any = null

  // 公共适配器属性
  public readonly chat: AsyncPrismaLikeAdapter<Chat & { id: string }>
  public readonly chatMoerOverseas: AsyncPrismaLikeAdapter<ChatMoerOverseas & { id: string }>

  private constructor() {
    // 初始化适配器
    this.chat = new AsyncPrismaLikeAdapter<Chat>(() => this.getChatModel())
    this.chatMoerOverseas = new AsyncPrismaLikeAdapter<ChatMoerOverseas>(() => this.getChatMoerOverseasModel())
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): MongoInstanceManager {
    if (!MongoInstanceManager.instance) {
      MongoInstanceManager.instance = new MongoInstanceManager()
    }
    return MongoInstanceManager.instance
  }

  /**
   * 获取 Chat 模型，确保连接建立后再创建模型
   */
  private async getChatModel() {
    if (!this.chatModel) {
      if (!this.chatConnection) {
        this.chatConnection = await getMongoConnection()
      }
      this.chatModel = getModelForClass(Chat, { existingConnection: this.chatConnection })
    }
    return this.chatModel
  }

  /**
   * 获取 ChatMoerOverseas 模型，确保连接建立后再创建模型
   */
  private async getChatMoerOverseasModel() {
    if (!this.chatMoerOverseasModel) {
      if (!this.moerOverseasConnection) {
        this.moerOverseasConnection = await getMongoConnection('moer_overseas')
      }
      this.chatMoerOverseasModel = getModelForClass(ChatMoerOverseas, { existingConnection: this.moerOverseasConnection })
    }
    return this.chatMoerOverseasModel
  }
}

/**
 * MongoDB 实例，提供 Prisma-like 的访问接口
 * 使用方式: await Mongo_instance.chat.findMany() - 只需要 await 一次！
 * 支持完整的 TypeScript 类型推断
 *
 * 注意：第一次调用时会自动建立数据库连接
 */
export const Mongo_instance = MongoInstanceManager.getInstance()
