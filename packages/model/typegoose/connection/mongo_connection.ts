import mongoose from 'mongoose'
import { Config } from 'config'

// 存储不同数据库的连接实例
const connections = new Map<string, mongoose.Connection>()
const connectingPromises = new Map<string, Promise<mongoose.Connection>>()

/**
 * 获取 MongoDB 连接，支持多数据库连接
 * 基于现有的 PrismaMongoClient 配置
 */
export async function getMongoConnection(dbName?: string): Promise<mongoose.Connection> {
  const targetDbName = dbName || Config.setting.projectName || 'yuhe'
  const connectionKey = targetDbName

  // 如果已有连接且状态正常，直接返回
  const existingConn = connections.get(connectionKey)
  if (existingConn && existingConn.readyState === 1) {
    return existingConn
  }

  // 如果正在连接中，等待连接完成
  const existingPromise = connectingPromises.get(connectionKey)
  if (existingPromise) {
    return existingPromise
  }

  // 创建新的连接
  const connectPromise = createConnection(targetDbName, connectionKey)
  connectingPromises.set(connectionKey, connectPromise)

  try {
    const conn = await connectPromise
    connections.set(connectionKey, conn)
    return conn
  } finally {
    connectingPromises.delete(connectionKey)
  }
}

/**
 * 创建新的数据库连接
 */
async function createConnection(dbName: string, connectionKey: string): Promise<mongoose.Connection> {
  try {
    const uri = `mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/${dbName}?authSource=admin`

    const conn = mongoose.createConnection(uri, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferCommands: true, // 改为 true，允许缓冲命令直到连接建立
    })

    // 设置连接事件监听器
    conn.on('error', (err) => {
      console.error(`[Mongoose ${dbName}] Connection error:`, err)
    })

    conn.on('disconnected', () => {
      console.log(`[Mongoose ${dbName}] Disconnected`)
      connections.delete(connectionKey)
    })

    conn.on('reconnected', () => {
      console.log(`[Mongoose ${dbName}] Reconnected to MongoDB`)
    })

    // 等待连接建立
    await new Promise<void>((resolve, reject) => {
      conn.once('open', () => {
        console.log(`[Mongoose] Connected to MongoDB database: ${dbName}`)
        resolve()
      })
      conn.once('error', reject)
    })

    return conn
  } catch (error) {
    console.error(`[Mongoose] Failed to connect to MongoDB database ${dbName}:`, error)
    throw error
  }
}

/**
 * 获取配置数据库连接
 */
export async function getConfigMongoConnection(): Promise<mongoose.Connection> {
  return getMongoConnection('bot_config')
}

/**
 * 优雅关闭所有连接
 */
export async function closeMongoConnections(): Promise<void> {
  // 关闭所有连接
  for (const [key, conn] of connections.entries()) {
    try {
      await conn.close()
      console.log(`[Mongoose] Closed connection to ${key}`)
    } catch (error) {
      console.error(`[Mongoose] Error closing connection to ${key}:`, error)
    }
  }
  connections.clear()
  connectingPromises.clear()
}
