# Prisma 到 Typegoose/Mongoose 迁移指南

本目录包含了从 Prisma 迁移到 Typegoose/Mongoose 的完整解决方案，提供了 **无痛迁移** 的 TypeGooseMongoClient 单例客户端。

## 🎯 迁移目标

- ✅ 保持 100% API 兼容性
- ✅ 采用与 PrismaMongoClient 相同的单例模式
- ✅ 支持现有的所有 Prisma 调用模式
- ✅ 自动重连和连接池管理
- ✅ 渐进式迁移，新旧代码可并存
- ✅ 性能优化和原生 MongoDB 查询支持

## 📁 文件结构

```
packages/model/typegoose/
├── README.md                           # 本文件
├── TypeGooseMongoClient.ts            # 核心客户端，采用单例模式
├── connection/
│   └── MongoConnection.ts             # 数据库连接管理（单例 + 自动重连）
├── models/
│   ├── chat.ts                        # Chat 模型定义（已存在）
│   └── chat-history.ts                # ChatHistory 模型定义（已存在）
├── types/
│   └── embedded.ts                    # 嵌套类型定义（已存在）
├── examples/
│   ├── migration-example.ts           # 迁移示例代码
│   └── migration-guide.ts             # 详细迁移指南
├── tests/
│   └── mongo-instance.test.ts         # 功能测试
└── scripts/
    └── verify-typegoose-migration.ts  # 迁移验证脚本
```

## 🚀 快速开始

### 1. 验证环境

```bash
# 运行迁移验证脚本
npx ts-node packages/model/typegoose/scripts/verify-typegoose-migration.ts
```

### 2. 迁移代码

**迁移前 (Prisma):**
```typescript
import { PrismaMongoClient } from 'model/mongodb/prisma';
const prisma = PrismaMongoClient.getInstance();

const chats = await prisma.chat.findMany({
  where: { phone: '13800138000' },
  orderBy: { created_at: 'desc' },
  take: 10
});
```

**迁移后 (TypeGooseMongoClient):**
```typescript
import { TypeGooseMongoClient } from 'model/typegoose/TypeGooseMongoClient';
const client = TypeGooseMongoClient.getInstance();

const chats = await client.chat.findMany({
  where: { phone: '13800138000' },
  orderBy: { created_at: 'desc' },
  take: 10
});
```

**采用相同的单例模式，迁移成本极低！**

### 3. 使用新版 ChatDB

```typescript
// 使用 TypeGooseMongoClient 版本的 ChatDB
import { ChatDB } from 'service/database/chat-mongoose';

// API 完全一致
const chat = await ChatDB.getById(chatId);
await ChatDB.updateState(chatId, newState);
await ChatDB.pushRoundId(chatId, roundId);
```

## 📋 支持的操作

### 基本 CRUD
- ✅ `findMany()` - 查询多条记录
- ✅ `findFirst()` - 查询第一条记录
- ✅ `findUnique()` - 根据唯一条件查询
- ✅ `create()` - 创建记录
- ✅ `createMany()` - 批量创建
- ✅ `update()` - 更新记录
- ✅ `updateMany()` - 批量更新
- ✅ `delete()` - 删除记录
- ✅ `deleteMany()` - 批量删除
- ✅ `upsert()` - 更新或插入
- ✅ `count()` - 计数

### 高级功能
- ✅ `where` 条件查询（支持 MongoDB 原生语法）
- ✅ `select` 字段选择
- ✅ `orderBy` 排序
- ✅ `skip` / `take` 分页
- ✅ 数组操作：`{ round_ids: { push: value } }`
- ✅ `findRaw()` - 原生 MongoDB 查询
- ✅ `aggregateRaw()` - 聚合查询

### 数据库支持
- ✅ 主数据库 (`yuhe`)
- ✅ Moer Overseas 数据库 (`moer_overseas`)
- ✅ 配置数据库 (`bot_config`)

## 🔧 配置说明

### 连接配置
MongoConnection.ts 使用与现有 PrismaMongoClient 相同的连接字符串：

```typescript
// 主数据库
mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/yuhe?authSource=admin

// Moer Overseas
mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/moer_overseas?authSource=admin
```

### 连接池设置
```typescriptw
{
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferCommands: false,
  bufferMaxEntries: 0,
}
```

## 📊 迁移路线图

| 阶段 | 目标 | 状态 | 说明 |
|------|------|------|------|
| 0️⃣ 准备 | 依赖安装 | ✅ 完成 | mongoose, @typegoose/typegoose 已安装 |
| 1️⃣ 基础设施 | 连接 + 代理 | ✅ 完成 | MongoConnection + MongoInstance |
| 2️⃣ 模型定义 | Chat 模型 | ✅ 完成 | 已有 Typegoose 模型 |
| 3️⃣ 渐进切换 | 业务代码迁移 | 🔄 进行中 | 按模块逐步替换 import |
| 4️⃣ 特性补齐 | 事务、中间件 | ⏳ 待定 | 根据需要实现 |
| 5️⃣ 清理收尾 | 移除 Prisma | ⏳ 待定 | 确认无问题后清理 |

## 🧪 测试

### 运行测试
```bash
# 功能测试
npm test mongo-instance.test.ts

# 迁移验证
npx ts-node packages/model/typegoose/scripts/verify-migration.ts
```

### 测试覆盖
- ✅ 基本 CRUD 操作
- ✅ 复杂查询条件
- ✅ Prisma 风格数组操作
- ✅ Raw 查询和聚合
- ✅ 错误处理
- ✅ 并发性能
- ✅ 多数据库支持

## 🔄 迁移步骤

### 第一步：验证环境
```bash
npx ts-node packages/model/typegoose/scripts/verify-migration.ts
```

### 第二步：选择迁移方式

**方式 A：直接替换 import（推荐）**
```typescript
// 将这行
import { PrismaMongoClient } from 'model/mongodb/prisma';
const prisma = PrismaMongoClient.getInstance();

// 改为这行
import { MongoInstance as prisma } from 'model/typegoose/MongoInstance';
```

**方式 B：使用新版 ChatDB**
```typescript
// 将这行
import { ChatDB } from 'service/database/chat';

// 改为这行
import { ChatDB } from 'service/database/chat-mongoose';
```

### 第三步：测试验证
1. 运行现有的单元测试
2. 检查数据一致性
3. 监控性能指标

### 第四步：逐步推广
1. 先迁移读操作
2. 确认无误后迁移写操作
3. 按模块逐步推进

## ⚠️ 注意事项

### 字段映射
- Prisma 使用 `id` 字段
- MongoDB/Mongoose 使用 `_id` 字段
- MongoInstance 自动处理映射

### 数组操作
```typescript
// Prisma 风格（MongoInstance 支持）
{ round_ids: { push: "new-round-id" } }

// 会自动转换为 MongoDB 操作
{ $push: { round_ids: "new-round-id" } }
```

### 查询语法
```typescript
// 支持 MongoDB 原生查询语法
{
  where: {
    course_no: { $exists: true, $ne: null },
    'contact.wx_name': { $regex: 'pattern', $options: 'i' }
  }
}
```

## 🚨 故障排除

### 连接问题
```bash
# 检查网络连接
ping dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com

# 检查数据库配置
echo $MONGO_URI
```

### 性能问题
1. 检查索引是否正确迁移
2. 监控连接池使用情况
3. 使用 `aggregateRaw()` 进行复杂查询优化

### 兼容性问题
1. 对比 Prisma 和 MongoInstance 的查询结果
2. 检查字段映射（id vs _id）
3. 验证数组操作的正确性

## 📞 支持

如有问题，请：
1. 查看测试文件中的示例
2. 运行验证脚本诊断问题
3. 检查 MongoDB 连接和权限

## 🎉 迁移完成后的优势

- 🚀 **性能提升**：原生 MongoDB 查询，无 Prisma 中间层开销
- 🔧 **灵活性**：支持 MongoDB 所有原生功能
- 📦 **体积减小**：移除 Prisma 依赖，减少包体积
- 🛠️ **可维护性**：直接的 Mongoose 模型，更易调试和扩展
- 🔄 **兼容性**：100% 兼容现有 Prisma API 调用
