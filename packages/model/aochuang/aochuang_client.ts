import axios from 'axios'
import crypto from 'crypto'
import { Retry } from '../../lib/retry/retry'
import { Config } from 'config'


interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

export class AoChuangClient {
  private baseUrl: URL

  constructor() {
    this.baseUrl = new URL(Config.setting.whatsapp.baseUrl)
  }

  private generateToken(timestamp: number): string {
    const tenantId = Config.setting.whatsapp.tenantId
    const apiKey = Config.setting.whatsapp.apiKey

    const rawString = tenantId +  timestamp + apiKey
    return crypto.createHash('md5').update(rawString).digest('hex')
  }

  private getHeader(): object {
    const timestamp = new Date().getTime()

    return {
      apiKey: Config.setting.whatsapp.apiKey,
      tenantId: Config.setting.whatsapp.tenantId,
      timestamp,
      token: this.generateToken(timestamp),
    }
  }

  async get<T>(requestUri: string, payload: object = {}) {

    try {
      return await Retry.retry(3, async () => {
        return await axios.get<T>(this.baseUrl.href + requestUri, {
          params: payload,
          headers: this.getHeader()
        })
      })
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }
      throw new Error(`请求失败: ${requestUri} ${e}`)
    }
  }

  async post<T>(requestUri: string, payload: any) {
    try {
      return await Retry.retry(3, async () => {
        return await axios.post<T>(this.baseUrl.href + requestUri, payload, {
          headers: {
            'Accept': '*/*',
            'Content-Type': 'application/json',
            ...this.getHeader()
          }
        })
      }, { delayFunc: (count) => count * 200 })
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }
      throw new Error(`请求失败: ${requestUri} ${e}`)
    }
  }

  async put<T>(requestUri: string, payload: any) {
    try {
      return await Retry.retry(3, async () => {
        return await axios.put<T>(this.baseUrl.href + requestUri, payload, {
          params: this.getHeader()
        })
      }, { delayFunc: (count) => count * 200 })
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }
      throw new Error(`请求失败: ${requestUri} ${e}`)
    }
  }
}