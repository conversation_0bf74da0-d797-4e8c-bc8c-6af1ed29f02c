import { AoChuangAPI } from '../aochuang_api'

const xinRanWhatsAppAccount = '***********'
const whatsAppReceiveAccount = process.env.whatsAppReceiveAccount || xinRanWhatsAppAccount


describe('Test aoChuang', function () {
  jest.setTimeout(60000)
  beforeAll(() => {

  })

  it('send Msg', async () => {
    const res = await AoChuangAPI.sendMsg({
      targetType: 1,
      sendWhatsApp: '************',
      friendWhatsApp: '***********',
      content: [
        {
          type: 1,
          text: '我'
        }
      ]
    })

    console.log(JSON.stringify(res.data, null, 4))
  }, 60000)

  it('query Task', async () => {
    const res = await AoChuangAPI.queryExecuteStatus('143416')

    console.log(JSON.stringify(res.data, null, 4))
  }, 60000)

  it('simple test', async () => {
    await AoChuangAPI.sendMsg({
      targetType: 1,
      sendWhatsApp: '************',
      friendWhatsApp: whatsAppReceiveAccount,
      content: [
        {
          type: 1,
          text: '您好，我是一个人'
        }
      ]
    })
  })

  it('test emoji', async () => {
    const res = await AoChuangAPI.sendMsg({
      targetType: 1,
      sendWhatsApp: '************',
      friendWhatsApp: whatsAppReceiveAccount,
      content: [
        {
          type: 2,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/3EB05729CB86BD91FC272A_1740983700501.jpeg',
        }
      ]
    })

    console.log(res.data.data.taskId)
  })

  it('test voice mp3', async () => {
    await AoChuangAPI.sendMsg({
      targetType: 1,
      sendWhatsApp: '************',
      friendWhatsApp: whatsAppReceiveAccount,
      content: [
        {
          type: 3,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/sop/day_3%E6%B7%B1%E5%BA%A6%CE%B1%E6%B3%A2%E5%86%A5%E6%83%B3%E9%9F%B3%E4%B9%90.mp3',
        }
      ]
    })
  })

  it('test voice ogg', async () => {
    await AoChuangAPI.sendMsg({
      targetType: 1,
      sendWhatsApp: '************',
      friendWhatsApp: whatsAppReceiveAccount,
      content: [
        {
          type: 3,
          url: 'https://wscrm-v3.oss-accelerate.aliyuncs.com/544999/3/3A9EED2CB128969C890F_1740639360616.oga',
        }
      ]
    })
  })

  it('test file', async () => {
    await AoChuangAPI.sendMsg({
      targetType: 1,
      sendWhatsApp: '************',
      friendWhatsApp: whatsAppReceiveAccount,
      content: [
        {
          type: 4,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E4%BA%94%E5%A4%A9%E5%86%A5%E6%83%B3%E5%85%A5%E9%97%A8%E8%90%A5%E6%80%9D%E7%BB%B4%E5%AF%BC%E5%9B%BE%E7%B2%BE%E5%8D%8E%E6%B1%87%E6%80%BB_zoqb_dZ9aV.pdf',
          fileName:'pdf.pdf'
        }
      ]
    })
  })

  it('import customer 1', async () => {
    await AoChuangAPI.batchImportCustomers([
      {
        agentAccount: 'wuruixin',
        whatsApp: '*************',
        tabName:'五天营14期'
      }
    ])
  }, 60000)
  it('import customer 2', async () => {
    await AoChuangAPI.batchImportCustomers([
      {
        agentAccount: 'wuruixin',
        whatsApp: '*************',
        tabName:'五天营14期'
        // tabName:'无效数据'
      }
    ])
  }, 60000)

  // it('a', async () => {
  //   Config.setting.wechatConfig = await loadConfigByWxId('****************')
  //   Config.setting.localTest = false
  //
  //   const a = {
  //     user_id: '123456',
  //     chat_id: 'xxx',
  //     name: TaskName.GroupSendPushEnergyTest,
  //     scheduleTime: {
  //       is_course_week: true,
  //       day: 1,
  //       time: '12:00:00'
  //     }
  //   }
  //
  //   await new GroupSend().process(a as any as ITask)
  // }, 60000)
})
