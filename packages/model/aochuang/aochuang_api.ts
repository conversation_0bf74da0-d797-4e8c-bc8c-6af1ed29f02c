import axios from 'axios'
import dayjs from 'dayjs'
import crypto from 'crypto'
import { Config } from 'config'
import { AoChuangClient } from './aochuang_client'
import { RegexHelper } from 'lib/regex/regex'


// Interface for customer data
export interface ICustomerData {
  agentAccount: string
  whatsApp: string
  friendName?: string
  sex?: string
  birthday?: string
  address?: string
  email?: string
  profession?: string
  income?: string
  desc?: string
  tabName?: string
  stage?: string
  source?: string
  languageTag?: string
  welcome?: string
  [key: string]: any // For dynamic extension fields
}

// Interface for batch import response
export interface IBatchImportResponseData {
  successExcel: any[]
  errExcel: Array<{
    errPhone: string
    errInfo: string
    errCode: number
  }>
  successCount: number
  errCount: number
  notFollowSuccessCount: string
  notFollowSuccessExcels: any[]
}


// WhatsApp API消息类型映射
export enum WhatsAppMsgType {
  Text = 1,
  Image = 2,
  Audio = 3,
  File = 4,
  Video = 5,
  Card = 6,
  CardLink = 10,
  FlowLink = 11
}

export interface IWhatsAppContentItem {
  type: WhatsAppMsgType // 内容类型 1.文字 2.图片 3.音频 4.文件 5.视频 6.名片 10.名片超链 11.分流超链

  text?: string // 内容文本信息，文本类型内容时必填
  url?: string // 静态资源地址，适用于图片、视频等类型
  fileName?: string // 文件名，适用于文件类型

  title?: string // 标题，通常适用于消息内容的简短说明
  desc?: string // 备注或描述，内容的详细说明
  link?: string // 链接，适用于超链类型的内容

  sort?: number // 排序，非必填字段

  transId?: string // 追踪ID，用于追踪消息的发送状态
  variableFields?: Array<{
    index: number // 变量字段的索引位置
    type: number // 变量字段的类型
    name: string // 变量字段的名称
    defaultValue: string // 变量字段的默认值
  }> // 变量字段数组，非必填字段，用于动态内容填充
  routeType?: number // 路由类型，1表示坐席分流，2表示客服号分组分流，3表示自定义接粉
  routeList?: string[] // 分流目标列表，包含分流目标的标识
  adsTemplateId?: number // 广告模板ID，仅在名片超链时有效，存在时会覆盖其他字段 (title, desc, link, url)
}

export enum TargetType {
  Personal = 1,
  Group = 2,
}



export interface IAoChuangSendMsgBody {
  targetType: TargetType // 目标类型（1：个人，2：群组）
  sendWhatsApp: string // 客服号
  friendWhatsApp: string // 目标受众的WhatsApp号码，接收方的号码或标识
  content: IWhatsAppContentItem[] // 内容信息，包含具体的消息内容

  name?: string // 任务名称，任务的标题或标识（可选，最大30个字符）
  startTaskTime?: string // 任务开始时间，格式为“YYYY-MM-DD HH:mm:ss”，可选字段
  endTaskTime?: string // 任务结束时间，格式为“YYYY-MM-DD HH:mm:ss”，可选字段
  sendType?: number // 发送方式，1表示PC端，2表示手机端，3表示微信
}

interface IQueryExecuteStatusResponseData {
  taskId: string
  status: number
  info: {
    whatsApp: string
    friendWhatsApp: string
    time: string
    status: number
  }[]
}

interface ISendMsgResponseData {
  code: number
  message: string
  data: {
    taskId: string
  }
}

export class AoChuangAPI {

  public static async sendMsg(param: IAoChuangSendMsgBody) {
    const url = 'group-dispatch-api/gsTask/assign/soCreate'

    // 做一下参数校验
    if (!RegexHelper.isNumericString(param.friendWhatsApp) || !RegexHelper.isNumericString(param.sendWhatsApp)) {
      throw new Error('Invalid WhatsApp number')
    }

    if (param.friendWhatsApp.length > 15 || param.sendWhatsApp.length > 15) {
      throw new Error('Invalid WhatsApp number')
    }

    return await new AoChuangClient().post<ISendMsgResponseData>(url, param)
  }


  /**
   * 查询群发任务执行状态
   * @param taskId 任务ID
   * @returns 任务执行状态详情
   */
  public static async queryExecuteStatus(taskId: string) {
    const url = `group-dispatch-api/gsTask/queryExecuteStatus?taskId=${taskId}`
    return await new AoChuangClient().get<IQueryExecuteStatusResponseData>(url)
  }

  /**
   * Batch import customers
   * @param param Customer batch import parameters
   * @returns Response with import results
   */
  public static async batchImportCustomers(param: ICustomerData[]) {
    const url = 'http://wascrm.socialepoch.com/wscrm-bus-api/customer/api/batchImport'
    const tenantId =  Config.setting.whatsapp.tenantId
    const userName = Config.setting.whatsapp.mainAccountUserName
    const apiKey = Config.setting.whatsapp.apiKey
    const createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

    const rawString =  tenantId + userName + createTime + apiKey
    const token =  crypto.createHash('md5').update(rawString).digest('hex')

    return await axios.post<IBatchImportResponseData>(url,  {
      tenantId,
      userName,
      token,
      createTime,
      data: param
    })
  }
}