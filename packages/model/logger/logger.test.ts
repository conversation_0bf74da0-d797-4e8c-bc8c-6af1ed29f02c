// import logger from './logger'
// import pino from 'pino'
// import { PrismaMongoClient } from '../mongodb/prisma'
// import { EventTracker } from './data_driven'
// import { ChatDB } from '../../service/moer/database/chat'
//
// describe('Test', function () {
//   beforeAll(() => {
//
//   })
//
//   it('should pass', async () => {
//     const chatId = '12345'
//
//     try {
//       JSON.parse('test')
//     } catch (error: any) {
//       logger.error(chatId, error.stack)
//     }
//     logger.log(undefined, 'test message')
//   })
//
//   it('data driven', async () => {
//     // await EventTracker.track('id1', IEventType.FailedToJoinGroup, { url: 'https://www.google.com' })
//     // await sleep(3000)
//     console.log(JSON.stringify(await PrismaMongoClient.getInstance().chat.deleteMany({ where: { id: { not: {
//       contains: '_'
//     } } } }), null, 4))
//   }, 30000)
//
//   it('prisma', async () => {
//     const chats = await PrismaMongoClient.getInstance().event_track.findRaw({
//       filter: {
//         'meta.reason': 'NotBindPhone'
//       }
//     }) as unknown as any[]
//
//     const res: any[] = []
//
//     // 查看还没绑定上 moer Id 的
//     for (const chat of chats) {
//       const a =  await gChatDB.etById(chat.chat_id)
//       if (a && !a.moer_id) {
//
//         if (a.contact && a.contact.wx_name) {
//           const accountMap = {
//             '****************': 'qiaoqiao',
//             '****************': 'tongtong',
//             '****************': 'moer3',
//             '****************': 'moer4',
//           }
//
//           res.push({
//             [a.contact.wx_name]: accountMap[a.wx_id]
//           })
//         } else {
//           console.log(JSON.stringify(a, null, 4))
//         }
//       }
//     }
//
//     console.log(JSON.stringify(res, null, 4))
//   }, 60000)
//
//   it('123123', async () => {
//     console.log(JSON.stringify(await PrismaMongoClient.getInstance().event_track.findRaw({
//       filter: {
//         'meta.url': 'https://www.google.com'
//       }
//     }), null, 4))
//   }, 60000)
//
//   it('埋点', async () => {
//     EventTracker.track('id1', 'FailedToJoinGroup', { progress: '销售' })
//
//     // await PrismaMongoClient.getInstance().event_track.findRaw({
//     //   filter: {
//     //     'meta.progress': '销售'
//     //   }
//     // })
//
//     EventTracker.track('id1', 'FailedToJoinGroup', { progress: '销售' })
//
//   }, 60000)
//
//   it('test', async () => {
//     logger.log({ chat_id: 'hi' }, '呵呵')
//   }, 60000)
//
//
//   it('logger', async () => {
//     logger.debug('fk u', 'hi')
//     logger.debug({ fk: 'u' }, 'fk u')
//     logger.log({ fk: 'u' }, 'fk u')
//     logger.log('fk u')
//   }, 30000)
//
//   it('pino', async () => {
//     pino().info('hi', 'fk u')
//   }, 30000)
//
//   it('1', async () => {
//     function removeEscapedCharacters(str) {
//       // 匹配转义字符
//       const escapedCharRegex = /\\[btnfr"'\\]/g
//       return str.replace(escapedCharRegex, '')
//     }
//
//     function removeAnsiColors(str) {
//       // 匹配 ANSI 颜色控制字符
//       const ansiColorRegex = /[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nq-uy=><]/g
//       return str.replace(ansiColorRegex, '')
//     }
//
//     function cleanLogMessage(str) {
//       // 去除转义字符
//       str = removeEscapedCharacters(str)
//       // 去除 ANSI 颜色控制字符
//       str = removeAnsiColors(str)
//       return str
//     }
//
//     // 示例字符串
//     const inputStr = 'enter LLMNode {\n  \"state\": {\n    \"chat_id\": \"7881300846030208_1688854546332791\",\n    \"user_id\": \"7881300846030208\",\n    \"userMessage\": \"我大专，咋搞\"\n  },\n  \"dynamicPrompt\": \"询问客户当前遇到的问题和想达成目标是什么。例如：\\\"有什么诉求？咱们读完书之后有什么规划？\\\"\"\n}'
//
//     // 去除转义字符和 ANSI 颜色控制字符
//     const cleanedStr = cleanLogMessage(inputStr)
//     console.log(cleanedStr)
//   }, 60000)
// })