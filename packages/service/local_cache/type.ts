export interface IChatState {
  nodeInvokeCount: Record<string, number> // 记录每个节点被调用的次数

  state: Record<string, boolean | number | string | undefined> // 会话状态，放一些 Flag 已拉群，已邀请入群等
  nextStage: string // 下个节点
  userSlots: Record<string, contentWithFrequency>
}

export type contentWithFrequency = {
  content: string
  frequency: number
}

export interface ICustomSlot {
  topic: string
  subTopic: string
  content: string
  frequency: number
}