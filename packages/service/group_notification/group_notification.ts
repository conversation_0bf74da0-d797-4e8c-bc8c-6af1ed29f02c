import { <PERSON><PERSON><PERSON><PERSON> } from 'model/juzi/api'
import { Config } from 'config'
import { IWecomMsgType } from 'model/juzi/type'
import logger from 'model/logger/logger'

/**
 * 群通知
 */
export class GroupNotification {
  public static async notify(message: string, groupId?: string, imBotId?: string) {
    await JuziAPI.sendGroupMsg(imBotId ?? Config.setting.wechatConfig?.id as string, groupId ? groupId : Config.setting.wechatConfig?.notifyGroupId as string, {
      type: IWecomMsgType.Text,
      text: message
    })
  }

  public static async notifyToHumanGroup(message: string) {
    const HUMAN_BOT_ID = '1688858335726355'
    const HUMAN_NOTIFY_GROUP_ID = 'R:10815051791863856'

    try {
      const result = await JuziAPI.sendGroupMsg(
        HUMAN_BOT_ID,
        HUMAN_NOTIFY_GROUP_ID,
        { type: IWecomMsgType.Text, text: message }
      )
      logger.log('[notifyToHumanGroup] result:', result)
    } catch (e) {
      logger.warn('[notifyToHumanGroup] error:', e)
    }
  }
}