import {
  Configuration, Contacts<PERSON>pi,
  WhatsappMessagesApi,
  WhatsappMessageTemplateComponent,
  WhatsappTemplatesApi
} from '@ycloud-cpaas/ycloud-sdk-node'

describe('test', () => {
  jest.setTimeout(600000)
  const ycloudApiKey = 'b129ab33adb30bce3e9c0e77defd8011'
  const configuration = new Configuration({ apiKey: ycloudApiKey })
  it('test', async() => {
    const ycloud = new WhatsappTemplatesApi(configuration)
    const list = await ycloud.list({ filterName:'f_v1e_4_1626_notice1' })
    console.dir(list.data, { depth:Infinity })
  })

  it('sendTemplate', async() => {
    const messageClient = new WhatsappMessagesApi(configuration)
    try {
      const res = await messageClient.sendDirectly({
        from: '+85296895907',
        to: '+18573353888',
        type: 'template',
        template:{
          name: 'f_v1e_4_1626_notice1',
          language: {
            code:'en'
          },
          components:[
          <WhatsappMessageTemplateComponent>{
            type:'body',
            parameters:[{
              type:'text',
              text:'hello_world'
            }]
          },
          <WhatsappMessageTemplateComponent>{
            type:'header',
            parameters:[{
              type:'image',
              image:{
                link:'https://oss-ycloud-publicread.oss-ap-southeast-1.aliyuncs.com/online/BASE-FILE/2025/07/30/398c1395-65a9-4eb8-ab6c-8d98e5ff7b13.jpg'
              }
            }]
          },
          ]
        }
      })

      console.log(res.data)
    } catch (e) {
      console.dir(e, { depth:Infinity })
    }
  })

  it('sendText', async() => {
    const messageClient = new WhatsappMessagesApi(configuration)
    try {
      const res = await messageClient.send({
        from: '+85296895907',
        to: '+18573353888',
        type: 'text',
        text:{
          body:'hello_world'
        }
      })

      console.log(res.data)
    } catch (e) {
      console.dir(e, { depth:Infinity })
    }
  })

  it('createContact', async () => {
    const messageClient = new ContactsApi(configuration)
    try {
      const res = await messageClient.create({
        phoneNumber:'+18573353888'
      })
      console.log(res.data)
    } catch (e) {
      console.dir(e, { depth:Infinity })
    }
  }, 60000)
})