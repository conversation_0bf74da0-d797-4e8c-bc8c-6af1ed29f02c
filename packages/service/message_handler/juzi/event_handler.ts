import { z } from 'zod'

// 定义 FollowUser 接口
interface FollowUser {
    wecomUserId: string
}

// 定义 ImInfo 接口
interface ImInfo {
    externalUserId: string
    followUser: FollowUser
}

// 定义 BotInfo 接口
interface BotInfo {
    botId: string
    imBotId: string
    name: string
    avatar: string
}

// 定义主接口 Contact
export interface FriendAcceptedEvent {
    imContactId: string
    name: string
    avatar: string
    gender: number
    createTimestamp: number
    imInfo: ImInfo
    botInfo: BotInfo
}

export abstract class JuziEventHandler {
  public async handle(data: any) {
    // 使用 zod 校验
    if (JuziEventHandler.isFriendAcceptedEvent(data)) {
      return await this.handleFriendAcceptedEvent(data as FriendAcceptedEvent)
    }

    console.log('event:', JSON.stringify(data, null, 4))
  }

  public static isFriendAcceptedEvent(data: any) {
    const schema = z.object({
      imContactId: z.string(),
      name: z.string(),
      avatar: z.string().url(),
      createTimestamp: z.number(),
      imInfo: z.object({
        externalUserId: z.string().optional(),
        followUser: z.object({
          wecomUserId: z.string()
        })
      }),
      botInfo: z.object({
        botId: z.string(),
        imBotId: z.string(),
        name: z.string(),
        avatar: z.string().url()
      })
    })

    const result = schema.safeParse(data)

    return result.success
  }

  abstract handleFriendAcceptedEvent(data: FriendAcceptedEvent):Promise<void>
}