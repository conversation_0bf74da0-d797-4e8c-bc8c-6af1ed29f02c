import { IWecomMessage, IWecomMsgType } from 'model/juzi/type'
import { UUID } from 'lib/uuid/uuid'
import { Config } from 'config'
import { JuziAPI } from 'model/juzi/api'
import { ChatHistoryService } from '../../chat_history/chat_history'


interface IWechatSendMsg {
    user_id?: string
    room_id?: string // 群聊的 id
    chat_id: string
    ai_msg: string // 放到聊天记录中的文本信息，在发送纯文本情况下与 send_msg 相同。 当发送文件或资料时，此处有可能是占位符，可以单独设置 send_msg 为 [xx文件.txt] 之类。
    send_msg?: IWecomMessage // 发送的非文本信息
}

interface IWechatSendOptions {
  shortDes?: string // 简短描述
  isAnnouncement?: boolean // 群公告
  round_id?: string // 轮次 id, 用于记录模型输出
  sop_id?: string
}

export class WecomMessageSender {
  private chatHistoryServiceClient:ChatHistoryService
  constructor(chatHistoryServiceClient:ChatHistoryService) {
    this.chatHistoryServiceClient = chatHistoryServiceClient
  }

  public async sendById(msg: IWechatSendMsg, options?: IWechatSendOptions) {
    if (!msg.ai_msg || msg.ai_msg.trim() === '') {
      return
    }

    const externalRequestId = UUID.short()

    if (Config.setting.localTest) {
      // add Bot Message 中会 Print 消息
    } else {
      let sendMsg: IWecomMessage
      if (!msg.send_msg) {
        sendMsg =  {
          type: IWecomMsgType.Text,
          text: msg.ai_msg
        }
      } else {
        sendMsg = msg.send_msg
      }

      let isAtAll : boolean| undefined = undefined
      if (sendMsg.type === IWecomMsgType.Text && sendMsg.mention && sendMsg.mention.includes('@all')) {
        delete  sendMsg.mention
        isAtAll = true
      }

      await JuziAPI.sendMsg({
        imBotId: Config.setting.wechatConfig?.id as string,
        imContactId: msg.user_id,
        imRoomId: msg.room_id,
        msg: sendMsg,
        isAtAll: isAtAll,
        isAnnouncement: options?.isAnnouncement,
        externalRequestId: externalRequestId
      })
    }

    if (options?.shortDes && !options?.shortDes.startsWith('[')) {
      options.shortDes = `[${options.shortDes}]`
    }


    await this.chatHistoryServiceClient.addBotMessage(msg.chat_id, msg.ai_msg, options?.shortDes, { round_id: options?.round_id, message_id: externalRequestId, sop_id:options?.sop_id })
  }

}
