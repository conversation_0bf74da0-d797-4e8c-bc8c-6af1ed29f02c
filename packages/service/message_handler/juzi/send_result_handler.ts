import { ISendMessageResult } from 'model/juzi/type'
import { ChatHistoryService } from '../../chat_history/chat_history'

export class SendMessageResultHandler {
  chatHistoryServiceClient:ChatHistoryService
  constructor(chatHistoryServiceClient:ChatHistoryService) {
    this.chatHistoryServiceClient = chatHistoryServiceClient
  }
  public async handle(data: ISendMessageResult) {
    // 将 messageId 中的 externalRequestId 替换为真实的 messageId
    const externalId = data.externalRequestId
    if (!externalId) return

    const message = await this.chatHistoryServiceClient.getMessageByMessageId(externalId)
    if (!message) return

    await this.chatHistoryServiceClient.updateMessageId(message.id, data.messageId)
  }
}