import { MessageReplyService } from '../reply/message_reply'
import { Que<PERSON>, Worker } from 'bullmq'
import { LRUCache } from 'lru-cache'
import { Config } from 'config'
import { getChatId } from 'config/chat_id'
import { RedisCacheDB } from 'model/redis/redis_cache'
import {
  IReceivedImageMsg,
  IReceivedMessage,
  IReceivedMessageSource,
  IReceivedRecallMsg,
  IReceivedTextMsg,
  IReceivedVideoFileMsg,
  IReceivedVoiceMsg,
  IWecomReceivedMsgType
} from 'model/juzi/type'
import logger from 'model/logger/logger'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { sleep } from 'lib/schedule/schedule'
import { DateHelper } from 'lib/date/date'
import XunfeiASR from 'model/nls/xunfei'
import { JuziAPI } from 'model/juzi/api'
import { EventTracker, IEventType } from 'model/logger/data_driven'
import { catchError } from 'lib/error/catchError'
import { ChatInterruptHandler } from '../interrupt/interrupt_handler'
import { ChatStateStore } from '../../local_cache/chat_state_store'
import { ChatDB, IChat } from '../../database/chat'


export interface ImageAndText {
  text?: string
  imageUrl?: string
}

interface IMessageHandlers {
  handleClassGroupMessage?: (message: IReceivedMessage) =>  Promise<any> // 默认不处理
  handleUnknownMessage: (message: IReceivedMessage) => Promise<any>

  handleImageMessage(imageUrl: string, chatId: string): Promise<string> // 返回图片转文字结果
  handleVideoMessage(videoUrl: string, chatId: string): Promise<string> // 返回视频转文字结果

  sendWelcomeMessage(chatId: string, userId: string): Promise<void>

  handleTextAndImageMessage?: (imageAndText: ImageAndText[], chatId: string, userId: string) => Promise<string> // 如果有关于图片的问题，对图文消息进行合并，走LLM多模态分析，直接返回问题回答给客户
  imageWaitingTime?: number // 修改图片单独等待时间， 单位 毫秒
}

/**
 * 用于管理全局消息队列，为每个客户设置单独的计时器，合并处理消息
 */
export class JuziMessageHandler {
  private readonly handlers: IMessageHandlers
  private readonly messageSet = new LRUCache<string, any>({ max: 3000 })
  private _messageQueueBullMQ: Queue
  private messageQueueWorker?: Worker
  private chatDBClient:ChatDB<IChat>
  private chatHistoryServiceClient:ChatHistoryService
  private chatStateStoreClient:ChatStateStore
  private messageReplyServiceClient:MessageReplyService
  private eventTrackClient:EventTracker

  constructor(handlers:IMessageHandlers, chatDBClient:ChatDB<IChat>, chatHistoryServiceClient:ChatHistoryService, chatStateStoreClient:ChatStateStore, messageReplyServiceClient:MessageReplyService, eventTrackClient:EventTracker) {
    this.handlers = handlers
    this.chatDBClient = chatDBClient
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.chatStateStoreClient = chatStateStoreClient
    this.messageReplyServiceClient = messageReplyServiceClient
    this.eventTrackClient = eventTrackClient
  }

  private get messageQueueBullMQ():Queue {
    if (!this._messageQueueBullMQ) {
      this._messageQueueBullMQ = new Queue(this.queueName, {
        connection: RedisCacheDB.getInstance()
      })
    }
    return this._messageQueueBullMQ
  }

  private getMessageStoreName(userId: string) {
    const chatId = getChatId(userId)

    return `user-message-store_${chatId}`
  }

  private get queueName() {
    return `user-message-queue_${Config.setting.wechatConfig!.id}`
  }

  public async handle (message: IReceivedMessage) {
    if (!message.imContactId) {
      return
    }

    // if (!message.isSelf && message.imRoomId && message.imRoomId === Config.setting.wechatConfig?.classGroupId) {
    //   // 班级群消息处理
    //   await this.handlers.handleClassGroupMessage(message)
    //   return
    // }

    if (message.messageId && this.messageSet.has(message.messageId)) {
      return
    }

    // 不处理群消息
    if (message.roomWecomChatId || message.imRoomId) {
      // logger.debug('忽略群消息', message.imRoomId)
      return
    }

    if (message.payload && 'text' in message.payload && message.payload.text) {
      logger.debug('接收消息', message.payload.text, message.imContactId)
      // 文本消息，提前 increment 一下 version 来即时打断
      await ChatInterruptHandler.incrementChatVersion(getChatId(message.imContactId))
    }

    if (message.messageId) {
      this.messageSet.set(message.messageId, 1)
    }

    const senderId = message.imContactId
    const messageId = message.messageId
    if (!messageId) {
      return
    }

    // 如果是账号挂上之前的老客户不进行回复
    if (await JuziMessageHandler.isPastUser(message.imContactId)) {
      return
    }


    try {
      const messageStore = new RedisCacheDB(this.getMessageStoreName(senderId)) // 改为 message_store + chatId 来存储，否则可能有 key 冲突问题
      // 将消息存到 Redis
      await messageStore.addSet(JSON.stringify(message))

      let delay =  Config.setting.waitingTime.messageMerge
      if (Config.isTestAccount()) {
        delay = 5 * 1000
      } else if (message.isSelf) {
        delay = 0
      } else if (message.messageType === IWecomReceivedMsgType.Image) { // 单独设置 图片等待时长
        delay = this.handlers.imageWaitingTime ? this.handlers.imageWaitingTime : delay
      }

      // 添加消息，到消息队列
      await this.messageQueueBullMQ.add(senderId, { userId: senderId, messageId }, { delay, removeOnComplete: true, removeOnFail: true })
    } catch (e) {
      logger.error('消息添加到任务队列失败', e)
    }
  }

  public startWorker() {
    if (!this.messageQueueWorker) {
      this.messageQueueWorker = new Worker(this.queueName, async (job) => {
        const { userId, messageId } = job.data

        // 只处理最新的消息
        const isLatest = await this.isLatestMessage(userId, messageId)
        if (!isLatest) {
          logger.debug(`跳过非最新消息: ${messageId}`)
          return
        }

        // 如果最后一条消息 是 AI 消息，并跟现在的时间比较接近，则做下延迟处理，拖延一下回复速度
        const messages =  await this.chatHistoryServiceClient.getChatHistoryByChatId(getChatId(userId))
        if (messages.length > 0) {
          const lastAIMessageReplyDiff = DateHelper.diff(messages[messages.length - 1].created_at, new Date(), 'second')
          if (lastAIMessageReplyDiff < 5) {
            await sleep(5 * 1000)
          }
        }

        await this.processUserMessages(userId)
      }, { connection: RedisCacheDB.getInstance(), concurrency: 20 })

      this.messageQueueWorker.on('error', (error) => {
        logger.error('消息处理 Worker error:', error)
      })
    }
  }

  private async isLatestMessage(userId: string, messageId: string) {
    const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
    const messages = await messageStore.getSetMembers()

    if (!messages || messages.length === 0) {
      return false
    }

    // 消息有可能不是按顺序接收的，需要按时间重排序下
    messages.sort((msgA, msgB) => {
      return msgA.timestamp - msgB.timestamp
    })

    return messages[messages.length - 1].messageId === messageId
  }

  public async processUserMessages (userId: string) {
    try {
      // 获取该客户在这段时间内发送的所有消息
      const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
      const userMessages = await messageStore.getSetMembers()

      // 消息有可能不是按顺序接收的，需要按时间重排序下
      userMessages.sort((msgA, msgB) => {
        return msgA.timestamp - msgB.timestamp
      })

      // 从消息队列中移除已处理的消息
      await messageStore.del()

      // 将消息转为文本
      const texts = await this.getTextsFromMessages (userMessages, userId)
      if (texts.length == 0) return

      await this.messageReplyServiceClient.reply(texts, userId)
    } catch (e) {
      console.error (`处理客户 ${userId} 的消息出错：`, e)
    }
  }

  private async getTextsFromMessages(messages: IReceivedMessage[], userId: string): Promise<string[]> {
    const texts : string[] = []
    const ignoreMessage = false // 是否忽略当前消息


    const requireMultiModal = Boolean(this.handlers.handleTextAndImageMessage) // 目前只有好人好股需要这个功能
    const imageAndText: ImageAndText[] = []


    for (const message of messages) {
      try {
        if (message.messageId) {
          const cache = new RedisCacheDB(message.messageId)
          await cache.set(message, 3 * 60 * 60) // 缓存 3小时
        }
      } catch (e) {
        logger.warn('缓存消息失败')
      }


      try {
        let text: string = ''
        const isSendByOpenAPI = message.source === IReceivedMessageSource.APIMessage
        if (isSendByOpenAPI) {
          // 忽略当前 AI 发送的消息
          continue
        }

        // 忽略系统通知
        if ([IWecomReceivedMsgType.SystemMessage, IWecomReceivedMsgType.WeChatWorkSystemMessage].includes(message.messageType)) {
          continue
        }

        if (message.coworker && !message.isSelf) { // 企业内员工发送的不进行处理
          break
        }

        // 忽略欢迎语
        if (message.messageType === IWecomReceivedMsgType.Text && (JuziMessageHandler.isWelcomeMessage((message.payload as IReceivedTextMsg).text as string) || await this.isFirstMessage(userId))) {
          const chatId = getChatId(userId)
          const flags = await this.chatStateStoreClient.getFlags<any>(chatId)
          if (flags.is_friend_accepted) {
            continue
          }

          // 如果没有 欢迎语的话，进入欢迎语流程
          await this.handlers.sendWelcomeMessage(getChatId(userId), userId)
          continue
        }

        switch (message.messageType) {
          case IWecomReceivedMsgType.Text: {
            const payload = message.payload as IReceivedTextMsg
            text = payload.text

            if (payload.quoteMessage) {
              if (!payload.quoteMessage.content.text) { // 引用非文本，查一下 对应的附件的注释
                // 查询一下 messageId 对应的 chatHistory
                const chatHistory = await this.chatHistoryServiceClient.getMessageByMessageId(payload.quoteMessage.messageId)

                if (chatHistory && chatHistory.short_description) {
                  text = `对“${chatHistory.short_description}”的回复是：\n${payload.text}`
                } else {
                  text =  `${payload.text}`
                }

              } else { // 引用文本回复
                text = `对“${payload.quoteMessage.content.text}”的回复是：\n${payload.text}`
              }
            }
            break
          }

          case IWecomReceivedMsgType.Emoticon:
            text = '【表情】'
            break

          case IWecomReceivedMsgType.Voice: {
            const msg = message.payload as IReceivedVoiceMsg
            if (msg.text) {
              text = msg.text
            } else {
              const xunfei = new XunfeiASR({
                appId: Config.setting.xunfeiASR.appId,
                secretKey: Config.setting.xunfeiASR.secretKey,
                uploadFileUrl: msg.voiceUrl
              })
              text = await xunfei.getResult()
            }
            break
          }

          case IWecomReceivedMsgType.MessageRecall: {
            // 埋点，数据库标记
            if (message.isSelf) {
              const userId = await JuziAPI.externalIdToWxId(message.customerExternalUserId as string, Config.setting.wechatConfig?.id as string)
              const chatId = getChatId(userId as string)
              const payload = message.payload as (IReceivedRecallMsg | undefined)
              if (payload) {
                const messageId = payload.content
                const cache = new RedisCacheDB(messageId)
                const originalMessage = await cache.get()

                const message = originalMessage?.payload?.text
                if (message !== undefined) {
                  this.eventTrackClient.track(chatId, IEventType.ManualReply, { type: '撤回', msg: message })
                }
                await catchError(this.chatHistoryServiceClient.setMessageRecalled(messageId)) // 更新 聊天记录中的消息撤回标签
              }
            }
            break
          }

          case IWecomReceivedMsgType.Unknown:
          case IWecomReceivedMsgType.VoiceOrVideoCall: {
            text = '【语音/视频通话】'
            break
          }

          case IWecomReceivedMsgType.Image: {
            // 当isHandleImageAndText为true（好人好股）时，只把图片放在imageAndText数组中，返回的text为空字符串。
            text = await this.handleImageMessage(userId, getChatId(userId), message, requireMultiModal,  imageAndText) ?? text
            break
          }

          case IWecomReceivedMsgType.Video: {
            text = await this.handleVideoMessage(userId, getChatId(userId), message) ?? text
            break
          }

          default:
            logger.log(JSON.stringify(message, null, 4))
            await this.handleUnknownMessageType(message)
        }

        // 客服或者手机端客服侧人工回复的消息，不处理，但是需要存一下
        if (message.isSelf && message.source !== undefined && [IReceivedMessageSource.MobilePush, IReceivedMessageSource.TeamConsoleManual].includes(message.source)) {
          logger.log('人工回复', JSON.stringify(message, null, 4))
          const userId = await JuziAPI.externalIdToWxId(message.customerExternalUserId as string,  Config.setting.wechatConfig?.id as string)
          const chatId = getChatId(userId as string)

          // 添加埋点，客户 + AI 回复
          if (text) {
            await this.chatHistoryServiceClient.addBotMessage(chatId, text, undefined, { is_send_by_human:true, message_id: message.messageId })
            this.eventTrackClient.track(chatId, IEventType.ManualReply, { reply: text, message: message.source })
          }
        } else {
          if (requireMultiModal) {
            imageAndText.push({
              text
            })
          }

          texts.push(text)
        }
      } catch (e) {
        // 避免消息处理过程中出错导致程序崩溃
        console.error('单条消息解析出错：', e)
      }
    }

    if (ignoreMessage) {
      return []
    }

    if (requireMultiModal && this.handlers.handleTextAndImageMessage && imageAndText.some((e) => Boolean(e.imageUrl))) {
      const chatId = getChatId(userId)
      const analysisResult = await this.handlers.handleTextAndImageMessage(imageAndText, chatId, userId)
      if (analysisResult && analysisResult.trim().length > 0) {
        return [analysisResult]
      }
      return []
    }

    return texts
  }

  public async handleUnknownMessageType(message: IReceivedMessage) {
    if (!message.imContactId)
      return

    const chat_id = getChatId(message.imContactId)
    logger.log(chat_id, '非文本消息类型', message.messageType)

    if (await this.chatDBClient.isHumanInvolvement(chat_id)) { // 已经人工参与了，不再处理
      return
    }

    await this.handlers.handleUnknownMessage(message)
  }

  private static isWelcomeMessage(msg: string) {
    if (msg.includes('我通过了你的联系人验证请求，现在我们可以开始聊天了') || msg.includes('我已经添加了你，现在我们可以开始聊天了。') || msg.includes('请求添加你为朋友') || msg.includes('我通过了你的朋友验证请求，现在我们可以开始聊天了'))
      return true

    const welcomeMsgRegex = /^我是.{1,10}$/
    return welcomeMsgRegex.test(msg)
  }

  private async isFirstMessage(userId: string) {
    const chatId = getChatId(userId)

    return await this.chatHistoryServiceClient.getUserMessageCount(chatId) === 0
  }

  private async handleImageMessage(userId: string, chatId: string, message: IReceivedMessage, requireMultiModal?: boolean, imageAndText?: ImageAndText[]): Promise<string> {
    if (message.isSelf || message.coworker || userId.startsWith('1688')) { // 自己发送的图片或员工发送的都不处理
      return ''
    }

    const originalImageResponse = await JuziAPI.getOriginalImage(message.chatId, message.messageId as string)
    let originalImageUrl: string

    if (originalImageResponse.code !== 0) {
      originalImageUrl = (message.payload as IReceivedImageMsg).imageUrl
    } else {
      originalImageUrl = originalImageResponse.data.url
    }

    // if (requireMultiModal && imageAndText) { // 交给后续图文 handler 统一处理，这里只做消息 push
    //   imageAndText.push({
    //     imageUrl: originalImageUrl
    //   })
    //
    //   return ''
    // }

    return this.handlers.handleImageMessage(originalImageUrl, chatId)
  }

  private async handleVideoMessage(userId: string, chatId: string, message: IReceivedMessage) {
    if (message.isSelf || message.coworker || userId.startsWith('1688')) { // 自己发送的图片或员工发送的都不处理
      return
    }

    // message中的payload有视频url
    const videoUrl = (message.payload as IReceivedVideoFileMsg).videoUrl
    return this.handlers.handleVideoMessage(videoUrl, chatId)
  }

  public static async isPastUser(senderId: string) {
    try {
      const accountCreatedTime = Config.setting.wechatConfig?.createdTime // 挂号时间

      if (!accountCreatedTime) {
        return false
      }

      if (senderId === Config.setting.wechatConfig?.id) {
        return false
      }

      const userInfo = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, senderId)
      if (!userInfo) {
        return false
      }

      if (['麦子', 'SYQ'].includes(userInfo.name)) {
        return false
      }

      if ((userInfo && Number(userInfo.createTimestamp) < accountCreatedTime.getTime())) {
        logger.warn({ chat_id: getChatId(senderId) }, `客户在 ${accountCreatedTime.toLocaleString()} 之前创建`, senderId)
        return true
      }

      return false
    } catch (e) {
      return false
    }
  }
}