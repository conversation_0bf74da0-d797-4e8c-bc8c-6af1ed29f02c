/**
 * UTF-8表情符号与中文表情标签的映射关系
 */

export interface EmojiMapping {
  utf8: string;
  label: string;
}

/**
 * UTF-8表情映射到GIF表情标签
 */
export const UTF8_TO_GIF_EMOJI_MAPPING: EmojiMapping[] = [
  { utf8: '😀', label: '[愉快]' },
  { utf8: '😄', label: '[愉快]' },
  { utf8: '😊', label: '[愉快]' },
  { utf8: '🙃', label: '[撇嘴]' },
  { utf8: '😏', label: '[色]' },
  { utf8: '😐', label: '[发呆]' },
  { utf8: '😴', label: '[睡]' },
  { utf8: '😎', label: '[得意]' },
  { utf8: '😢', label: '[流泪]' },
  { utf8: '😭', label: '[大哭]' },
  { utf8: '😳', label: '[害羞]' },
  { utf8: '😶', label: '[闭嘴]' },
  { utf8: '😅', label: '[尴尬]' },
  { utf8: '😠', label: '[发怒]' },
  { utf8: '😡', label: '[发怒]' },
  { utf8: '😜', label: '[调皮]' },
  { utf8: '😛', label: '[调皮]' },
  { utf8: '😁', label: '[呲牙]' },
  { utf8: '😲', label: '[惊讶]' },
  { utf8: '😮', label: '[惊讶]' },
  { utf8: '☹️', label: '[难过]' },
  { utf8: '😔', label: '[难过]' },
  { utf8: '😰', label: '[冷汗]' },
  { utf8: '😱', label: '[抓狂]' },
  { utf8: '🤮', label: '[吐]' },
  { utf8: '🤭', label: '[偷笑]' },
  { utf8: '😍', label: '[可爱]' },
  { utf8: '🥰', label: '[可爱]' },
  { utf8: '🙄', label: '[白眼]' },
  { utf8: '😤', label: '[傲慢]' },
  { utf8: '🤤', label: '[饥饿]' },
  { utf8: '😪', label: '[困]' },
  { utf8: '😨', label: '[惊恐]' },
  { utf8: '😓', label: '[流汗]' },
  { utf8: '😆', label: '[憨笑]' },
  { utf8: '🪖', label: '[大兵]' },
  { utf8: '💪', label: '[奋斗]' },
  { utf8: '🤬', label: '[咒骂]' },
  { utf8: '🤔', label: '[疑问]' },
  { utf8: '🤫', label: '[嘘]' },
  { utf8: '😵', label: '[晕]' },
  { utf8: '😩', label: '[折磨]' },
  { utf8: '😖', label: '[衰]' },
  { utf8: '💀', label: '[骷髅]' },
  { utf8: '🔨', label: '[敲打]' },
  { utf8: '👋', label: '[再见]' },
  { utf8: '💦', label: '[擦汗]' },
  { utf8: '👃', label: '[抠鼻]' },
  { utf8: '👏', label: '[鼓掌]' },
  { utf8: '🙈', label: '[糗大了]' },
  { utf8: '😈', label: '[坏笑]' },
  { utf8: '🙄', label: '[左哼哼]' },
  { utf8: '🙄', label: '[右哼哼]' },
  { utf8: '🥱', label: '[哈欠]' },
  { utf8: '🤨', label: '[鄙视]' },
  { utf8: '🥺', label: '[委屈]' },
  { utf8: '😿', label: '[快哭了]' },
  { utf8: '😈', label: '[阴险]' },
  { utf8: '😘', label: '[亲亲]' },
  { utf8: '😨', label: '[吓]' },
  { utf8: '🥺', label: '[可怜]' },
  { utf8: '🔪', label: '[菜刀]' },
  { utf8: '🍉', label: '[西瓜]' },
  { utf8: '🍺', label: '[啤酒]' },
  { utf8: '🏀', label: '[篮球]' },
  { utf8: '🏓', label: '[乒乓]' },
  { utf8: '☕', label: '[咖啡]' },
  { utf8: '🍚', label: '[饭]' },
  { utf8: '🐷', label: '[猪头]' },
  { utf8: '🌹', label: '[玫瑰]' },
  { utf8: '🥀', label: '[凋谢]' },
  { utf8: '💖', label: '[示爱]' },
  { utf8: '❤️', label: '[爱心]' },
  { utf8: '💔', label: '[心碎]' },
  { utf8: '🍰', label: '[蛋糕]' },
  { utf8: '⚡', label: '[闪电]' },
  { utf8: '💣', label: '[炸弹]' },
  { utf8: '🗡️', label: '[刀]' },
  { utf8: '⚽', label: '[足球]' },
  { utf8: '🐞', label: '[瓢虫]' },
  { utf8: '💩', label: '[便便]' },
  { utf8: '🌙', label: '[月亮]' },
  { utf8: '☀️', label: '[太阳]' },
  { utf8: '🎁', label: '[礼物]' },
  { utf8: '🤗', label: '[拥抱]' },
  { utf8: '👍', label: '[强]' },
  { utf8: '👎', label: '[弱]' },
  { utf8: '🤝', label: '[握手]' },
  { utf8: '✌️', label: '[胜利]' },
  { utf8: '🙏', label: '[抱拳]' },
  { utf8: '🫵', label: '[勾引]' },
  { utf8: '👊', label: '[拳头]' },
  { utf8: '👎', label: '[差劲]' },
  { utf8: '🫶', label: '[爱你]' },
  { utf8: '🙅', label: '[NO]' },
  { utf8: '👌', label: '[OK]' },
  { utf8: '💘', label: '[爱情]' },
  { utf8: '😗', label: '[飞吻]' },
  { utf8: '🦘', label: '[跳跳]' },
  { utf8: '🥶', label: '[发抖]' },
  { utf8: '😠', label: '[怄火]' },
  { utf8: '🌀', label: '[转圈]' },
  { utf8: '🙇', label: '[磕头]' },
  { utf8: '🔄', label: '[回头]' },
  { utf8: '🪢', label: '[跳绳]' },
  { utf8: '👋', label: '[挥手]' },
  { utf8: '🤩', label: '[激动]' },
  { utf8: '💃', label: '[街舞]' },
  { utf8: '💋', label: '[献吻]' },
  { utf8: '☯️', label: '[左太极]' },
  { utf8: '☯️', label: '[右太极]' }
]

/**
 * UTF-8表情映射到PNG表情标签
 */
export const UTF8_TO_PNG_EMOJI_MAPPING: EmojiMapping[] = [
  { utf8: '🤦', label: '[捂脸]' },
  { utf8: '😅', label: '[囧]' },
  { utf8: '😊', label: '[愉快]' },
  { utf8: '😌', label: '[悠闲]' },
  { utf8: '😃', label: '[笑脸]' },
  { utf8: '🤒', label: '[生病]' },
  { utf8: '😂', label: '[破涕为笑]' },
  { utf8: '😰', label: '[恐惧]' },
  { utf8: '😞', label: '[失望]' },
  { utf8: '😑', label: '[无语]' },
  { utf8: '😎', label: '[嘿哈]' },
  { utf8: '😈', label: '[奸笑]' },
  { utf8: '🤓', label: '[机智]' },
  { utf8: '😟', label: '[皱眉]' },
  { utf8: '✌️', label: '[耶]' },
  { utf8: '🍉', label: '[吃瓜]' },
  { utf8: '💪', label: '[加油]' },
  { utf8: '🎆', label: '[烟花]' },
  { utf8: '🧨', label: '[爆竹]' },
  { utf8: '🉐', label: '[福]' },
  { utf8: '💰', label: '[发财]' },
  { utf8: '🧧', label: '[红包]' },
  { utf8: '🎉', label: '[庆祝]' },
  { utf8: '🙏', label: '[合十]' },
  { utf8: '👄', label: '[嘴唇]' },
  { utf8: '💔', label: '[裂开]' },
  { utf8: '😣', label: '[苦涩]' },
  { utf8: '😔', label: '[叹气]' },
  { utf8: '👀', label: '[让我看看]' },
  { utf8: '👌', label: '[666]' },
  { utf8: '🙄', label: '[翻白眼]' },
  { utf8: '😮', label: '[哇]' },
  { utf8: '🤚', label: '[打脸]' },
  { utf8: '👍', label: '[好的]' },
  { utf8: '🐶', label: '[旺柴]' },
  { utf8: '😏', label: '[社会社会]' },
  { utf8: '🤨', label: '[Emm]' },
  { utf8: '😱', label: '[天啊]' },
  { utf8: '😓', label: '[汗]' }
]


/**
 * 根据UTF-8表情符号查找对应的中文标签
 */
export function findLabelByUtf8Emoji(utf8Emoji: string): string | null {
  // 首先在GIF映射中查找
  const gifMatch = UTF8_TO_GIF_EMOJI_MAPPING.find((item) => item.utf8 === utf8Emoji)
  if (gifMatch) return gifMatch.label

  // 然后在PNG映射中查找
  const pngMatch = UTF8_TO_PNG_EMOJI_MAPPING.find((item) => item.utf8 === utf8Emoji)
  if (pngMatch) return pngMatch.label

  return null
}

/**
 * 根据中文标签查找对应的UTF-8表情符号
 */
export function findUtf8EmojiByLabel(label: string): string | null {
  // 首先在GIF映射中查找
  const gifMatch = UTF8_TO_GIF_EMOJI_MAPPING.find((item) => item.label === label)
  if (gifMatch) return gifMatch.utf8

  // 然后在PNG映射中查找
  const pngMatch = UTF8_TO_PNG_EMOJI_MAPPING.find((item) => item.label === label)
  if (pngMatch) return pngMatch.utf8

  return null
}

/**
 * 将字符串中的UTF-8表情转换为中文标签，找不到映射的表情将被删除
 */
export function convertUtf8EmojisToLabels(text: string): string {
  // 使用正则表达式匹配所有emoji字符
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F0FF}]|[\u{1F100}-\u{1F14F}]|[\u{1F150}-\u{1F167}]|[\u{1F170}-\u{1F251}]|[\u{1F004}]|[\u{1F0CF}]|[\u{1F18E}]|[\u{3030}]|[\u{2B50}]|[\u{2B55}]|[\u{2934}]|[\u{2935}]|[\u{2B05}-\u{2B07}]|[\u{2B1B}]|[\u{2B1C}]|[\u{3297}]|[\u{3299}]|[\u{303D}]|[\u{00A9}]|[\u{00AE}]|[\u{2122}]|[\u{23F3}]|[\u{24C2}]|[\u{23E9}-\u{23EF}]|[\u{25B6}]|[\u{23F8}-\u{23FA}]|[\u{200D}]/gu

  return text.replace(emojiRegex, (emoji) => {
    const label = findLabelByUtf8Emoji(emoji)
    return label || '' // 如果找到映射返回标签，否则返回空字符串（删除）
  })
}

/**
 * 获取所有映射关系
 */
export function getAllEmojiMappings(): EmojiMapping[] {
  return [
    ...UTF8_TO_GIF_EMOJI_MAPPING,
    ...UTF8_TO_PNG_EMOJI_MAPPING
  ]
}