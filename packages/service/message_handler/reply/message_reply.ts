import chalk from 'chalk'
import dayjs from 'dayjs'
import logger from 'model/logger/logger'
import { getChatId } from 'config/chat_id'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { sleep } from 'lib/schedule/schedule'
import { Config } from 'config'
import { ChatDB, IChat } from '../../database/chat'
import { replyHistogram } from './prometheus'
import { BaseWorkFlow } from '../../agent/workflow'

export class MessageReplyService {
  workflow: typeof BaseWorkFlow
  chatDBClient:ChatDB<IChat>
  chatHistoryServiceClient:ChatHistoryService

  constructor(workflow: typeof BaseWorkFlow, chatDBClient:ChatDB<IChat>, chatHistoryServiceClient:ChatHistoryService) {
    this.workflow = workflow
    this.chatDBClient = chatDBClient
    this.chatHistoryServiceClient = chatHistoryServiceClient
  }

  public async reply(text: string[], senderId: string) {
    const prevTime = dayjs()
    const chat_id = getChatId(senderId)
    const chat = await this.chatDBClient.getById(chat_id) as IChat
    let contactName = chat_id
    if (chat && chat.contact && chat.contact.wx_name) { contactName = chat.contact.wx_name }

    try {
      if (!text.join('').trim()) { return }

      if (!chat) { return }

      let userMessage = text.map((msg) => (msg.length > 500 ? `${msg.slice(0, 500)  }...` : msg)).join('\n')
      if (userMessage.length > 1500) { userMessage = userMessage.slice(-1500) }

      if (Config.setting.onlyReceiveMessage) { // mode
        console.log(chalk.redBright('仅接收消息模式'))
        await this.chatHistoryServiceClient.addUserMessage(chat_id, userMessage)
        return
      }

      // 添加聊天日志
      if (await this.chatDBClient.isHumanInvolvement(chat_id)) { // 人工参与，则不进行回复
        logger.log(chat_id, '联系人交给人工处理')
        // 存储下客户消息
        await this.chatHistoryServiceClient.addUserMessage(chat_id, userMessage)
        const courseNo = await this.chatDBClient.getCourseNo(chat_id)
        await this.workflow.humanInvolveGroupNotify(contactName, courseNo, userMessage)
        return
      }

      // 下面开始处理消息
      if (userMessage.length >= 100) { // 客户发送的消息比较长的话，mock 一下人工阅读的时间
        await sleep(10 * 1000) // 延缓输出
      }

      await this.workflow.step(chat_id, senderId, userMessage)
      const now = dayjs()
      const diffSecond = now.diff(prevTime, 'second')
      replyHistogram.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).observe(diffSecond)
    } catch (e) {
      logger.error(e)
    }
  }
}