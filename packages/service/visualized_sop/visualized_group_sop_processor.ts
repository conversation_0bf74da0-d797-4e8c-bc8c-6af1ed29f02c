import { Job, Worker } from 'bullmq'
import { Sema } from 'async-sema'
import { Config } from 'config'
import logger from 'model/logger/logger'
import { RedisDB } from 'model/redis/redis'
import { AsyncLock } from 'model/lock/lock'
import { ActionType, ContentCustom, ContentFile, ContentImage, ContentLink, ContentModifyGroupAnnouncement, ContentTextPlain, ContentVideo, ContentVideoChannel, ContentVoice, GroupAction, GroupCondition, GroupSop, IGroupTask, ISendMedia, LinkSourceType, TextType } from './visualized_sop_type'
import { IWecomMsgType } from 'model/juzi/type'
import { commonSleep, sleep } from 'lib/schedule/schedule'
import { getVisualizedGroupSopQueueName, VisualizedGroupSopTasks } from './visualized_group_sop_task_starter'
import { WecomMessageSender } from '../message_handler/juzi/message_sender'
import { PrismaClient } from 'model/prisma_client'
import { groupSopActionProcessCounter, groupSopActionProcessErrorCounter, groupSopProcessCounter, groupSopProcessErrorCounter } from './prometheus'

export const sema = new Sema(10)

export abstract class VisualizedGroupSopProcessor {

  abstract getActionCustomMap():Record<string, (params:{groupId:string})=> Promise<void>>
  abstract getConditionJudgeMap():Record<string, ((params:{groupId:string})=>Promise<boolean>)>
  abstract getLinkSourceVariableTagMap():Record<string, (params:{groupId:string})=>Promise<string>>
  abstract getTextVariableMap():Record<string, (params:{groupId:string})=> Promise<string>>

  mongoClient:PrismaClient
  enterPriseName:string
  wecomMessageSender:WecomMessageSender

  constructor(enterPriseName:string, mongoClient:PrismaClient, wecomMessageSender:WecomMessageSender) {
    this.enterPriseName = enterPriseName
    this.mongoClient = mongoClient
    this.wecomMessageSender = wecomMessageSender
  }

  public start() {
    if (!Config.setting.wechatConfig?.id) {
      throw ('获取wx bot id错误')
    }
    new Worker(getVisualizedGroupSopQueueName(this.enterPriseName, Config.setting.wechatConfig.id), async (job: Job<IGroupTask>) => {
      try {
        groupSopProcessCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
        await this.generalProcess(this.enterPriseName, job)
      } catch (e) {
        groupSopProcessErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
        logger.error(`任务执行出错: ${job.name} ${job.data} ${e}`)
      }
    }, { connection: RedisDB.getInstance(),
      lockDuration: 60 * 1000,
      concurrency: 30
    })
  }

  public async generalProcess(enterPriseName:string, job: Job<IGroupTask>) {
    if (job.opts.delay && job.opts.delay < 0) {
      // logger.warn(`任务 ${job.name} 超时 ${-job.opts.delay} 毫秒，不进行处理`)
      return
    }
    const groupId = job.data.groupId
    const groupInfo = await this.mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      throw (`在处理sop的时候没有找到groupInfo group_id:${groupId}`)
    }
    if (groupInfo.owner_account_id != Config.setting.wechatConfig?.id) {
      throw (`在处理sop的时候wechatConfig.id ${Config.setting.wechatConfig?.id} != owner_account_id:${groupInfo.owner_account_id}`)
    }
    // 加锁，防止在 主动回复中间插入消息
    const lock = new AsyncLock()
    try {
      await sema.acquire()
      await lock.acquire(job.data.groupId, async () => {
        const task = job.data
        await this.handleGroupSopBySopId(enterPriseName, task.groupId, task.name)
      }, { timeout: 60 * 1000 })
    } catch (e) {
      groupSopProcessErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
      logger.error(e)
    } finally {
      sema.release()
    }
  }

  public async handleGroupSopBySopId(enterPriseName:string, groupId:string, sopId:string) {
    const sops = await VisualizedGroupSopTasks.getTasks(enterPriseName, groupId)
    const filteredSop = sops.filter((item) => {
      if (item.id != sopId) return false
      return true
    })
    for (const sop of filteredSop) {
      // 将所有条件取出来一次性判断
      await this.handleSop(groupId, sop)
    }
  }

  public  async handleSop(groupId:string, sop:GroupSop) {
    logger.log(`群${groupId}执行sop${sop.id} ${sop.title} ${sop.week}周${sop.day}天${sop.time}`)
    const conditionFixedTypes = new Set<string>()
    for (const { conditions } of sop.situations) {
      for (const condition of conditions) {
        conditionFixedTypes.add(condition.condition)
      }
    }
    const conditionsFixed = [...conditionFixedTypes]
    const conditionFixedMap = new Map<string, boolean>()
    for (const condition of conditionsFixed) {
      conditionFixedMap.set(condition, await this.judgeFixedCondition(groupId, condition))
    }
    logger.log(`群${groupId}执行sop${sop.id} 时候固定条件是 ${JSON.stringify(Object.fromEntries(conditionFixedMap))})}`)

    for (const situation of sop.situations) {
      if (!this.judgeConditions(situation.conditions, conditionFixedMap)) continue
      for (const action of situation.action) {
        await this.handleAction(groupId, action as GroupAction, { sop_id:sop.id })
        await commonSleep()
      }
    }
  }

  public async judgeFixedCondition(groupId:string, condition:string):Promise<boolean> {
    const func = this.getConditionJudgeMap()[condition]
    if (!func) throw (`判断条件没有这个键${condition}`)
    return await func({ groupId })
  }

  public judgeConditions(conditions:GroupCondition[], conditionFixedMap:Map<string, boolean>) {
    for (const condition of conditions) {
      const conditionMap = conditionFixedMap
      if (!conditionMap.has(condition.condition)) {
        logger.log('没有这个条件')
        return false
      }
      if ((condition.isOrNotIs && !conditionMap.get(condition.condition)) || (!condition.isOrNotIs && conditionMap.get(condition.condition))) {
        return false
      }
    }
    return true
  }

  public async handleAction(groupId:string, action:GroupAction, opt:HandleGroupActionOption) {
    groupSopActionProcessCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
    try {
      if (action.type == ActionType.text) {
        await this.handleActionText(groupId, action, opt)
      } else if (action.type == ActionType.image) {
        await this.handleActionImage(groupId, action, opt)
      } else if (action.type == ActionType.video) {
        await this.handleActionVideo(groupId, action, opt)
      } else if (action.type == ActionType.file) {
        await this.handleActionFile(groupId, action, opt)
      } else if (action.type == ActionType.voice) {
        await this.handleActionVoice(groupId, action, opt)
      } else if (action.type == ActionType.custom) {
        await this.handleActionCustom(groupId, action)
      } else if (action.type == ActionType.link) {
        await this.handleActionLink(groupId, action, opt)
      } else if (action.type == ActionType.videoChannel) {
        await this.handleActionVideoChannel(groupId, action, opt)
      } else if (action.type == ActionType.modifyGroupAnnouncement) {
        await this.handleActionModifyGroupAnnouncement(groupId, action, opt)
      } else {
        throw ('unknow action type')
      }
    } catch (e) {
      groupSopActionProcessErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
      logger.error(`群${groupId} sop的时候action出错${e}`)
    }
  }

  public async handleActionText(groupId:string, action: ContentTextPlain, opt:HandleGroupActionOption) {
    let text = ''
    for (const item of action.textList) {
      if (item.type == TextType.fixed) {
        text += item.text
      } else {
        text += await this.getTextVariable(groupId, item.tag)
      }
    }
    await this.sendGroupMsg(groupId, text.trim(), action.description)
  }

  public async getTextVariable(groupId:string, variableTag:string):Promise<string> {
    const func = this.getTextVariableMap()[variableTag]
    if (!func) throw (`字符串变量没有这个键${variableTag}`)
    return await func({ groupId })
  }

  public async handleActionImage(groupId:string, action: ContentImage, opt:HandleGroupActionOption) {
    await this.sendGroupMsg(groupId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.Image,
        url:action.url
      },
    }], action.description)
  }

  public async handleActionVideo(groupId:string, action: ContentVideo, opt:HandleGroupActionOption) {
    await this.sendGroupMsg(groupId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.Video,
        url:action.url
      }
    }], action.description)
  }

  public async handleActionVoice(groupId:string, action: ContentVoice, opt:HandleGroupActionOption) {
    await this.sendGroupMsg(groupId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.Voice,
        voiceUrl:action.url,
        duration:action.duration
      }
    }], action.description)
  }

  public async handleActionFile(groupId:string, action: ContentFile, opt:HandleGroupActionOption) {
    await this.sendGroupMsg(groupId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.File,
        name:action.name,
        url:action.url
      }
    }], action.description)
  }

  public async handleActionCustom(groupId:string, action: ContentCustom):Promise<void> {
    const func = this.getActionCustomMap()[action.tag]
    if (!func) throw (`自定义事件没有这个键${action.tag}`)
    return await func({ groupId })
  }

  public async handleActionLink(groupId:string, action: ContentLink, opt:HandleGroupActionOption) {
    let sourceLink = ''
    if (action.source.type == LinkSourceType.fixed) {
      sourceLink = action.source.url
    } else if (action.source.type == LinkSourceType.variable) {
      const getSourceLinkFunc = this.getLinkSourceVariableTagMap()[action.source.tag]
      if (!getSourceLinkFunc) throw (`自定义事件没有这个键${action.source.tag}`)
      sourceLink = await getSourceLinkFunc({ groupId })
    } else {
      throw ('unknow link source type')
    }
    await this.sendGroupMsg(groupId, [<ISendMedia>{
      description: action.description,
      msg: {
        type: IWecomMsgType.Link,
        sourceUrl: sourceLink,
        title: action.title,
        summary: action.summary,
        imageUrl:action.imageUrl
      }
    }], action.description)
  }

  public async handleActionVideoChannel(groupId:string, action:ContentVideoChannel, opt: HandleGroupActionOption) {
    await this.sendGroupMsg(groupId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.VideoChannel,
        avatarUrl: action.avatarUrl,
        coverUrl: action.coverUrl,
        description: action.contentDescription,
        feedType:4,
        nickname: action.nickname,
        thumbUrl: action.thumbUrl,
        url: action.url,
        extras: action.extras
      }
    }], action.description)
  }

  public async handleActionModifyGroupAnnouncement(groupId:string, action:ContentModifyGroupAnnouncement, opt:HandleGroupActionOption) {
    await this.wecomMessageSender.sendById({
      room_id: groupId,
      chat_id: groupId,
      ai_msg: action.content,
    }, {
      shortDes: action.description ? `[${action.description}]` : undefined,
      isAnnouncement:true
    })
  }
  public async sendGroupMsg(groupId: string,  messages: (ISendMedia | string)[] | string, description?: string): Promise<void> {
    if (typeof messages === 'string') {
      await this.wecomMessageSender.sendById({
        room_id: groupId,
        chat_id: groupId,
        ai_msg: messages,
      }, {
        shortDes: description ? `[${description}]` : undefined,
      })
      return
    }

    let waitTime = 0
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i]
      if (typeof message === 'string') {
        waitTime = message.length * 150

        await this.wecomMessageSender.sendById({
          room_id: groupId,
          chat_id: groupId,
          ai_msg: message,
        }, {
          shortDes: `[${  description  }]`
        })
      } else {
        await this.wecomMessageSender.sendById({
          room_id: groupId,
          chat_id: groupId,
          ai_msg: `[${message.description}]`,
          send_msg: message.msg,
        }, {
          shortDes: `[${  description  }]`
        })

        // 对于图片消息，固定等待时间 20s
        waitTime = 20 * 1000
        if (message.msg.type === IWecomMsgType.Text) {
          waitTime = message.msg.text.length * 150
        }
      }

      if (i < messages.length - 1) {
      // 等待指定的时间
        await sleep(waitTime)
      }
    }
  }
}

export interface HandleGroupActionOption {
  sop_id?:string
}
