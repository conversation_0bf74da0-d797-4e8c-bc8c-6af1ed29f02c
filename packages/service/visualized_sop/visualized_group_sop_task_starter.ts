import { Queue } from 'bullmq'
import logger from 'model/logger/logger'
import { RedisDB } from 'model/redis/redis'
import { RedisCacheDB } from 'model/redis/redis_cache'
import { ScheduleTask } from './schedule'
import {  GroupSop, IGroupTask, SopScheduleTime, getVisualizedRedisGroupSopKey } from './visualized_sop_type'
import { PrismaClient } from 'model/prisma_client'

export async function startGroupTasks(mongoClient:PrismaClient, enterpriseName:string, groupId: string, calTaskTime:(time: SopScheduleTime, courseNo:number)=> Date) {
  logger.log(`开始添加群sop groupId:${groupId}`)
  const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
  if (!groupInfo) {
    logger.error(`添加group tasks的时候，查询group信息失败，gruop_id:${groupId}`)
    return
  }

  const tasks = await VisualizedGroupSopTasks.getTaskList(enterpriseName, groupId)
  // 使用Promise.all进行并发操作
  await Promise.all(
    tasks.map(async (task) => {
      task.sendTime = calTaskTime(
        task.scheduleTime,
        groupInfo.course_no
      )
    })
  )

  await ScheduleTask.addGroupTasks(getVisualizedGroupSopQueueName(enterpriseName, groupInfo.owner_account_id), tasks)
  logger.log(`添加群sop groupId:${groupId} botId:${groupInfo.owner_account_id} 成功`)
}

export class VisualizedGroupSopTasks {
  static async getTaskList(enterpriseName:string, groupId:string): Promise<IGroupTask[]> {
    const sops = await this.getTasks(enterpriseName, groupId)
    return sops.map((sop) => {
      return {
        name: sop.id,
        groupId,
        scheduleTime: {
          day: sop.day,
          week: sop.week,
          time: sop.time,
        },
      }
    })
  }

  static async getTasks(enterpriseName:string, groupId:string) {
    const redisClient = RedisDB.getInstance()
    const redisResult = await redisClient.get(getVisualizedRedisGroupSopKey(enterpriseName, groupId))
    if (!redisResult) {
      logger.error('redis查询失败')
      return []
    }
    return JSON.parse(redisResult) as GroupSop[]
  }


  static async clearSop(enterpriseName:string, botId:string) {
    const redisClient = RedisCacheDB.getInstance()
    const queue = new Queue<IGroupTask>(
      getVisualizedGroupSopQueueName(enterpriseName, botId),
      {
        connection: redisClient,
      }
    )
    await queue.drain(true)
  }
}

export function getVisualizedGroupSopQueueName(enterpriseName:string, botId:string): string {
  return `${enterpriseName}-group-sop-${botId}`.replaceAll(':', '')
}