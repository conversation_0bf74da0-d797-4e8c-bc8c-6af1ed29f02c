export enum SendMessageType {
  text = 'text',
  image = 'image',
  video = 'video',
  audio = 'audio',
  wecomVoice = 'wecom_voice',
  file = 'file',
  sticker = 'sticker',

  wecomCard = 'wecom_card',
  wecomVideoChannel = 'wecom_video_channel',

  yCloudTemplate = 'ycloud_template',

  material = 'material'
}

export type MessageText = {
  type:SendMessageType.text
  text:string
  description:string
}

export type MessageImage = {
  type:SendMessageType.image
  url:string
  description:string
}

export type MessageVideo = {
  type:SendMessageType.video
  url:string
  description:string
}

export type MessageAudio = {
  type:SendMessageType.audio
  url:string
  description:string
}

export type MessageWecomVoice = {
  type:SendMessageType.wecomVoice
  url:string
  duration:number
  description:string
}

export type MessageFile = {
  type:SendMessageType.file
  url:string
  filename:string
  description:string
}

export type MessageSticker = {
  type:SendMessageType.sticker
  url:string
  description:string
}

export type MessageWecomCard = {
  type:SendMessageType.wecomCard
  sourceUrl:string
  title:string
  summary:string
  imageUrl:string
  description:string
}

export type MessageWecomVideoChannel = {
  type:SendMessageType.wecomVideoChannel
  avatarUrl:string
  coverUrl:string
  wecomContentdescription:string
  nickname:string
  thumbUrl:string
  url:string
  extras:string
  description:string
}

export type MessageYCloudTemplate = {
  type:SendMessageType.yCloudTemplate
  description:string
  templateName:string
  language: string
  variable: string[]
  header?: {
    type:'video' | 'document' | 'image'
    url: string
  }
}

export type MessageMaterial = {
  type: SendMessageType.material
  description?:string
  sourceId:string
}

export type MessageSendOption = {
  sopId?:string
  roundId?:string
  force?:boolean
}

export type MessageAll = MessageText | MessageImage | MessageVideo | MessageAudio | MessageWecomVoice | MessageFile | MessageSticker | MessageWecomCard | MessageWecomVideoChannel | MessageYCloudTemplate | MessageMaterial