import { Counter } from 'lib/prometheus_client/index'

export const sopProcessCounter = new Counter({
  name: 'sop_process',
  help: 'The times of sop process',
  labelNames:['bot_id']
})

export const sopProcessErrorCounter = new Counter({
  name: 'sop_process_error',
  help: 'The times of sop process error',
  labelNames:['bot_id']
})

export const sopActionProcessCounter = new Counter({
  name: 'sop_action_process',
  help: 'The times of sop action process',
  labelNames:['bot_id']
})

export const sopActionProcessErrorCounter = new Counter({
  name: 'sop_action_process_error',
  help: 'The times of sop action process error',
  labelNames:['bot_id']
})

export const groupSopProcessCounter = new Counter({
  name: 'group_sop_process',
  help: 'The times of group sop process',
  labelNames:['bot_id']
})

export const groupSopProcessErrorCounter = new Counter({
  name: 'group_sop_process_error',
  help: 'The times of group sop process error',
  labelNames:['bot_id']
})

export const groupSopActionProcessCounter = new Counter({
  name: 'group_sop_action_process',
  help: 'The times of group sop action process',
  labelNames:['bot_id']
})

export const groupSopActionProcessErrorCounter = new Counter({
  name: 'group_sop_action_process_error',
  help: 'The times of group sop action process error',
  labelNames:['bot_id']
})
