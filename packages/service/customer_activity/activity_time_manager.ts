import { PrismaMongoClient } from 'model/mongodb/prisma'
import {
  CustomerActivity,
  TimeSlot,
  TimeSlotWeight,
  CreateCustomerActivityInput,
  UpdateCustomerActivityInput,
  DEFAULT_CONFIG
} from 'model/prisma/models/customer_activity'
import { TimeSlotUtils } from './time_slot_utils'
import { TimeSlotPredictor, IPredictedTimeSlot } from './time_slot_predictor'

/**
 * 默认配置常量 - 使用从模型文件导入的常量
 */
const DEFAULT_COOLDOWN_MS = DEFAULT_CONFIG.COOLDOWN_MS
const DEFAULT_INCREASE_AMOUNT = DEFAULT_CONFIG.INCREASE_AMOUNT
const DEFAULT_DECREASE_AMOUNT = DEFAULT_CONFIG.DECREASE_AMOUNT
const DEFAULT_MIN_WEIGHT = DEFAULT_CONFIG.MIN_WEIGHT
const DEFAULT_MAX_WEIGHT = DEFAULT_CONFIG.MAX_WEIGHT

/**
 * 权重调整配置
 */
export interface IWeightAdjustmentConfig {
  coolDown?: number  // 更新权重冷却时间（毫秒），默认5分钟
  increaseAmount?: number  // 权重增加量
  decreaseAmount?: number  // 权重减少量
  minWeight?: number  // 最小权重
  maxWeight?: number  // 最大权重
}

/**
 * 客户活跃时段管理器
 * 提供方法用于初始化客户活跃时段数据、增加权重、减少权重和获取最佳时段
 */
export class ActivityTimeManager {
  private static prismaClient: any = null

  /**
   * 获取Prisma客户端实例
   */
  private static getPrismaClient() {
    if (!this.prismaClient) {
      this.prismaClient = PrismaMongoClient.getInstance()
    }
    return this.prismaClient
  }

  /**
   * 确保集合存在（避免在事务中创建集合的问题）
   */
  private static async ensureCollectionExists() {
    try {
      const prismaClient = this.getPrismaClient()
      // 尝试查询一个不存在的记录，这会确保集合存在
      await prismaClient.customer_activity.findFirst({
        where: { chat_id: 'non_existent_check' }
      })
    } catch (error) {
      // 如果集合不存在，这个查询会失败，但这是预期的
      // 我们只是想确保集合被创建
    }
  }

  /**
   * 检查是否在冷却时间内
   * @param lastUpdated 上次更新时间
   * @param cooldownMs 冷却时间（毫秒）
   * @returns true表示在冷却时间内，false表示可以更新
   */
  private static isInCooldown(lastUpdated: Date, cooldownMs: number): boolean {
    const now = new Date()
    const timeDiff = now.getTime() - lastUpdated.getTime()
    return timeDiff < cooldownMs
  }

  /**
   * 获取客户的冷却时间配置
   * @param customerActivity 客户活跃时段数据
   * @param configCooldown 配置中的冷却时间
   * @returns 冷却时间（毫秒）
   */
  private static getCooldownMs(customerActivity: any, configCooldown?: number): number {
    // 优先级：传入的配置 > 客户存储的配置 > 默认值
    return configCooldown || customerActivity.cooldown_ms || DEFAULT_COOLDOWN_MS
  }

  /**
   * 拿到客户画像之后，初始化客户活跃时段数据
   * @param chatId 聊天ID
   * @param customerProfile 客户画像
   * @param config 权重调整配置
   */
  public static async initializeCustomerActivity(
    chatId: string,
    customerProfile: string,
    config?: Partial<IWeightAdjustmentConfig>
  ): Promise<void> {
    try {
      // 检查是否已存在
      const prismaClient = this.getPrismaClient()
      const existing = await prismaClient.customer_activity.findUnique({
        where: { chat_id: chatId }
      })
      if (existing) {
        console.debug(`客户 ${chatId} 的活跃时段数据已存在`)
        return
      }

      // 使用AI预测活跃时段
      const predictedSlots = await TimeSlotPredictor.predictActiveTimeSlots(chatId, customerProfile)

      // 创建时段权重数组
      const timeSlots: TimeSlotWeight[] = []

      // 初始化所有时段为默认权重
      // 将初始的 last_updated 设置为较早的时间，避免冷却机制影响首次更新
      const initialLastUpdated = new Date(Date.now() - (config?.coolDown || DEFAULT_COOLDOWN_MS) - 1000)

      for (const slot of TimeSlotUtils.getAllTimeSlots()) {
        const predicted = predictedSlots.find((p) => p.slot === slot)
        timeSlots.push({
          slot,
          weight: predicted ? predicted.weight : 0.3, // 未预测的时段给较低权重
          is_predicted: Boolean(predicted),
          last_updated: initialLastUpdated  // 设置为较早的时间
        })
      }

      // 创建客户活跃时段记录
      const customerActivityData: CreateCustomerActivityInput = {
        chat_id: chatId,
        time_slots: timeSlots,
        weight_increase_factor: config?.increaseAmount || DEFAULT_INCREASE_AMOUNT,
        weight_decrease_factor: config?.decreaseAmount || DEFAULT_DECREASE_AMOUNT,
        min_weight: config?.minWeight || DEFAULT_MIN_WEIGHT,
        max_weight: config?.maxWeight || DEFAULT_MAX_WEIGHT,
        cooldown_ms: config?.coolDown || DEFAULT_COOLDOWN_MS
      }

      await prismaClient.customer_activity.create({
        data: customerActivityData
      })

      console.log(`已为客户 ${chatId} 初始化活跃时段数据，预测了 ${predictedSlots.length} 个时段`)
    } catch (error) {
      console.error(`初始化客户活跃时段数据失败 for ${chatId}:`, error)
      throw error
    }
  }

  /**
   * 增加当前时段权重（客户发消息时调用）
   * @param chatId 聊天ID
   * @param messageTime 消息时间，默认为当前时间
   * @param skipCooldown 是否跳过冷却检查（仅用于测试）
   */
  public static async increaseCurrentSlotWeight(
    chatId: string,
    messageTime: Date = new Date(),
    skipCooldown: boolean = false
  ): Promise<void> {
    try {
      const timeSlot = TimeSlotUtils.getTimeSlotByDate(messageTime)
      if (!timeSlot) {
        console.debug(`消息时间 ${messageTime.toISOString()} 不在活跃时段范围内`)
        return
      }

      const prismaClient = this.getPrismaClient()
      const now = new Date()

      // 获取客户活跃时段数据
      const customerActivity = await prismaClient.customer_activity.findUnique({
        where: { chat_id: chatId }
      })

      if (!customerActivity) {
        console.warn(`客户 ${chatId} 的活跃时段数据不存在，无法增加权重`)
        return
      }

      // 找到对应时段
      const slotIndex = customerActivity.time_slots.findIndex((ts: TimeSlotWeight) => ts.slot === timeSlot)
      if (slotIndex === -1) {
        console.warn(`客户 ${chatId} 的时段 ${timeSlot} 不存在`)
        return
      }

      const currentSlot = customerActivity.time_slots[slotIndex]
      const cooldownMs = this.getCooldownMs(customerActivity)

      // 检查是否在冷却时间内（除非跳过冷却检查）
      if (!skipCooldown && this.isInCooldown(currentSlot.last_updated, cooldownMs)) {
        console.debug(`客户 ${chatId} 时段 ${timeSlot} 在冷却时间内（${cooldownMs}ms），跳过权重更新`)
        return
      }

      // 计算新权重
      const currentWeight = currentSlot.weight
      const newWeight = Math.min(
        currentWeight + customerActivity.weight_increase_factor,
        customerActivity.max_weight
      )

      // 更新时段权重数组
      const updatedTimeSlots = [...customerActivity.time_slots]
      updatedTimeSlots[slotIndex] = {
        ...currentSlot,
        weight: newWeight,
        last_updated: now
      }

      // 更新数据库
      await prismaClient.customer_activity.update({
        where: { chat_id: chatId },
        data: {
          time_slots: updatedTimeSlots,
          updated_at: now
        }
      })

      console.debug(`客户 ${chatId} 时段 ${timeSlot} 权重从 ${currentWeight.toFixed(2)} 增加到 ${newWeight.toFixed(2)}`)
    } catch (error) {
      console.error(`增加时段权重失败 for ${chatId}:`, error)
    }
  }

  /**
   * 减少指定时段权重（客户未回复时调用）
   * @param chatId 聊天ID
   * @param timeSlot 时段
   * @param skipCooldown 是否跳过冷却检查（仅用于测试）
   */
  public static async decreaseSlotWeight(
    chatId: string,
    timeSlot: TimeSlot,
    skipCooldown: boolean = false
  ): Promise<void> {
    try {
      const prismaClient = this.getPrismaClient()
      const now = new Date()

      // 获取客户活跃时段数据
      const customerActivity = await prismaClient.customer_activity.findUnique({
        where: { chat_id: chatId }
      })

      if (!customerActivity) {
        console.warn(`客户 ${chatId} 的活跃时段数据不存在，无法减少权重`)
        return
      }

      // 找到对应时段
      const slotIndex = customerActivity.time_slots.findIndex((ts: TimeSlotWeight) => ts.slot === timeSlot)
      if (slotIndex === -1) {
        console.warn(`客户 ${chatId} 的时段 ${timeSlot} 不存在`)
        return
      }

      const currentSlot = customerActivity.time_slots[slotIndex]
      const cooldownMs = this.getCooldownMs(customerActivity)

      // 检查是否在冷却时间内（除非跳过冷却检查）
      if (!skipCooldown && this.isInCooldown(currentSlot.last_updated, cooldownMs)) {
        console.debug(`客户 ${chatId} 时段 ${timeSlot} 在冷却时间内（${cooldownMs}ms），跳过权重更新`)
        return
      }

      // 计算新权重
      const currentWeight = currentSlot.weight
      const newWeight = Math.max(
        currentWeight - customerActivity.weight_decrease_factor,
        customerActivity.min_weight
      )

      // 更新时段权重数组
      const updatedTimeSlots = [...customerActivity.time_slots]
      updatedTimeSlots[slotIndex] = {
        ...currentSlot,
        weight: newWeight,
        last_updated: now
      }

      // 更新数据库
      await prismaClient.customer_activity.update({
        where: { chat_id: chatId },
        data: {
          time_slots: updatedTimeSlots,
          updated_at: now
        }
      })

      console.debug(`客户 ${chatId} 时段 ${timeSlot} 权重从 ${currentWeight.toFixed(2)} 减少到 ${newWeight.toFixed(2)}`)
    } catch (error) {
      console.error(`减少时段权重失败 for ${chatId}:`, error)
    }
  }

  /**
   * 获取客户的最佳活跃时段（按权重排序）
   * @param chatId 聊天ID
   * @param limit 返回数量限制，默认为3
   * @returns 按权重排序的时段数组
   */
  public static async getBestActiveTimeSlots(
    chatId: string,
    limit: number = 3
  ): Promise<TimeSlotWeight[]> {
    try {
      const prismaClient = this.getPrismaClient()
      const customerActivity = await prismaClient.customer_activity.findUnique({
        where: { chat_id: chatId }
      })
      if (!customerActivity) {
        console.warn(`客户 ${chatId} 的活跃时段数据不存在`)
        return []
      }

      // 按权重排序并返回前N个
      return customerActivity.time_slots
        .sort((a, b) => b.weight - a.weight)
        .slice(0, limit)
    } catch (error) {
      console.error(`获取最佳活跃时段失败 for ${chatId}:`, error)
      return []
    }
  }

  /**
   * 获取客户活跃时段的完整信息
   * @param chatId 聊天ID
   * @returns 客户活跃时段数据
   */
  public static async getCustomerActivity(chatId: string): Promise<CustomerActivity | null> {
    try {
      const prismaClient = this.getPrismaClient()
      return await prismaClient.customer_activity.findUnique({
        where: { chat_id: chatId }
      })
    } catch (error) {
      console.error(`获取客户活跃时段数据失败 for ${chatId}:`, error)
      return null
    }
  }

  /**
   * 更新权重调整配置
   * @param chatId 聊天ID
   * @param config 新的配置
   */
  public static async updateWeightConfig(
    chatId: string,
    config: Partial<IWeightAdjustmentConfig>
  ): Promise<void> {
    try {
      const updateData: any = {}

      if (config.increaseAmount !== undefined) {
        updateData.weight_increase_factor = config.increaseAmount
      }
      if (config.decreaseAmount !== undefined) {
        updateData.weight_decrease_factor = config.decreaseAmount
      }
      if (config.minWeight !== undefined) {
        updateData.min_weight = config.minWeight
      }
      if (config.maxWeight !== undefined) {
        updateData.max_weight = config.maxWeight
      }
      if (config.coolDown !== undefined) {
        updateData.cooldown_ms = config.coolDown
      }

      updateData.updated_at = new Date()

      const prismaClient = this.getPrismaClient()
      await prismaClient.customer_activity.update({
        where: { chat_id: chatId },
        data: updateData
      })

      console.log(`已更新客户 ${chatId} 的权重调整配置`)
    } catch (error) {
      console.error(`更新权重配置失败 for ${chatId}:`, error)
      throw error
    }
  }
}
