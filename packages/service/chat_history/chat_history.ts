import { AIMessage as BotMessage, BaseMessage, HumanMessage } from '@langchain/core/messages'
import chalk from 'chalk'
import { catchError } from 'lib/error/catchError'
import { DateHelper } from 'lib/date/date'
import { Config } from 'config'
import logger from 'model/logger/logger'
import { ChatStateStore } from '../local_cache/chat_state_store'
import { IChatState } from '../local_cache/type'
import { ChatInterruptHandler } from '../message_handler/interrupt/interrupt_handler'
import { PrismaClient } from 'model/prisma_client'

export interface IDBBotMessageOptions {
  is_send_by_human?: boolean // 人工回复
  round_id?: string   // LLM 输出的
  chat_state?: IChatState  //聊天时的客户状态
  is_recalled?: boolean // 消息被撤回
  message_id?: string // 消息 ID
  sop_id?:string
  state?:string
}

export interface IDBBaseMessage extends IBaseMessage, IDBBotMessageOptions {
  id: string
  created_at: Date
  chat_id: string
  short_description?: string // SOP 话术
}

export interface IBaseMessage {
  role: 'user' | 'assistant'
  content: string
}

export class ChatHistoryService {
  mongoClient:PrismaClient
  chatStateStoreClient:ChatStateStore

  constructor(mongoClient: PrismaClient, chatStateStoreClient: ChatStateStore) {
    this.mongoClient = mongoClient
    this.chatStateStoreClient = chatStateStoreClient
  }

  static archiveFlag = '[archived]'

  public async getChatHistoryByChatId(chat_id: string, noCompress = false) {
    try {
      let chatHistory = await this.mongoClient.chat_history.findMany({
        where: {
          chat_id: chat_id
        },
        orderBy: {
          created_at: 'asc'
        },
        select: {
          id: true,
          role: true,
          content: true,
          created_at: true,
          chat_id: true,
          short_description: true,
          is_send_by_human: true,
          round_id: true,
          is_recalled: true,
          message_id: true
        }
      }) as IDBBaseMessage[]

      // 如果有 archive flag，取 archive flag 往后的记录
      let latestArchiveIndex = -1

      for (let i = chatHistory.length - 1; i >= 0; i--) {
        if (chatHistory[i].role === 'assistant' && chatHistory[i].content === ChatHistoryService.archiveFlag) {
          latestArchiveIndex = i
          break
        }
      }

      if (latestArchiveIndex !== -1) {
        chatHistory = chatHistory.slice(latestArchiveIndex + 1)
      }

      if (!noCompress) {
        chatHistory = ChatHistoryService.compressMarketingMessages(chatHistory)
      }

      return chatHistory
    } catch (error) {
      console.error('Error fetching chat records:', error)
      return []
    }
  }

  public async getAllChatHistory() {
    try {
      let chatHistory = await this.mongoClient.chat_history.findMany({
        orderBy: {
          created_at: 'asc'
        },
      }) as unknown as IDBBaseMessage[]
      chatHistory = ChatHistoryService.compressMarketingMessages(chatHistory)
      return chatHistory
    } catch (error) {
      console.error('Error fetching chat records:', error)
      return []
    }
  }

  public async getLLMChatHistory(chat_id: string, rounds?: number): Promise<BaseMessage[]> {
    let chatHistory: IDBBaseMessage[]
    if (rounds === 0) {
      return []
    } else if (rounds) {
      chatHistory = await this.getRecentConversations(chat_id, rounds, 'user')
    } else {
      chatHistory = await this.getChatHistoryByChatId(chat_id)
    }
    return chatHistory.map((message) => {
      if (message.role === 'user') {
        return this.createMultiModalHumanMessage(message.content, message.created_at)
      } else {
        return new BotMessage(message.content)
      }
    })
  }

  /**
   * 创建支持多模态的HumanMessage
   * 检测消息中的图片URL并转换为多模态格式
   * @param content 消息内容
   * @param createdAt 消息创建时间，用于添加时间戳
   */
  private createMultiModalHumanMessage(content: string, createdAt?: Date): HumanMessage {
    // 添加时间戳到消息内容中
    let contentWithTimestamp = content
    if (createdAt) {
      const timestamp = DateHelper.formatDate(createdAt, 'YYYY-MM-DD')
      contentWithTimestamp = `[${timestamp}] ${content}`
    }

    // 检测是否包含图片URL格式：【图片Url】https://...
    // 使用更精确的正则表达式，URL应该以空格、中文字符或字符串结尾为边界
    const imageUrlPattern = /【图片Url】(https?:\/\/[^\s\u4e00-\u9fff【】，。！？；："'（）]*(?:\.[a-zA-Z0-9]+)*(?:\?[^\s\u4e00-\u9fff【】，。！？；："'（）]*)?)/g
    const imageMatches = Array.from(contentWithTimestamp.matchAll(imageUrlPattern))

    if (imageMatches.length === 0) {
      // 没有图片，返回普通文本消息
      return new HumanMessage(contentWithTimestamp)
    }

    // 构建多模态内容
    const multiModalContent: Array<{
      type: 'text' | 'image_url',
      text?: string,
      image_url?: { url: string }
    }> = []

    let lastIndex = 0

    // 处理每个图片URL
    for (const match of imageMatches) {
      const fullMatch = match[0] // 完整匹配：【图片Url】https://...
      const imageUrl = match[1] // 提取的URL
      const matchIndex = match.index!

      // 添加图片前的文本（如果有）
      if (matchIndex > lastIndex) {
        const textBefore = contentWithTimestamp.substring(lastIndex, matchIndex).trim()
        if (textBefore) {
          multiModalContent.push({
            type: 'text',
            text: textBefore
          })
        }
      }

      // 添加图片
      multiModalContent.push({
        type: 'image_url',
        image_url: { url: imageUrl }
      })

      lastIndex = matchIndex + fullMatch.length
    }

    // 添加最后剩余的文本（如果有）
    if (lastIndex < contentWithTimestamp.length) {
      const textAfter = contentWithTimestamp.substring(lastIndex).trim()
      if (textAfter) {
        multiModalContent.push({
          type: 'text',
          text: textAfter
        })
      }
    }

    return new HumanMessage({
      content: multiModalContent
    })
  }



  public async clearChatHistory(chat_id: string, archive = true) {
    try {
      // 假删除
      if (archive) {
        await this.addBotMessage(chat_id, ChatHistoryService.archiveFlag)
      } else {
        const result = await this.mongoClient.chat_history.deleteMany({
          where: {
            chat_id
          },
        })
        logger.debug(chat_id, `${result.count} chat records with chatId '${chat_id}' have been cleared.`)
        return result.count
      }
    } catch (error) {
      console.error('Error clearing chat records:', error)
    }
  }


  public async addBotMessage(chat_id: string, message: string, shortDes?: string, options?: IDBBotMessageOptions) {
    if (message) {
      await this.mongoClient.chat_history.create({
        data: {
          chat_id: chat_id,
          role: 'assistant',
          content: message,
          created_at: new Date(),
          short_description: shortDes,
          is_send_by_human: options?.is_send_by_human,
          round_id: options?.round_id,
          chat_state: await this.chatStateStoreClient.get(chat_id),
          is_recalled: options?.is_recalled,
          message_id: options?.message_id,
          sop_id:options?.sop_id,
          state:options?.state
        }
      })
    }

    if (message !== ChatHistoryService.archiveFlag) {
      logger.log({ chat_id }, chalk.greenBright(`${Config.setting.AGENT_NAME}: ${message}`))
    }
  }

  public async addUserMessage(chat_id: string, message: string, roundId?:string) {
    // 移除 打卡模版 前缀
    message = message.replace(/[\s\S]*?(比如👉：)/, '').trim()

    if (message) {
      await this.mongoClient.chat_history.create({
        data: {
          chat_id: chat_id,
          role: 'user',
          content: message,
          created_at: new Date(),
          round_id:roundId
        }
      })
      logger.log({ chat_id }, chalk.blueBright(`客户: ${message}`))

      await ChatInterruptHandler.incrementChatVersion(chat_id)
    }
  }

  public async getFormatChatHistoryByChatId(chat_id: string, noCompress:boolean = false) {
    if (!chat_id) {
      return ''
    }

    const chatHistory = await this.getChatHistoryByChatId(chat_id, noCompress)
    const formattedHistory = chatHistory.map((message) => {
      return `${message.role === 'user' ? '客户' : Config.setting.AGENT_NAME}: ${message.content.replaceAll('{', '').replaceAll('}', '')}`
    })
    return formattedHistory.join('\n')
  }

  public async formatHistoryOnRole(chat_id: string, role: 'user' | 'assistant', last_rounds?: number) {
    let chatHistory = await this.getChatHistoryByChatId(chat_id)
    chatHistory = chatHistory.filter((message) => message.role === role)

    if (last_rounds) {
      chatHistory = chatHistory.slice(-last_rounds)
    }
    const formattedHistory = chatHistory.map((message) => {
      return `${message.role === 'user' ? '客户' : Config.setting.AGENT_NAME}: ${message.content}`
    })
    return formattedHistory.join('\n')
  }

  public static formatHistoryHelper(messages: (IDBBaseMessage | BaseMessage)[]) {
    const formatContent = (content: string) => {
      return content.replace(/[{}]/g, '').replace(/\n+/g, ' ').trim()
    }
    return messages.map((message) => {
      if (message instanceof BaseMessage) {
        const role = message.getType() === 'human' ? '- 客户' : `  - ${Config.setting.AGENT_NAME}`
        return `${role}：${formatContent(message.content as string)}`
      } else {
        const timestamp = DateHelper.formatDate(message.created_at, 'YYYY-MM-DD HH:mm:ss')
        const role = message.role === 'user' ? `- [${timestamp}] 客户` : `  - [${timestamp}] ${Config.setting.AGENT_NAME}`
        return `${role}：${formatContent(message.content as string)}`
      }
    }).join('\n').trim()
  }

  async repeatLastMessage(chat_id: string, s: string): Promise<boolean> {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    if (chatHistory.length === 0) {
      return false
    }

    const lastMessage = chatHistory[chatHistory.length - 1]
    return lastMessage.content === s
  }

  async setChatHistory(chat_id: string, chatHistory: IBaseMessage[]) {
    await this.clearChatHistory(chat_id)

    chatHistory.forEach((message: IBaseMessage) => {
      if (message.role === 'user') {
        logger.log({ chat_id }, chalk.blueBright(`客户: ${message.content}`))
      } else {
        logger.log({ chat_id }, chalk.greenBright(`${Config.setting.AGENT_NAME}: ${message.content}`))
      }
    })

    await this.mongoClient.chat_history.createMany({
      data: chatHistory.map((message: IBaseMessage) => {
        return {
          chat_id: chat_id,
          role: message.role,
          content: message.content,
          created_at: new Date()
        }
      })
    })
  }

  async getUserMessageCount(chat_id: string): Promise<number> {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    return chatHistory.length
  }

  async getMessageCount(chat_id: string): Promise<number> {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    return chatHistory.filter((message) => message.role === 'user' || message.role === 'assistant').length
  }

  async getLastRoundHistory(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    let res: IDBBaseMessage[] = []

    // 倒序获取一轮对话
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'user') {
        res = chatHistory.slice(i, chatHistory.length)
        break
      }
    }
    return res
  }

  async getRecentConversations (chat_id: string, rounds: number, role: 'user' | 'assistant' = 'user'): Promise<IDBBaseMessage []> {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    let res: IDBBaseMessage [] = []
    let roundCount = 0

    // 从后向前遍历
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      // 如果是客户消息，增加轮次计数
      if (chatHistory [i].role === role) {
        roundCount++

        // 如果达到指定轮次数，结束遍历
        if (roundCount === rounds) {
          res = chatHistory.slice (i)
          break
        }
      }

      // 如果遍历到开头仍未达到指定轮次，返回全部历史
      if (i === 0) {
        res = chatHistory.slice ()
      }
    }

    return res
  }

  public async getDialogHistory(chat_id: string, rounds: number, maxLength: number = 9, noReplyFilter: boolean = true) {
    let chatHistory = await this.getRecentConversations(chat_id, rounds, 'user')
    if (noReplyFilter) {
      chatHistory = this.filterChatHistory(chatHistory)
    }
    if (chatHistory.length > maxLength) {
      chatHistory = chatHistory.slice(-maxLength)
    }
    for (let i = 0; i < chatHistory.length; i++) {
      if (chatHistory[i].content.length > 600 || chatHistory[i].content.includes('A B C D')) {
        chatHistory[i].content = chatHistory[i].short_description || '[营销信息]'
      }
    }
    return ChatHistoryService.formatHistoryHelper(chatHistory).replace(/\n+/g, '\n').trim()
  }

  private filterChatHistory(chatHistory: IDBBaseMessage[]) {

    return chatHistory.filter((message) => {
      const nextMessage = chatHistory[chatHistory.indexOf(message) + 1]
      if (nextMessage && nextMessage.role === 'user') {
        return true
      }
      return !message.sop_id
    })
  }

  async getLastAIMessage(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'assistant') {
        return chatHistory[i].content
      }
    }
    return ''
  }

  async getLastUserMessage(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'user') {
        return chatHistory[i].content
      }
    }
    return ''
  }

  async countRemainingMsgAfterMsg(chat_id: string, message: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    const msgIndex = chatHistory.findIndex((msg) => msg.content === message)

    if (msgIndex === -1) {
      return -1
    }

    return chatHistory.length - msgIndex - 1
  }

  async getLastMessage(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    return chatHistory[chatHistory.length - 1]
  }

  async getUserMessages(chat_id: string, round?: number) {
    if (round) {
      const chatHistory = await this.getRecentConversations(chat_id, round, 'user')
      return chatHistory.filter((message) => message.role === 'user').map((message) => message.content)
    } else {
      const chatHistory = await this.getChatHistoryByChatId(chat_id)

      return chatHistory.filter((message) => message.role === 'user').map((message) => message.content)
    }
  }

  async getBotMessages(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    return chatHistory.filter((message) => message.role === 'assistant').map((message) => message.content)
  }

  async isRepeatedMsg(chat_id: string, message: string) {
    const chatHistory = await this.formatHistoryOnRole(chat_id, 'assistant', 10)

    // 分句检查是否重复
    // Split the sentence into clauses based on punctuation marks like period, comma, semicolon, etc.
    const clauses = message
      .split(/[,;!?，。；！？]/)
      .map((clause) => clause.trim())
      .filter((clause) => clause.length >= 7)

    const noCheckCommonClaude = 'http://'

    // 尝试从分句中查找重复的部分
    const repeatedClause = clauses.find(
      (clause) => chatHistory.includes(clause) && !clause.includes(noCheckCommonClaude)
    )

    let repeated = false
    let repeatedPart = ''
    let previousSentence = ''

    if (repeatedClause) {
      repeated = true
      repeatedPart = repeatedClause
      // 将 chatHistory 按换行分割，并查找包含重复部分的那句话
      const sentences = chatHistory.split('\n')
      previousSentence = sentences.find((sentence) => sentence.includes(repeatedClause)) || ''
    } else if (chatHistory.includes(message) && message.length >= 7) {
      repeated = true
      repeatedPart = message
      const sentences = chatHistory.split('\n')
      previousSentence = sentences.find((sentence) => sentence.includes(message)) || ''
    }

    // 如果检测到重复，则通过 logger.log 输出当前 message、重复的历史内容及重复部分
    if (repeated) {
      console.log({ chat_id }, `重复检测 - 当前 message: ${message}`)
      console.log({ chat_id }, `重复检测 - 与之前重复的那句话: ${previousSentence}`)
      console.log({ chat_id }, `重复检测 - 重复的部分: ${repeatedPart}`)
    }

    return repeated
  }

  async hasRepeatedMsg(chat_id: string, toMatch: string) {
    const chatHistory = await this.formatHistoryOnRole(chat_id, 'assistant')

    return chatHistory.includes(toMatch)
  }

  private static compressMarketingMessages(chatHistory: IDBBaseMessage[]) {
    const totalMessages = chatHistory.length
    return chatHistory.map((message, index) => {
      if (index >= totalMessages - 10 || !message.short_description) {
        return message
      }
      return { ...message, content: message.short_description }
    })
  }

  /**
   * 将指定 消息放到最后
   * @param chat_id
   * @param userMessage
   */
  async moveToEnd(chat_id: string, userMessage: string) {
    if (!userMessage) {
      return
    }

    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    let message: IDBBaseMessage | null = null
    for (let i = chatHistory.length - 1; i >= 0; i--) { // 有可能客户会重复说同一句话，倒序遍历会更稳定一些
      if (chatHistory[i].content === userMessage) {
        message = chatHistory[i]
        break
      }
    }
    if (!message) {
      return
    }

    await catchError(this.mongoClient.chat_history.update({
      where: {
        id: message.id
      },
      data: {
        created_at: new Date()
      }
    }))
  }

  /**
   @return 是否在 duration 的时间范围内有非营销的聊天记录
   * @param chatId
   * @param duration
   * @param unit
   */
  async isLastMessageWithDuration(chatId: string, duration:number, unit: 'second' | 'minute') {
    const chatHistory = await this.getChatHistoryByChatId(chatId)

    if (chatHistory.length < 1) return false

    let lastMsg = chatHistory[chatHistory.length - 1]
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (!chatHistory[i].short_description) {
        lastMsg = chatHistory[i]
      }
    }
    if (lastMsg.short_description) return false

    const lastMsgSendTime = lastMsg.created_at
    return DateHelper.diff(lastMsgSendTime, new Date(), unit) < duration
  }

  async getMessageByMessageId(externalId: string) {
    return this.mongoClient.chat_history.findFirst({
      where: {
        message_id: externalId
      }
    })
  }

  async updateMessageId(id: string, messageId: string) {
    return this.mongoClient.chat_history.update({
      where: {
        id
      },
      data: {
        message_id: messageId
      }
    })
  }

  async setMessageRecalled(messageId: string) {
    const message = await this.getMessageByMessageId(messageId)

    if (!message) return

    await catchError(this.mongoClient.chat_history.update({
      where: {
        id: message.id
      },
      data: {
        is_recalled: true
      }
    }))
  }
}