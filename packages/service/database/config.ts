import { PrismaMongoClient } from 'model/mongodb/prisma'
import { IWechatConfig } from 'config/interface'
import { Config } from 'config'

export interface Account {
    orgToken: string
    nickname: string
    wechatId: string
    botUserId: string
    address: string
    port: number

    notifyGroupId: string
    classGroupId: string
    isGroupOwner?: boolean
    proxyGroupNotify?: boolean
}

interface IMoerEnterpriseConfig {
    notifyGroupId: string
    classGroupId: string
    isGroupOwner?: boolean
    proxyGroupNotify?: boolean
}

export class ClientAccountConfig {
  private static idServerAddressMap: Map<string, string> = new Map() // wechatId -> serverAddress
  private static wechatNameMap: Map<string, Account> = new Map() // nickname -> account

  private static async pullAllConfig() {
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany()
    for (const config of configs) {
      this.idServerAddressMap.set(config.wechatId, config.address)
    }
  }

  private static async pullEnterpriseConfig() {
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: Config.setting.projectName
        }
      }
    )

    for (const config of configs) {
      const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig

      this.wechatNameMap.set(config.accountName, {
        orgToken: config.orgToken,
        nickname: config.accountName,
        wechatId: config.wechatId,
        botUserId: config.botUserId,
        address: config.address,
        port: Number(config.port),
        notifyGroupId: enterpriseConfig.notifyGroupId,
        classGroupId: enterpriseConfig.classGroupId,
        isGroupOwner: enterpriseConfig.isGroupOwner,
        proxyGroupNotify: enterpriseConfig.proxyGroupNotify
      })
    }
  }

  public static async getServerAddressByWechatId(wechatId: string) {
    // 如果本地没有，从数据库尝试读取
    if (this.idServerAddressMap.has(wechatId)) {
      return this.idServerAddressMap.get(wechatId)
    }

    await this.pullAllConfig()
    return this.idServerAddressMap.get(wechatId)
  }

  public static async getAccountByName(name: string) {
    if (this.wechatNameMap.has(name)) {
      return this.wechatNameMap.get(name)
    }

    await this.pullEnterpriseConfig()
    return this.wechatNameMap.get(name)
  }
}


export async function loadConfigByAccountName(name: string): Promise<IWechatConfig> {
  const config = await PrismaMongoClient.getConfigInstance().config.findFirst(
    {
      where: {
        enterpriseName: Config.setting.projectName,
        accountName: name
      }
    }
  )
  if (!config) {
    throw new Error('Config not found')
  }
  const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig

  const account =  {
    orgToken: config.orgToken,
    nickname: config.accountName,
    wechatId: config.wechatId,
    botUserId: config.botUserId,
    address: config.address,
    port: Number(config.port),
    notifyGroupId: enterpriseConfig.notifyGroupId,
    classGroupId: enterpriseConfig.classGroupId,
    isGroupOwner: enterpriseConfig.isGroupOwner,
    proxyGroupNotify: enterpriseConfig.proxyGroupNotify,
    createdTime: config.createdAt
  }

  return {
    orgToken: account.orgToken,
    id: account.wechatId,
    name: account.nickname,
    botUserId: account.botUserId,
    notifyGroupId: account.notifyGroupId,
    classGroupId: account.classGroupId,
    isGroupOwner: account.isGroupOwner,
    proxyGroupNotify: account.proxyGroupNotify,
    createdTime: account.createdTime
  }
}