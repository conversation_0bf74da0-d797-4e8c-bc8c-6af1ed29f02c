/**
 * Chat 数据库操作 - Mongoose/Typegoose 版本
 *
 * 这是 packages/service/database/chat.ts 的 MongoInstance 版本
 * 提供与原版完全相同的 API，但使用 MongoInstance 而不是 PrismaMongoClient
 */

import { Mongo_client } from 'model/typegoose/mongo_client'
import { IChatState } from '../local_cache/type'

export interface Chat {
    id: string;
    round_ids: string[];
    contact: {
        wx_id: string;
        wx_name: string;
    };
    wx_id: string;
    created_at: Date | null;
    chat_state: IChatState;
    moer_id?: string;
    is_human_involved?: boolean;
    course_no?: number;
    is_stop_group_push?: boolean;
    phone?: string;
}

/**
 * ChatDB 类 - TypeGooseMongoClient 版本
 *
 * API 与原版保持完全一致，只是底层实现从 Prisma 改为 TypeGooseMongoClient
 */
export class ChatDB {

  public static async getChatByPhone(phone: string): Promise<Chat | null> {
    const client = Mongo_client.getInstance()
    return client.chat.findFirst({
      where: { phone }
    }) as Promise<Chat | null>
  }

  public static async removeById(chat_id: string) {
    const client = Mongo_client.getInstance()
    return client.chat.delete({
      where: { _id: chat_id }  // 注意：Mongoose 使用 _id 而不是 id
    })
  }

  public static async updateState(chat_id: string, chatState: IChatState) {
    const client = Mongo_client.getInstance()
    return client.chat.update({
      where: { _id: chat_id },
      data: { chat_state: chatState }
    })
  }

  public static async create(chat: Chat): Promise<Chat> {
    const client = Mongo_client.getInstance()
    // 将 id 映射到 _id
    const chatData = { ...chat, _id: chat.id }
    delete (chatData as any).id

    return client.chat.create({
      data: chatData
    }) as Promise<Chat>
  }

  public static async pushRoundId(chatId: string, roundId: string) {
    const existingChat = await this.getById(chatId)
    if (!existingChat) {
      return
    }

    const client = Mongo_client.getInstance()
    return client.chat.update({
      where: { _id: chatId },
      data: {
        round_ids: { push: roundId }  // Prisma 风格的数组操作
      }
    })
  }

  public static async getById(id: string): Promise<Chat | null> {
    const client = Mongo_client.getInstance()
    const result = await client.chat.findUnique({
      where: { _id: id }
    })

    // 将 _id 映射回 id 以保持接口一致性
    if (result) {
      return {
        ...result,
        id: result._id
      } as Chat
    }

    return null
  }

  public static async deleteById(id: string) {
    const client = Mongo_client.getInstance()
    return client.chat.delete({
      where: { _id: id }
    })
  }

  public static async isHumanInvolvement(chatId: string): Promise<boolean> {
    const client = Mongo_client.getInstance()
    const chat = await client.chat.findUnique({
      where: { _id: chatId }
    })

    if (chat) {
      return Boolean(chat.is_human_involved)
    }

    return false
  }

  public static async setHumanInvolvement(chatId: string, isHumanInvolved: boolean) {
    const client = Mongo_client.getInstance()
    return client.chat.update({
      where: { _id: chatId },
      data: { is_human_involved: isHumanInvolved }
    })
  }

  public static async getCourseNo(chatId: string): Promise<number | null> {
    const client = Mongo_client.getInstance()
    const chat = await client.chat.findUnique({
      where: { _id: chatId }
    })

    if (chat) {
      return chat.course_no || null
    }

    return null
  }

  public static async getPhone(chatId: string): Promise<string | null> {
    const client = Mongo_client.getInstance()
    const chat = await client.chat.findUnique({
      where: { _id: chatId }
    })

    if (chat) {
      return chat.phone || null
    }

    return null
  }

  static async setStopGroupPush(chatId: string, stopPush: boolean) {
    const client = Mongo_client.getInstance()
    return client.chat.update({
      where: { _id: chatId },
      data: { is_stop_group_push: stopPush }
    })
  }

  static async getChatStateById(chat_id: string) {
    const chat = await this.getById(chat_id)

    if (chat) {
      return chat.chat_state
    } else {
      return null
    }
  }

  static async updateContact(chatId: string, userId: string, name: string) {
    const client = Mongo_client.getInstance()
    return client.chat.update({
      where: { _id: chatId },
      data: {
        contact: {
          wx_id: userId,
          wx_name: name
        }
      }
    })
  }

  /**
   * 统计方法
   */
  static async countByCourseNo(courseNo: number): Promise<number> {
    const client = Mongo_client.getInstance()
    return client.chat.count({
      where: { course_no: courseNo }
    })
  }

  static async countHumanInvolvedChats(courseNo?: number): Promise<number> {
    const client = Mongo_client.getInstance()
    const where: any = { is_human_involved: true }
    if (courseNo !== undefined) {
      where.course_no = courseNo
    }

    return client.chat.count({ where })
  }

  /**
   * Raw 查询方法 - 兼容现有的复杂查询
   */
  static async findChatsWithoutPhone(courseNo?: number) {
    const filter: any = { phone: { $exists: false } }
    if (courseNo !== undefined) {
      filter.course_no = courseNo
    }

    const client = Mongo_client.getInstance()

    return await client.chat.findRaw({
      filter,
      options: { limit: courseNo ? undefined : 50 }
    })
  }
}