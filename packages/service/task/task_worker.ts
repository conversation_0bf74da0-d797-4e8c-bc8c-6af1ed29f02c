import { Job, Worker } from 'bullmq'
import { TaskManager } from './task_manager'
import { Config } from 'config'
import { ITask, TaskStatus } from './type'
import RateLimiter from 'model/redis/rate_limiter'
import { RedisDB } from 'model/redis/redis'
import logger from 'model/logger/logger'
import { AsyncLock } from 'model/lock/lock'

export class TaskWorker {
  constructor(private taskProcessor: (chat_id: string, task_description: string) => Promise<void>, private  projectName: string) {
  }

  public static getTaskQueueName(botId: string, projectName: string) {
    return `${projectName}-planner-sop-${botId}`
  }

  public start() {
    const queueName = TaskWorker.getTaskQueueName(Config.setting.wechatConfig?.id as string, this.projectName)
    const self = this

    new Worker(queueName, async (job: Job) => {
      const data = job.data as ITask
      logger.log('process Task:', JSON.stringify(data, null, 4))

      // 如果任务已经被 被动回复完成了不执行任务
      const task = await TaskManager.getTaskById(data.id)
      if (task && task.status !== TaskStatus.TODO) {
        logger.log({ chat_id: data.chat_id }, `任务被取消，已经不是未完成状态，当前状态 ${task.status}`)
        return
      }

      if (!task) {
        return
      }

      await TaskManager.updateStatus(data.id, TaskStatus.DONE)
      await self.processTask(data.chat_id, task.description)
    }, {
      connection: RedisDB.getInstance()
    }).on('error', (err) => {
      logger.error({
        error_name: err.name,
        error_message: err.message,
        error_stack: err.stack,
      }, 'task_worker 发生未捕获错误', err)
    })
  }

  public async processTask(chatId: string, description: string) {
    // // 频率限制，12 小时不超过 5 条
    // const limiter = new RateLimiter({
    //   windowSize: 12 * 60 * 60,
    //   maxRequests: 10 // TODO revert
    // })
    //
    // const isAllowed = await limiter.isAllowed('planner_sop', chatId)
    // if (!isAllowed) {
    //   logger.log({ chat_id: chatId }, '任务被取消，超过频率限制')
    //   return
    // }

    const self = this
    const lock = new AsyncLock()
    await lock.acquire(chatId, async () => { // 防止跟对话撞车
      logger.log({ chatId: chatId }, '执行任务', description)
      await self.taskProcessor(chatId, description)
    },  { timeout:  2 * 60 * 1000 })
  }
}