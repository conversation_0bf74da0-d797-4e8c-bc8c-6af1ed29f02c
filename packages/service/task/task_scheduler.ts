import { LLM } from 'lib/ai/llm/llm_model'
import logger from 'model/logger/logger'


/**
 * 任务调度输入接口
 */
export interface ScheduleTaskInput {
  user_profile: string
  current_time: string // YYYY-MM-DD HH:MM:SS
  tasks_to_schedule: TaskToSchedule[]
  existing_schedule: ExistingTask[]

  chat_id: string
}

/**
 * 待调度任务
 */
export interface TaskToSchedule {
  task_id: string
  task_description: string
}

/**
 * 已存在的任务
 */
export interface ExistingTask {
  time: string // YYYY-MM-DD HH:MM:SS
  description: string
}

/**
 * 任务角色分配结果
 */
export interface TaskIdentificationResult {
  proactive_tasks: TaskWithReasoning[]
  reactive_materials: TaskWithReasoning[]
}

/**
 * 带推理的任务
 */
export interface TaskWithReasoning {
  reasoning: string
  task_id: string
}

/**
 * 调度计划项
 */
export interface SchedulePlanItem {
  task_id: string
  urgency_level: 'urgent' | 'normal'
  task_type: 'daily_greeting' | 'pre_class_reminder' | 'post_class_follow_up' | 'engagement_prompt' | 'value_delivery'
  scheduled_time: string // YYYY-MM-DD HH:MM:SS 或 "now"
}

/**
 * 任务调度输出接口
 */
export interface ScheduleTaskOutput {
  step1_identification_result: TaskIdentificationResult
  step2_scheduling_plan: SchedulePlanItem[]
}

/**
 * 任务调度器
 */
export class TaskScheduler {
  /**
   * 执行任务调度
   * @param input 调度输入
   * @returns 调度结果
   */
  public static async scheduleTask(input: ScheduleTaskInput): Promise<SchedulePlanItem[]> {
    try {
      const prompt = this.createPrompt(input)
      const response = await LLM.predict(prompt,
        {
          model: 'gpt-5-mini',
          responseJSON: true,
          meta: {
            chat_id: input.chat_id,
            promptName: 'task_scheduler',
          }
        }
      )

      // 解析 JSON 响应
      const result = response as ScheduleTaskOutput

      return result.step2_scheduling_plan
    } catch (error) {
      logger.error('TaskScheduler error:', error)
      return []
    }
  }

  /**
   * 创建调度 prompt
   * @param input 输入参数
   * @returns prompt 字符串
   */
  private static createPrompt(input: ScheduleTaskInput): string {
    return `你的核心职责是分析一个任务列表，并输出一个两步的规划：第一步是识别出适合主动发起的任务并说明理由；第二步是为这些主动任务制定详细的执行时间表。

## 6天股民体系化交易开悟之旅（当前课程）, 福利课 10分钟，正式课 70-80分钟一节， 20:30 基本上结束
- 福利课：开课前，需要让学员提前学习：神奇的双线合一（约7分钟）、绝密的四点共振（约8分钟）、主力出货和锁仓（约7分钟），以上内容内容来自正课里的精华切片，为的是让学员对这套交易体系有个初步了解和认可
- 第一课：19:20直播，主题为四点共振（市场合力突破的买点），讲解如何通过识别市场突破的时机，把握最优入场点，确保突破真正有效，而不是假突破。通过互动环节帮助学员理解这些概念的实际应用
- 第二课：19:20直播，主题为双线和一（上涨加速前低吸的买点），重点介绍如何在股票上涨前期找到入场点，提前布局，以抓住加速上涨的机会。课程内容通过实际案例展示这些技术的应用
- 第三课：19:20直播，主题为抄底先锋（利用散户割肉的恐慌选股），讲解如何在市场恐慌时抓住抄底机会，利用散户的恐慌进行低价买入。课程中将有大量的实战案例讲解，帮助学员掌握选股的技巧
- 第四课：19:20直播，主题为趋势拐点（利用主力黄金坑提高胜率），介绍如何识别趋势的拐点，进入“黄金坑”区域，提升交易的胜率。学员将学习如何控制风险并确保在合适的时机买入
- 第五课：19:20直播，主题为优中选优（选择最具潜力的股票），教授如何筛选符合交易体系的优质股票，进行精准投资。课程将强调通过模拟交易与实战策略的结合提升实际交易能力
- 第六课：19:20直播，主题为卖点（高纬度无延迟筹码峰用法），介绍如何根据市场情绪与筹码变化，选择合适的时机卖出股票，锁定利润。学员将学习如何在实际交易中应用所学策略，并了解未来如何将交易体系理论知识通过实战内化成技能的进阶路径\`

# 1. 输入格式

你将收到一个JSON对象作为输入，包含以下字段：

-   \`user_profile\`: 当前的客户画像以及最近的聊天记录
-   \`current_time\`: 当前的日期和时间 (YYYY-MM-DD HH:MM:SS)。
-   \`tasks_to_schedule\`: 一个需要你分析和安排的原始任务列表。
-   \`existing_schedule\`: 一个未来已安排好的任务列表。

# 2. 核心处理流程

你的输出必须包含两个步骤，严格按照以下逻辑生成：

# 第一步：任务角色分配与理由阐述

这是你的首要任务。遍历 \`tasks_to_schedule\` 列表中的每一个任务，为它分配一个"沟通角色"（\`PROACTIVE_OUTREACH\` 或 \`REACTIVE_MATERIAL\`），并为你的每一次判断提供清晰的理由。

*   **判断为适合主动发起任务的标准：**
    1. 当前有关键信息未被传递完成
    2. 临近销售节点促进成交、或在特定时间点提醒客户

*   **输出要求：** 在此步骤中，你需要生成一个包含 \`proactive_tasks\` 和 \`reactive_materials\` 两个列表的对象。每个对象内，**必须先输出 \`reasoning\` 字段，再输出 \`task_id\` 字段。**

# 第二步：为主动外呼任务排期

现在，只聚焦于你在第一步中识别出的 \`proactive_tasks\` 列表。对这个列表中的每一个任务，制定详细的执行计划。

*   **调度逻辑：**
    1.  **紧急度评估**: 判断任务是否是响应客户刚刚的实时行为。如果是，其 \`scheduled_time\` 标记为 \`"now"\`。注意，当前时间为深夜或客户画像显示其作息规律不适合立即打扰，当前客户忙等，无明显对话意愿，可结合任务队列安排延后发送。
    2.  **常规任务调度**:
        *   **任务类型分析**: 判断任务属于 \`daily_greeting\`, \`pre_class_reminder\`, \`post_class_follow_up\`, \`engagement_prompt\` 或 \`value_delivery\`。
        *   **选择黄金时间段**: 根据任务类型选择最合适的时间段（例如：问候在上午，提醒在课前，跟进在课后次日，比较花费时间的非紧急行为在午休或者下午茶时间等）。
        *   **确定具体时间点**: 在黄金时间段内选择一个具体时间，并检查与 \`existing_schedule\` 的冲突，确保间隔至少30分钟。如果存在冲突，请在黄金时间段内另选一个时间，或者调整到相邻的时间段。

*   **输出要求：** 在此步骤中，你需要生成一个 \`scheduling_plan\` 列表，其中包含所有主动任务的详细执行安排。

# 3. 输出格式

你的最终输出必须是一个JSON对象，包含 \`step1_identification_result\` 和 \`step2_scheduling_plan\` 两个部分，严格遵循以下结构。

{
  "step1_identification_result": {
    "proactive_tasks": [
      {
        "reasoning": "此处为判断该任务为主动任务的理由。",
        "task_id": "..."
      }
    ],
    "reactive_materials": [
      {
        "reasoning": "此处为判断该任务为被动素材的理由。",
        "task_id": "..."
      }
    ]
  },
  "step2_scheduling_plan": [
    {
      "task_id": "...",
      "urgency_level": "urgent" | "normal",
      "task_type": "...",
      "scheduled_time": "YYYY-MM-DD HH:MM:SS" | "now"
    }
  ]
}

# 重要提醒

1. **时间冲突检查**：在安排新任务时，必须确保与 \`existing_schedule\` 中的任务至少间隔30分钟。
2. **紧急度判断**：如果任务描述中包含"刚刚"、"立即"、"马上"等紧急词汇，且是主动任务，应标记为 urgent 或 scheduled_time 为 "now"。
3. **JSON格式**：输出必须是有效的JSON格式，不要包含任何其他文本。

# 输入数据

${JSON.stringify(input, null, 2)}

请严格按照上述JSON格式输出结果。`
  }
}
