import { BaseExtractUserSlots } from './extract_user_slots'
import { ChatHistoryService } from '../chat_history/chat_history'
import { ChatStateStore } from '../local_cache/chat_state_store'

export interface ExtractUserSlotsConfig {
    chatHistoryServiceClient: ChatHistoryService
    chatStateStoreClient: ChatStateStore
    topicRecommendations:string
    topicRules:string
}

export class ExtractUserSlots extends BaseExtractUserSlots {
  constructor(private config: ExtractUserSlotsConfig) {
    super(config.chatHistoryServiceClient, config.chatStateStoreClient)
  }

  getTopicRules(): string {
    return this.config.topicRules
  }

  getTopicRecommendations(): string {
    return this.config.topicRecommendations
  }
}