import { SystemMessagePromptTemplate } from '@langchain/core/prompts'

export class QueryRewritePrompt {
  public static async format() {
    const template = `# 意图改写
您的任务是为搜索引擎提供一个更合适的意图改写，以回答给定的问题。

{{chat_history}}

# 客户查询
{{query}}

# 要求
- 优先根据客户的原始问题生成推断问题，不偏离其语义。必要时，可以使用上下文来细化或增强问题，但不得改变原问题的核心意图
- 仅在客户问题过于模糊时，使用上下文来补充和澄清问题。如果历史聊天与客户查询无关，请专注于客户查询，不要插入历史聊天信息中的无关内容
- 专有名词：{{topicStr}}，当遇到专有名词时，无需增加其他的定语辅助描述
- 在所有推断问题中避免使用代词或模糊地引用（例如：“这个”“那个”“这些”“那些”）。始终用具体、明确的主题或问题替换

# 使用JSON输出
{
 "query":"（改写后的查询）"
}`

    return SystemMessagePromptTemplate.fromTemplate(template, { templateFormat: 'mustache' })
  }
}
