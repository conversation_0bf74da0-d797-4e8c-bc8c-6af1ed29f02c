import { LLM } from 'lib/ai/llm/llm_model'
import { XMLHelper } from 'lib/xml/xml'
import logger from 'model/logger/logger'
import ElasticSearchService from 'model/elastic_search/elastic_search'


export interface IEmbeddingOptions {
  similaritySearchReturnNumber?: number // similaritySearch返回的结果数量
  rerankReturnNumber?: number // rerankReturn返回的结果数量
  lowestScore?: number // 最低的分数
  filter?: object
}

export class MemoryRecall {
  public static indexName = 'moer_user_memory_2048d'

  public static async keywordExtraction(query: string, prompt: string) {
    const llm = new LLM({})
    try {
      const rawKeyword = await llm.predict(prompt)
      const xmlKeyword = XMLHelper.extractContents(rawKeyword, 'query')
      if (!xmlKeyword) {
        return query
      }
      return xmlKeyword[0]
    } catch (error) {
      logger.error('Error in keyword extraction:', error)
      return query
    }
  }

  public static async elasticMemorySearch(query: string, options?: IEmbeddingOptions) {
    const memoryResult = await ElasticSearchService.embeddingSearch(this.indexName, query, options?.similaritySearchReturnNumber, options?.lowestScore, options?.filter)
    if (memoryResult.length === 0) {
      return []
    } else {
      return memoryResult.map((item) => {
        return item.pageContent
      })
    }
  }

  public static async memoryRecall(query: string, chat_id: string, options?: IEmbeddingOptions) {
    if (query.startsWith('[') && query.endsWith(']')) { return '' }

    // 3 条记忆，1条最近 + 2条语义相关
    // 拉一条最近的历史记录
    const recentMemory = await this.getRecentMemory(chat_id, 1)

    const relevantMemory = await this.elasticMemorySearch(query,  options ? options : {
      similaritySearchReturnNumber: 2,
      lowestScore: 0.6,
      filter: { 'bool': { 'should':[{ 'term':{ 'metadata.chat_id': chat_id } }] } },
    })

    const memoryResult = Array.from(new Set([...recentMemory, ...relevantMemory]))

    return memoryResult.map((item) => {
      item = item.replace(/\n/g, '')
      return `- ${item}`
    }).join('\n')
  }


  public static async getRecentMemory(chat_id: string, number: number): Promise<string[]> {
    const elasticSearchResult = await ElasticSearchService.search(MemoryRecall.indexName,
      {
        'bool': {
          'should':[
            {
              'term': {
                'metadata.chat_id': chat_id
              }
            }]
        }
      }, number, {
        sort: {
          'metadata.timestamp': {
            order: 'desc'
          }
        }
      })

    return elasticSearchResult.map((item) => {
      return (item._source as any).text as string
    })
  }
}