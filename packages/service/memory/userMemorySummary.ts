import { PromptTemplate } from '@langchain/core/prompts'

interface ChatSessionGroup {
    content: string[]
    lastMessageTimestamp: Date
}

export class MemorySummaryPromptOnline {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`# 记忆提取
- 你的任务是根据以下规则，参考客户的聊天记录，生成一个以客户为中心的全面且精准的总结

## 对话记录
{{chat_history}}

## 规则限制
- 以客户为主体进行叙述
- 保持信息的完整性和准确性
- 只总结明确客户提到的信息，避免推测，如果需要可以保留客户的具体语句
- 总结要求以简洁，语言清晰和逻辑连贯的段落形式输出，切忌冗余，保证内容的直接性
- 切记不要使用markdown格式
- 如果最后要输出对于整体的分析总结，以"整体来看"来说

请按照以上要求与格式输出客户总结，若对话没有提供客户相关信息，请输出 "null"`, { templateFormat: 'mustache' }).format({
      chat_history: chat_history
    })
  }
}


export class DanmuMemorySummaryPrompt {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`客户发送的弹幕: 
{{chat_history}}

你的任务是根据以下规则，参考客户的聊天记录，生成一个以客户为中心的全面且精准的总结

要求：
- 以客户为主体进行叙述
- 保持信息的完整性和准确性
- 只总结明确客户提到的信息，避免推测，如果需要可以保留客户的具体语句
- 总结要求以简洁，语言清晰和逻辑连贯的段落形式输出，切忌冗余，保证内容的直接性
- 切记不要使用markdown格式
- 如果最后要输出对于整体的分析总结，以"整体来看"来说

请按照以上要求与格式输出客户总结，若对话没有提供客户相关信息，请输出 "null"`, { templateFormat: 'mustache' }).format({
      chat_history: chat_history
    })
  }
}


export class MemoryMergePromptOnline {
  public static async format(summarized_memory: string, previous_memory: string) {
    return PromptTemplate.fromTemplate(`# 记忆合并
- 将新记忆与旧记忆对比，删除重复内容

## 旧记忆
{previous_memory}

## 新记忆
{summarized_memory}

## 规则限制
- 基于语义级别比对，识别并删除新记忆中与老记忆重复的部分
- 语义级别完全重复则返回"null"
- 语义级别部分重复则只保留新记忆中的独特部分
- 保持原有语序和逻辑连贯性
- 切记不要使用markdown格式

直接输出去重后的新记忆内容，不加任何标题或说明`).format({
      previous_memory: previous_memory,
      summarized_memory: summarized_memory,
    })
  }
}