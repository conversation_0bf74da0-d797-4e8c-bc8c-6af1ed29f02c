import logger from 'model/logger/logger'
import { catchError } from 'lib/error/catchError'
import { ChatDB, IChat } from '../database/chat'
import { ChatHistoryService } from '../chat_history/chat_history'
import { FileHelper } from 'lib/file'
import { LLMReply, LLMReplyBaseParam } from '../llm/llm_reply'
import { PathHelper } from 'lib/path'
import { randomSleep } from 'lib/schedule/schedule'
import { MessageSender } from '../visualized_sop/common_sender'

export class Reply {
  private readonly chatDBClient: ChatDB<IChat>
  private readonly chatHistoryServiceClient: ChatHistoryService
  private readonly messageSender: MessageSender
  constructor(chatDBClient: ChatDB<IChat>, chatHistoryServiceClient: ChatHistoryService, messageSender: MessageSender) {
    this.chatDBClient = chatDBClient
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.messageSender = messageSender
  }

  public async invoke(param: LLMReplyBaseParam) {
    const llmReplyClient = new LLMReply(this.chatDBClient, this.chatHistoryServiceClient)
    await llmReplyClient.invoke({
      ...param,
      promptName: param.promptName,
      messageSender: async (line: string,
        chat_id: string,
        user_id: string,
        round_id: string,
        noSplit?: boolean,
        short_description?:string
      ) => {

        // 发送消息
        if (noSplit) {
          // 直接把文件移除掉，进行输出
          line = line.replaceAll(/\[[^\]]*]/g, '')

          return await this.messageSender.sendText(chat_id, {
            text: line,
            description:''
          }, { roundId: round_id })
        } else {
          // 将文件部分提取出来，放到文本后面。
          const parts = line.split(/\[((?:.*?)_(?:\w{4})\.(?:\w+))\]/)
          const fileRegex =   /.*?_\w{4}\.\w+/

          let textPart = ''
          const filePart: string[] = []

          for (let part of parts) {
            if (fileRegex.test(part)) {
              filePart.push(part)
            } else {
              if (part.endsWith('：') || part.endsWith(':')) {
                part = part.replace(/[：:]/, '')
              }
              textPart += part
            }
          }
          textPart = textPart.replaceAll(/\s+/g, ' ').replaceAll(/\[[^\]]*]/g, '').trim()

          if (textPart) {
            await this.messageSender.sendText(chat_id, {
              text: textPart,
              description:''
            }, {
              roundId:round_id
            })
          }

          for (const fileString of filePart) {
            // 这是一个文件名
            const description = fileString.split('_')[0]
            const extension = PathHelper.getFileExt(fileString)
            const fileName = fileString

            // 构建消息对象
            let message

            const fileUrl = `https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/rag_file/${encodeURIComponent(fileName)}`
            const [err, res]  = await catchError(FileHelper.getFileSizeFromUrl(fileUrl))
            if (err || (res && res === 0)) {
              // 文件不存在，有可能是模型幻觉，或者文件被删除了
              logger.error(`${fileName}文件不存在`)
              continue
            }

            // 根据文件后缀名判断文件类型
            switch (extension.toLowerCase ()) {
              case 'jpg':
              case 'jpeg':
              case 'png':
              case 'webp':
                await this.messageSender.sendImage(chat_id, {
                  url: fileUrl,
                  description: fileName
                }, {
                  roundId:round_id,
                  force:true
                })
                break
              case 'mp4':
              case 'avi':
              case 'mov':
              case 'wmv':
              case 'flv':
              case 'mkv':
                await this.messageSender.sendVideo(chat_id, {
                  url: fileUrl,
                  description: fileName
                }, {
                  roundId:round_id,
                  force:true
                })
                break
              default:
                await this.messageSender.sendFile(chat_id, {
                  url: fileUrl,
                  filename: fileName,
                  description: fileName
                }, {
                  roundId:round_id,
                  force:true
                })
            }

            await randomSleep(3000, 5000)
          }
        }

      }
    })
  }
}