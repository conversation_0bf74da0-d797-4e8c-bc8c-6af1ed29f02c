import { Config } from './config'

export function getChatId(contactId: string) {
  if (Config.setting.localTest) {

    return 'local' + `_${contactId}`
  }

  if (!Config.setting.wechatConfig) {
    throw new Error('wechatConfig is not set ')
  }

  return `${contactId}_${Config.setting.wechatConfig?.id}`
}

export function getUserId(chatId: string) {
  return chatId.split('_')[0]
}

export function getBotId(chatId: string) {
  return chatId.split('_')[1]
}