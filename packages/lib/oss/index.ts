import Oss, { Checkpoint } from 'ali-oss'
import { posix as path } from 'path'
import fs from 'fs'
import mime from 'mime'
import stream from 'stream'
import { clearTimeout } from 'timers'
import { PathHelper } from '../path'
import { FileHelper } from '../file'
import { Retry } from '../retry/retry'
import { generate } from 'short-uuid'

/**
 * 上传 OSS 返回参数
 */
interface IUploadFileResult {
  url: string
  bucket: string
  mime: string
}

interface ICredentials {
  region: string
  accountId: string
  accessKeyId: string
  secretAccessKey: string
  securityToken?: string
}

interface IAliyunOssBucketParam {
  region: string
  bucket: string
  internal: boolean
  domain: string
}

enum AliyunResizeSupportedImageTypes {
  // https://help.aliyun.com/document_detail/44688.html
  // 支持剪裁的图片格式 JPG、PNG、BMP、GIF、WebP、TIFF, 其中GIF格式的图片支持指定宽高缩放，不支持等比缩放（等比缩放情况下，动态图会变成静态图）。
  BMP = 'image/bmp',
  JPEG = 'image/jpeg',
  JPG = 'image/jpg',
  PNG = 'image/png',
  TIFF = 'image/tiff',
  GIF = 'image/gif',
  WEBP = 'image/webp',
}

export abstract class AliyunOssBucket {
  public readonly region: string // 所属区域
  public readonly bucket: string // bucket名称
  public readonly internal: boolean // 是否走内网访问
  public readonly credentials: ICredentials
  public readonly domain: string // 外部访问域名
  private ossClient?: Oss

  protected constructor(param: IAliyunOssBucketParam, credentials: ICredentials) {
    this.region = param.region
    this.bucket = param.bucket
    this.internal = param.internal
    this.credentials = credentials
    this.domain = param.domain
  }

  public replaceClientWithCredentials(credentials: ICredentials) { // 使用客户初始化 oss client
    this.ossClient = new Oss({
      region: this.region,
      accessKeyId: credentials.accessKeyId,
      stsToken: credentials.securityToken,
      accessKeySecret: credentials.secretAccessKey,
      bucket: this.bucket,
      internal: this.internal,
    })
  }

  private get client(): Oss {
    if (this.ossClient) {
      return this.ossClient
    }

    return new Oss({
      region: this.region,
      accessKeyId: this.credentials.accessKeyId,
      stsToken: this.credentials.securityToken,
      accessKeySecret: this.credentials.secretAccessKey,
      bucket: this.bucket,
      internal: this.internal,
    })
  }

  public static getContentType(fileName: string): string {
    const ext = path.extname(fileName)
    const defaultType = 'text/plain; charset=UTF-8'

    if (!ext) {
      return defaultType
    }

    const mimeType = mime.getType(fileName)

    if (!mimeType) {
      return defaultType
    }

    return mimeType
  }

  private static limitWidth(width: number) {
    const MAX_OSS_RESIZE_LENGTH = 4096
    const MIN_OSS_RESIZE_LENGTH = 36
    width = Math.round(width)

    if (width < 0) {
      width = MIN_OSS_RESIZE_LENGTH
    } else if (width > MAX_OSS_RESIZE_LENGTH) {
      width = MIN_OSS_RESIZE_LENGTH
    }

    return width
  }

  /**
   * 流式上传本地文件到 OSS
   * @param filepath
   * @param folder 指定文件上传到的 OSS 文件夹
   * @param mimeType
   * @private
   */
  public async upload(filepath: string, folder?: string, mimeType?: string): Promise<IUploadFileResult> {
    if (!fs.existsSync(filepath)) {
      throw new Error(`${filepath} 不存在`)
    }

    // 生成上传路径
    const fileId = generate()
    const filename = path.basename(filepath)
    let fileObjectName = path.join(fileId, filename)
    if (folder) {
      fileObjectName = path.join(folder, fileObjectName)
    }

    // 上传文件
    if (!mimeType) {
      mimeType = mime.getType(filepath) ?? 'application/octet-stream'
    }

    // 获取文件大小
    const fileSize = FileHelper.getFileSize(filepath, 'mb')
    let uploadResult: Oss.MultipartUploadResult | Oss.PutObjectResult

    if (fileSize > 100) { // 分片上传
      uploadResult = await this.multipartUpload(fileObjectName, filepath, mimeType)
    } else {
      const fileReadStream = fs.createReadStream(filepath)
      uploadResult = await this.putObjectStream(fileObjectName, fileReadStream, mimeType)
    }

    // 记录文件处理结果
    return {
      url: uploadResult.name,
      bucket: this.bucket,
      mime: mimeType,
    }
  }

  /**
   * 流式下载 OSS 文件到本地文件夹内
   * @param url oss 文件路径
   * @param directory 本地目录
   * @param filepath 下载到的文件地址
   * @return 返回下载的文件地址
   */
  public async download(url: string, directory: string, filepath?: string): Promise<string> {
    try {
      return await this.downloadStream(url, directory, filepath)
    } catch (e: any) {
      if (e.message.includes('Object not exists')) { // 存储中有脏数据，如中文 encode 后的文件名， 需要重试下载
        // 重试 decode 下载
        const decodeUrl = decodeURIComponent(url)

        return await this.downloadStream(decodeUrl, directory, filepath)
      } else {
        throw e
      }
    }
  }

  private async downloadStream(url: string, directory: string, filepath?: string): Promise<string> {
    let cacheFilepath: string

    if (filepath) {
      cacheFilepath = filepath
    } else {
      // 检查当前目录内是否有重名文件，如果有则重命名
      let filename = path.basename(url)
      filename = PathHelper.formatFilename(filename, false, 50)
      filename = await FileHelper.generateUniqueFilename(directory, filename)

      // 创建下载目录和文件
      cacheFilepath = path.join(directory, filename)

      await FileHelper.makeFolder(directory)
    }

    // 防止文件没创建，导致异步读取文件夹时出错。
    await FileHelper.touchFile(cacheFilepath)

    // 下载文件
    let getObjectResult: Oss.GetStreamResult
    try {
      getObjectResult = await this.getObjectStream(url)
      if (getObjectResult.res.status !== 200) {
        throw new Error(`下载文件失败: ${getObjectResult.res.status}`)
      }
      if (!getObjectResult.stream) {
        throw new Error('下载文件失败: stream 为空')
      }
    } catch (e: any) {
      // 下载失败删除文件
      await FileHelper.removeFile(cacheFilepath)
      throw new Error(`${url} 下载失败: ${e.message}`)
    }

    return new Promise<string>((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`下载超时:${  url  }`))
      }, 60 * 1000 * 30)

      const fileWriteStream = fs.createWriteStream(cacheFilepath, { emitClose: true, autoClose: true })
      getObjectResult.stream
        .pipe(fileWriteStream)
        .on('close', () => {
          clearTimeout(timer)
          resolve(cacheFilepath)
        })
        .on('error', () => {
          clearTimeout(timer)
          reject()
        })
    })
  }

  /**
   * 获取外网可访问的下载链接
   * @param url oss 文件路径
   * @param expires 过期时间
   */
  public async getDownloadLink(url: string, expires = 1800): Promise<string> {
    const options: Oss.SignatureUrlOptions = {
      response: {
        // 响应部分，用于浏览器下载，要提取文件名出来并 encode
        'content-disposition': `attachment; filename=${encodeURIComponent(path.basename(url))}`,
      },
      expires,
    }

    // 获取存储bucket
    const singedUrl = this.getObjectSignedUrl(url, options)

    // 替换为外部域名
    const downloadUrl = new URL(singedUrl)
    downloadUrl.hostname = new URL(this.domain).hostname
    downloadUrl.protocol = 'https:'
    return downloadUrl.href
  }

  /**
   * 上传内容到OSS
   * https://www.npmjs.com/package/ali-oss#putname-file-options
   * @param name
   * @param file 文件路径 | Buffer | ReadStream
   * @param options
   */
  public async putObject(name: string, file: string | Buffer | fs.ReadStream, options?: Oss.PutObjectOptions): Promise<Oss.PutObjectResult> {
    return Retry.retry<Oss.PutObjectResult>(3, async () => {
      const result = await this.client.put(name, file, options)

      if (result.res.status !== 200) {
        throw new Error(`上传文件失败${name}`)
      }

      return result
    })
  }

  /**
   * 以流的方式上传内容到OSS
   * https://www.npmjs.com/package/ali-oss#putname-file-options
   * @param name
   * @param readStream 文件路径 | Buffer | ReadStream
   * @param mimeType
   */
  public async putObjectStream(name: string, readStream: stream.Readable, mimeType: string): Promise<Oss.PutObjectResult> {
    return Retry.retry<Oss.PutObjectResult>(3, async () => {
      const result: any = await this.client.putStream(name, readStream, {
        mime: mimeType,
        timeout: 3 * 600 * 1000, // 普通函数最大超时时间
      } as any)

      if (result.res.status !== 200) {
        throw new Error(`上传文件失败${name}`)
      }

      return {
        url: result.url,
        name: result.name,
        res: result.res,
        data: {},
      }
    })
  }

  /**
   * 分片上传
   */
  public async multipartUpload(name: string, file: string | Buffer | fs.ReadStream, mimeType: string): Promise<Oss.MultipartUploadResult> {
    let breakPoint: Checkpoint | undefined

    return Retry.retry<Oss.MultipartUploadResult>(3, async () => {
      const result = await this.client.multipartUpload(name, file, {
        checkpoint: breakPoint,
        timeout: 3 * 600 * 1000, // 普通函数最大超时时间
        progress: async (percentage, checkpoint) => {
          breakPoint = checkpoint
        },
        mime: mimeType,
      })

      if (result.res.status !== 200) {
        throw new Error(`上传文件失败${name}`)
      }

      return result
    })
  }

  /**
   * 从OSS下载内容
   * https://www.npmjs.com/package/ali-oss#getname-file-options
   * @param name objectName
   * @param file 文件路径 | WriteStream
   * @param option
   */
  public async getObject(name: string, file?: string | fs.WriteStream, option?: Oss.GetObjectOptions): Promise<Oss.GetObjectResult> {
    return Retry.retry<Oss.GetObjectResult>(3, async () => {
      const result = await this.client.get(name, file, option)

      if (result.res.status !== 200) {
        throw new Error(`get object failed ${name}`)
      }

      return result
    })
  }

  /**
   * 以流的方式从OSS下载内容
   * https://www.npmjs.com/package/ali-oss#getname-file-options
   * @param name
   * @param option
   */
  public async getObjectStream(name: string, option?: Oss.GetObjectOptions): Promise<Oss.GetStreamResult> {
    return Retry.retry<Oss.GetStreamResult>(3, async () => {
      const result = await this.client.getStream(name, option)

      if (result.res.status !== 200) {
        throw new Error(`get object failed ${name}`)
      }

      return result
    })
  }

  /**
   * @return Promise<string>
   * @param url
   */
  public async getFileSize(url: string): Promise<number> {
    const head = await this.head(url)
    return parseInt(head['content-length'] as string, 10)
  }

  /**
   * 获取文件信息
   *
   */
  public async head(name: string, option?: Oss.HeadObjectOptions): Promise<Record<string, unknown>> {
    return Retry.retry<Record<string, unknown>>(3, async () => {
      const result = await this.client.head(name, option)

      if (result.res.status !== 200) {
        throw new Error(`get object headers failed ${name}`)
      }

      return result.res.headers as Record<string, unknown>
    })
  }

  /**
   * 删除OSS对象,目前在不同电脑上出现bug，先注释，需要时进行修改
   * @param name
   * @param option
   */
  // public async deleteObject(name: string, option?: Oss.RequestOptions): Promise<Oss.DeleteResult> {
  //   return Retry.retry<Oss.DeleteResult>(3, async () => {
  //     const result = await this.client.delete(name, option)

  //     if (result.res.status !== 200) {
  //       throw new Error(`delete object failed ${name}`)
  //     }

  //     return result
  //   })
  // }

  /**
   * 删除多个OSS对象
   * @param name
   * @param option
   */
  public async deleteObjects(name: string[], option?: Oss.DeleteMultiOptions): Promise<Oss.DeleteMultiResult> {
    return Retry.retry<Oss.DeleteMultiResult>(3, async () => {
      const result = await this.client.deleteMulti(name, option)

      if (result.res.status !== 200) {
        throw new Error(`delete objects failed ${name}`)
      }

      return result
    })
  }

  /**
   * 获取文件签名链接
   * @param name
   * @param options
   */
  public getObjectSignedUrl(name: string, options?: Oss.SignatureUrlOptions): string {
    const signatureUrl = this.client.signatureUrl(name, options)

    if (!signatureUrl) {
      throw new Error(`getObjectSignedUrl failed ${name}`)
    }

    return signatureUrl
  }

  /**
   * 获取文件签名展示链接
   * @return Promise<string>
   * @param filename
   * @param width
   */
  public getSingedDisplayUrl(filename: string, width?: number): string {
    if (width) {
      width = AliyunOssBucket.limitWidth(width) // 限制宽度为上下限内
    }

    const contentType = AliyunOssBucket.getContentType(filename)

    let processParameter: undefined | string = undefined
    if (width && Object.values<string>(AliyunResizeSupportedImageTypes).includes(contentType)) {
      processParameter = `image/resize,w_${width}`
    }

    return this.getObjectSignedUrl(filename, {
      response: {
        'content-type': contentType,
      },
      process: processParameter,
    })
  }
}
