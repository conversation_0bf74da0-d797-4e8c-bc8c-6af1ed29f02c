import { AliyunCredentials } from '../cer'
import { FreeSpiritOss } from '../../model/oss/oss'

describe('Test', function () {
  beforeAll(async () => {
    AliyunCredentials.initialize({
      region: 'cn-hangzhou',
      accountId: '****************',
      accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
      secretAccessKey: '******************************',
    })
  })

  it('oss 文件大小', async () => {
    const bucket = new FreeSpiritOss('static')
    const head = await bucket.head('README.md')
    const size = parseInt(head['content-length'] as string, 10)

    console.log(size / 1024)
    console.log(typeof parseInt(head['content-length'] as string, 10))
  })

  it('upload', async () => {
    const bucket = new FreeSpiritOss('static')

    const res = await bucket.upload('package.json')
    console.log(JSON.stringify(res, null, 4))
  }, 30000)

  it('下载', async () => {
    const bucket = new FreeSpiritOss('static')
    await bucket.download(
      'static/oaqY6iNEECCU4W8ysBDtPR/Untitled Database 08c2d01fc68b40a0a7e394886f5ba959.csv',
      __dirname,
    )
  }, 1e8)

  it('getDisplayUrl', async () => {
    const bucket = new FreeSpiritOss('static')

    const url = bucket.getSingedDisplayUrl('static/2vyUwdskjmaqjppkMBRhf/image.png', 48)
    console.log(url)
  }, 30000)
})
