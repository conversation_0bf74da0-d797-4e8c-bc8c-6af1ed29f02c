import { AliyunCredentials } from '../cer'
import { FreeSpiritOss } from '../../model/oss/oss'
import { HttpHelper } from './httpHelper'

describe('Test', function () {
  beforeAll(async () => {
    AliyunCredentials.initialize({
      region: 'cn-hangzhou',
      accountId: '****************',
      accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
      secretAccessKey: '******************************',
    })
  })

  it('download', async () => {
    const url:string = 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/2a850be9-9a00-4207-9d52-8bff2b30c61d/bc2aae67-5b3e-465c-a470-925fd6cc3c72.jpg'
    const saveDirectory = '/Users/<USER>/Downloads/JYS_Workspace/Moer/wechaty_bot/dev/class_group_message_send/class_group_images_and_videos/1.jpg'
    await HttpHelper.downloadFile(url, saveDirectory)
  }, 3E8)

  it('装换image为不缩略', async () => {
    //const originalImageResponse = await JuziAPI.getOriginalImage(message.chatId, message.messageId as string)
    // if (originalImageResponse.code !== 0) {
    //   return await this.handleUnknownMessageType(message)
    // }
    //const imageOcrUrl = originalImageResponse.data.url
  })

  it('upload', async () => {
    const bucket = new FreeSpiritOss('static')

    const ossFolder = 'class_group_files'
    const res = await bucket.upload('/Users/<USER>/Downloads/JYS_Workspace/Moer/wechaty_bot/dev/class_group_message_send/class_group_images_and_videos/1.jpg', ossFolder)
    console.log(JSON.stringify(res, null, 4))
    const halfNewUrl = res.url
    const newUrl = `https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/${halfNewUrl}`
    console.log(JSON.stringify(newUrl, null, 4))
  }, 30000)
})