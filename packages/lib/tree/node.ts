/**
 * 模拟树节点，用来做树的节点测试
 */
export class DummyNode {
  [key: string]: any

  public children: DummyNode[]
  public parent?: DummyNode

  constructor(obj: Record<string, unknown>) {
    Object.assign(this, obj)

    this.children = []
  }

  public addChild(child: DummyNode): void {
    this.children.push(child)
    child.parent = this
  }

  public clone(): DummyNode {
    return new DummyNode(this)
  }

  public static clone(node: Record<string, unknown>): DummyNode {
    return new DummyNode(node)
  }
}
