interface TreeNode {
  children?: TreeNode[]

  [key: string]: any
}


export class TreeWalk {
  public static bfs<T extends TreeNode>(node: T, callback: (node: T) => void): void {
    if (!node) {
      return
    }

    const queue: T[] = [node]

    while (queue.length > 0) {
      const current = queue.shift() as T
      callback(current)

      if (current.children) {
        current.children.forEach((child) => queue.push(child as T))
      }
    }
  }


  public static dfs<T extends TreeNode>(node: T, callback: (node: T) => any): void {
    callback(node)

    if (node.children) {
      node.children.forEach((child) => TreeWalk.dfs(child as T, callback))
    }
  }

  /**
   * 遍历树，返回满足条件的所有节点
   * @param root
   * @param filter
   */
  public static filter<T extends TreeNode>(root: T, filter: (node: T) => boolean): T[] {
    const nodes: T[] = []
    const pushNode = (node: T) => {
      if (filter(node)) {
        nodes.push(node)
      }
    }

    this.dfs(root, pushNode)

    return nodes
  }

  // 使用 bfs 搜索满足条件的节点, 搜索成功后会直接返回。只会返回第一个满足条件的节点。
  public static async search<T extends TreeNode>(node: T, callback: (node: T) => Promise<boolean>): Promise<T | null> {
    if (!node) {
      return null
    }

    const queue: T[] = [node]

    while (queue.length > 0) {
      const current = queue.shift() as T

      if (await callback(current)) {
        return current
      }

      if (current.children) {
        current.children.forEach((child) => queue.push(child as T))
      }
    }

    return null
  }
}
