import { LRUCache } from 'lru-cache'

type AnyFn = (...args: any[]) => any

export interface MemoizeOptions<F extends AnyFn> {
    /** LRU 容量（条目个数），默认 100 */
    max?: number
    /** 自定义 key 生成器；返回同一字符串则视为相同入参 */
    cacheKey?: (args: Parameters<F>, ctx: unknown) => string
    /** 是否把 this 纳入 key；默认 true，且按“实例身份”而非深序列化 */
    includeThisInKey?: boolean
    /** Promise reject 是否也缓存；默认 false（失败剔除，便于重试） */
    cacheRejected?: boolean
    /** 透传给 lru-cache 的其它可选项（如 ttl），除 max 外 */
    lruOptions?: Omit<ConstructorParameters<typeof LRUCache<string, any>>[0], 'max'>
}

type Memoized<F extends AnyFn> = F & {
    cache: LRUCache<string, any>
    clear(): void
    keyFor(args: Parameters<F>, ctx?: unknown): string
    deleteByArgs(args: Parameters<F>, ctx?: unknown): boolean
}

/** —— 给每个实例分配稳定的身份 token（WeakMap 不阻止 GC） —— */
const thisIdMap = new WeakMap<object, number>()
let thisIdSeq = 0
function thisToken(ctx: unknown): string {
  if ((typeof ctx === 'object' && ctx !== null) || typeof ctx === 'function') {
    let id = thisIdMap.get(ctx as object)
    if (id === undefined) {
      id = ++thisIdSeq
      thisIdMap.set(ctx as object, id)
    }
    return `this#${id}`
  }
  return `this:${String(ctx)}`
}

/** —— 稳定序列化参数（支持循环/Map/Set/Date/RegExp/TypedArray 等） —— */
function stableStringify(value: unknown, seen = new Map<any, number>()): string {
  const t = typeof value
  if (value === null) return 'null'
  if (t === 'undefined') return 'u'
  if (t === 'boolean') return `b:${value ? 1 : 0}`
  if (t === 'number') {
    if (Number.isNaN(value)) return 'num:NaN'
    if (Object.is(value, -0)) return 'num:-0'
    return `num:${value}`
  }
  if (t === 'bigint') return `big:${String(value)}n`
  if (t === 'string') return `str:${JSON.stringify(value)}`
  if (t === 'symbol') return `sym:${String((value as symbol).description ?? '')}`
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  if (t === 'function') return `fn:${(value as Function).name || 'anonymous'}`

  if (value instanceof Date) return `date:${value.toISOString()}`
  if (value instanceof RegExp) return `re:${value.toString()}`

  if (ArrayBuffer.isView(value)) {
    const u8 = new Uint8Array((value as any).buffer, (value as any).byteOffset, (value as any).byteLength)
    return `ta:${(value as any).constructor?.name}:[${Array.from(u8).join(',')}]`
  }

  if (typeof value === 'object') {
    if (seen.has(value)) return `ref:${seen.get(value)}`
    seen.set(value, seen.size + 1)

    if (Array.isArray(value)) {
      return `arr:[${value.map((v) => stableStringify(v, seen)).join(',')}]`
    }

    if (value instanceof Map) {
      const entries = Array.from(value.entries()).map(
        ([k, v]) => [stableStringify(k, seen), stableStringify(v, seen)] as [string, string]
      )
      entries.sort((a, b) => (a[0] < b[0] ? -1 : a[0] > b[0] ? 1 : a[1].localeCompare(b[1])))
      return `map:{${entries.map(([k, v]) => `${k}=>${v}`).join(',')}}`
    }

    if (value instanceof Set) {
      const items = Array.from(value.values()).map((v) => stableStringify(v, seen)).sort()
      return `set:{${items.join(',')}}`
    }

    const proto = Object.getPrototypeOf(value)
    const ctor = proto?.constructor?.name && proto.constructor !== Object ? proto.constructor.name : ''
    const keys = Object.keys(value as object).sort()
    const body = keys.map((k) => `${JSON.stringify(k)}:${stableStringify((value as any)[k], seen)}`).join(',')
    return `obj${ctor ? `(${ctor})` : ''}{${body}}`
  }

  return JSON.stringify(value)
}

function defaultKey(args: unknown[], ctx: unknown, includeThisInKey: boolean): string {
  const payload = includeThisInKey ? [thisToken(ctx), ...args] : args
  return stableStringify(payload)
}

/** —— 主函数 —— */
export function memoize<F extends AnyFn>(fn: F, opts: MemoizeOptions<F> = {}): Memoized<F> {
  const {
    max = 100,
    cacheRejected = false,
    includeThisInKey = true, // ✅ 默认按实例身份区分
    cacheKey,
    lruOptions
  } = opts

  const cache = new LRUCache<string, any>({ max, ...lruOptions })

  const keyFor = (args: Parameters<F>, ctx?: unknown) =>
    (cacheKey ?? ((a, c) => defaultKey(a, c, includeThisInKey)))(args, includeThisInKey ? ctx : undefined)

  const memoized = function (this: unknown, ...args: any[]) {
    const key = keyFor(args as Parameters<F>, this)
    if (cache.has(key)) {
      console.log('hit cache', key)
      return cache.get(key)
    }

    let value: any
    try {
      value = fn.apply(this, args)
    } catch (err) {
      if (!cacheRejected) cache.delete(key)
      throw err
    }

    if (value && typeof value.then === 'function') {
      const p = (value as Promise<any>)
        .then((res) => res)
        .catch((err) => {
          if (!cacheRejected) cache.delete(key)
          throw err
        })
      cache.set(key, p)
      return p
    }

    cache.set(key, value)
    return value
  } as unknown as Memoized<F>

  memoized.cache = cache
  memoized.clear = () => cache.clear()
  memoized.keyFor = keyFor
  memoized.deleteByArgs = (args: Parameters<F>, ctx?: unknown) => cache.delete(keyFor(args, ctx))

  return memoized
}