import { memoize } from '../memoize'

const delay = (ms: number) => new Promise((res) => setTimeout(res, ms))

describe('memoize (Jest)', () => {
  beforeEach(() => {
    jest.useRealTimers()
    jest.clearAllMocks()
  })
  afterEach(() => {
    jest.useRealTimers()
  })

  test('同步函数：相同参数只执行一次', () => {
    const impl = jest.fn((a: number, b: number) => a + b)
    const add = memoize(impl, { max: 100 })

    expect(add(1, 2)).toBe(3)
    expect(add(1, 2)).toBe(3)
    expect(impl).toHaveBeenCalledTimes(1)

    expect(add(2, 1)).toBe(3) // 顺序不同 → 重新计算
    expect(impl).toHaveBeenCalledTimes(2)
  })

  test('异步函数：并发同 key 只触发一次（共享 Promise）', async () => {
    const impl = jest.fn(async (id: string) => {
      await delay(20)
      return `u:${id}`
    })
    const getUser = memoize(impl)

    const [a, b, c] = await Promise.all([getUser('42'), getUser('42'), getUser('42')])
    expect(a).toBe('u:42')
    expect(b).toBe('u:42')
    expect(c).toBe('u:42')
    expect(impl).toHaveBeenCalledTimes(1)

    const d = await getUser('42') // 已缓存
    expect(d).toBe('u:42')
    expect(impl).toHaveBeenCalledTimes(1)
  })

  test('异步：默认不缓存 reject（cacheRejected=false）', async () => {
    let n = 0
    const impl = jest.fn(async () => {
      n++
      await delay(5)
      throw new Error(`boom${  n}`)
    })
    const fn = memoize(impl, { cacheRejected: false })

    await expect(fn()).rejects.toThrow('boom1')
    await expect(fn()).rejects.toThrow('boom2') // 未缓存，第二次又执行
    expect(impl).toHaveBeenCalledTimes(2)
  })

  test('异步：缓存 reject（cacheRejected=true）', async () => {
    const impl = jest.fn(async () => {
      await delay(5)
      throw new Error('fatal')
    })
    const fn = memoize(impl, { cacheRejected: true })

    await expect(fn()).rejects.toThrow('fatal')
    await expect(fn()).rejects.toThrow('fatal') // 命中缓存中的 rejected promise
    expect(impl).toHaveBeenCalledTimes(1)
  })

  test('默认纳入 this：不同实例不共享缓存', () => {
    class Svc {
      constructor(public base: number) {}
      calc = memoize(function (this: Svc, x: number) {
        return this.base * x
      })
    }
    const a = new Svc(2)
    const b = new Svc(3)

    expect(a.calc(10)).toBe(20)
    expect(a.calc(10)).toBe(20) // 命中
    expect(b.calc(10)).toBe(30) // 不同实例 → 重新计算
  })

  test('关闭 this 参与：不同实例共享缓存', () => {
    class Svc {
      constructor(public tag: string) {}
      calc = memoize(function (this: Svc, x: number) {
        return x * 2
      }, { includeThisInKey: false })
    }
    const a = new Svc('A')
    const b = new Svc('B')

    const s1 = a.calc(5)
    const s2 = b.calc(5) // 共享
    expect(s1).toBe(10)
    expect(s2).toBe(10)
  })

  test('自定义 cacheKey：只按部分字段', async () => {
    const impl = jest.fn(async (q: { id: string; fields?: string[] }) => {
      await delay(1)
      return { id: q.id, ok: true }
    })
    const getUser = memoize(impl, {
      cacheKey: ([q]) => `user:${q.id}`
    })

    await getUser({ id: '1', fields: ['a'] })
    await getUser({ id: '1', fields: ['b', 'c'] })
    expect(impl).toHaveBeenCalledTimes(1)
  })

  test('LRU：超过 max 淘汰最旧条目', () => {
    const impl = jest.fn((x: number) => x * 10)
    const f = memoize(impl, { max: 2 })

    expect(f(1)).toBe(10) // 缓存: [1]
    expect(f(2)).toBe(20) // 缓存: [1,2]
    expect(f(3)).toBe(30) // 淘汰 1 → [2,3]
    expect(impl).toHaveBeenCalledTimes(3)

    expect(f(2)).toBe(20) // 命中
    expect(f(1)).toBe(10) // 1 被淘汰，需要重算
    expect(impl).toHaveBeenCalledTimes(4)
  })

  test('TTL：过期后重算（假时钟）', () => {
    jest.useFakeTimers()
    const impl = jest.fn((x: number) => x + 1)
    const f = memoize(impl, { lruOptions: { ttl: 100 } }) // 100ms

    expect(f(1)).toBe(2)
    expect(impl).toHaveBeenCalledTimes(1)

    jest.advanceTimersByTime(90)
    expect(f(1)).toBe(2) // 未过期
    expect(impl).toHaveBeenCalledTimes(1)

    jest.advanceTimersByTime(20) // 共 110ms
    expect(f(1)).toBe(2) // 过期重算
    expect(impl).toHaveBeenCalledTimes(2)

    jest.useRealTimers()
  })

  test('复杂参数：循环/Map/Set/Date/RegExp/TypedArray 等稳定 key', () => {
    const impl = jest.fn((x: any) => JSON.stringify(x) ?? '')
    const f = memoize(impl)

    const a: any = { v: 1 }
    a.self = a

    const m1 = new Map<any, any>([[{ k: 2 }, 'b'], [1, 'a']])
    const m2 = new Map<any, any>([[1, 'a'], [{ k: 2 }, 'b']])

    const s1 = new Set<any>([3, 1, 2])
    const s2 = new Set<any>([2, 1, 3])

    const d1 = new Date('2020-01-01T00:00:00.000Z')
    const d2 = new Date('2020-01-01T00:00:00.000Z')

    const r1 = /foo/gi
    const r2 = new RegExp('foo', 'ig')

    const t1 = new Uint16Array([1, 2, 3])
    const t2 = new Uint16Array([1, 2, 3])

    f(a)
    f(m1)
    f(s1)
    f(d1)
    f(r1)
    f(t1)

    f(a)   // 同对象
    f(m2)  // 等价 Map
    f(s2)  // 等价 Set
    f(d2)  // 同时间
    f(r2)  // 等价 RegExp
    f(t2)  // 同内容 TypedArray

    expect(impl).toHaveBeenCalledTimes(6)
  })

  test('clear / deleteByArgs：精确清理缓存', () => {
    const impl = jest.fn((x: number) => x * x)
    const f = memoize(impl)

    expect(f(2)).toBe(4)
    expect(f(2)).toBe(4)
    expect(impl).toHaveBeenCalledTimes(1)

    f.deleteByArgs([2])
    expect(f(2)).toBe(4)
    expect(impl).toHaveBeenCalledTimes(2)

    f.clear()
    expect(f(2)).toBe(4)
    expect(impl).toHaveBeenCalledTimes(3)
  })

  test('同步异常不应污染缓存（默认不缓存失败）', () => {
    let n = 0
    const impl = jest.fn((x: number) => {
      n++
      if (n <= 2) throw new Error('oops')
      return x + 1
    })
    const f = memoize(impl)

    expect(() => f(1)).toThrow('oops')
    expect(() => f(1)).toThrow('oops')
    expect(f(1)).toBe(2)
    expect(impl).toHaveBeenCalledTimes(3)
  })

  test('includeThisInKey=true 时，deleteByArgs 需带上 ctx', () => {
    class C {
      v = 10
      calc = memoize(function (this: C, x: number) {
        return this.v + x
      }, { includeThisInKey: true })
    }
    const a = new C()
    const b = new C()

    expect(a.calc(1)).toBe(11)
    expect(b.calc(1)).toBe(11)

    a.calc.deleteByArgs([1], a as any) // 只删 a 的
    expect(a.calc(1)).toBe(11) // 重算
    expect(b.calc(1)).toBe(11) // 仍命中
  })

  test('class', () => {
    class C {
      public static fk(a: number) {
        return a
      }
    }


    const f = memoize(C.fk)
    f(1)
    f(1)
    f(2)
    f(1)
  })

})