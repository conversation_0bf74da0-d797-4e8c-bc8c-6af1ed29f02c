export async function catchError<T>(promise: Promise<T>): Promise<[undefined, T] | [Error]> {
  return promise
    .then((data) => {
      return [undefined, data] as [undefined, T]
    })
    .catch((err) => {
      return [err] as [Error]
    })
}



/**
 * 同步版本：处理可能抛出异常的同步函数
 */
export function catchErrorSync<T>(
  fn: () => T
): [undefined, T] | [Error] {
  try {
    return [undefined, fn()] as [undefined, T]
  } catch (err) {
    return [err as Error]
  }
}