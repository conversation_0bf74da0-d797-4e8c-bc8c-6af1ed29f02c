import { XMLParser } from 'fast-xml-parser'

export class XMLHelper {
  public static parse(xml: string): any {
    const parser = new XMLParser()
    return parser.parse(xml)
  }

  /**
     * 提取指定标签内的内容
     * @param xml
     * @param tagName 标签名
     */
  public static extractContent (xml: string, tagName: string): string | null {
    // 使用 RegExp 构造函数来动态创建正则表达式，注意对 tagName 进行转义以避免注入问题
    const escapedTagName = tagName.replace (/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
    const pattern = new RegExp (`<${escapedTagName}>\\s*([\\s\\S]*?)\\s*</${escapedTagName}>`, 'i')

    const match = xml.match (pattern)
    return match ? match [1].trim() : null
  }

  /**
   * 用于提取多个同名 xml 标签中的内容
   * @param xml
   * @param tagName
   */
  public static extractContents(xml: string, tagName: string): string[] | null {
    // 使用 RegExp 构造函数来动态创建正则表达式，注意对 tagName 进行转义以避免注入问题
    const escapedTagName = tagName.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
    const pattern = new RegExp(`<${escapedTagName}>\\s*([\\s\\S]*?)\\s*</${escapedTagName}>`, 'gi')

    const matches = xml.matchAll(pattern)
    const results: string[] = []

    for (const match of matches) {
      if (match[1]) {
        results.push(match[1].trim())
      }
    }
    return results.length > 0 ? results : null
  }
}