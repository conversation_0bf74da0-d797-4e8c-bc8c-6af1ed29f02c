import { <PERSON>SO<PERSON><PERSON>el<PERSON> } from './json'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(JSON.stringify(JSONHelper.parse('{fk: you, your: mother, fker: !}'), null, 4))
    console.log(JSON.stringify(JSONHelper.parse('{fk: you, your: mother, fker: !}'), null, 4))
  })

  it('parse', async () => {
    console.log(JSONHelper.parse('[\'1\', \'2\']'))
  }, 60000)

  it('json part', async () => {
    console.log(JSON.stringify(JSONHelper.extractJSONPart('Hello, mother fker？{fk: you, your: mother, fker: !}'), null, 4))

    console.log(JSON.stringify(JSONHelper.extractJSONPart(`{
    fk: {fk: you}
    }`), null, 4))
  }, 30000)
})