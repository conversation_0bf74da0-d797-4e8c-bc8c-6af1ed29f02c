import { jsonrepair } from 'jsonrepair'
import fs from 'fs'
export class JSONHelper {
  public static parseJSONLFromFile(filePath: string) {
    try {
      const data = fs.readFileSync(filePath, 'utf8')
      return this.parseJSONL(data)
    } catch (e) {
      return null
    }
  }

  public static parseJSONL(data: string) {
    return data.split('\n').map((line) => {
      try {
        return JSON.parse(line)
      } catch (e) {
        console.log('line', line)
      }
    })
  }

  public static parseFromFile(filePath: string) {
    try {
      const data = fs.readFileSync(filePath, 'utf8')
      return this.parse(data)
    } catch (e) {
      return null
    }
  }
  public static parse(data: string) {
    try {
      // 使用 JSON 自动修复
      const repaired = jsonrepair(data)

      return JSON.parse(repaired)
    } catch (e) {
      return null
    }
  }

  public static extractJSONPart(data: string) {
    const jsonRegex = /{\s*.*\s*}/s
    const match = data.match(jsonRegex)
    return match && match.length > 0 ? match[0] : null
  }
}