/*
 * markdownChunker.ts –Tiny utility to split Markdown‑ish text into logical “blocks”
 * ------------------------------------------------------------
 * ‣ Focuses on clarity & linear‑time performance rather than a single gigantic regex.
 * ‣ Supports: ATX & Setext headings, horizontal rules, fenced code, blockquotes,
 *   list items (ordered / unordered / task), blank‑line paragraphs and HTML blocks.
 * ‣ Easy to extend: just add another `isXXX` predicate and slot it into the switch.
 *
 * Apache‑2.0 ©2024‑2025 JinaAI (Example implementation for demo purposes)
 */
export function chunkMarkdown(text: string): string[] {
  // Normalise line endings and split once.
  const lines = text.replace(/\r\n?/g, '\n').split('\n')

  const chunks: string[] = []
  let buffer: string[] = []
  let inFence = false
  let fenceMarker = ''

  const flush = () => {
    if (buffer.length) {
      chunks.push(buffer.join('\n').trimEnd())
      buffer = []
    }
  }

  // ---------- tiny helpers ---------- //
  const re = {
    fenceStart: /^(\s*)(```|~~~)/,
    atxHeading: /^ {0,3}#{1,6}(?:\s+|$)/,
    hr: /^ {0,3}(-{3,}|_{3,}|\*{3,})\s*$/,
    list: /^ {0,3}(?:[-*+]\s+|\d+\.\s+|\[?[ xX]\]\s+)/,
    blockquote: /^ {0,3}>\s?/,
    setextUnderline: /^[-=]{2,}\s*$/,
    htmlBlockOpen: /^<([A-Za-z][A-Za-z0-9]*)\b[^>]*?>/,
    htmlBlockClose: /^<\/(?:[A-Za-z][A-Za-z0-9]*)>/
  } as const

  const isFenceStart = (l: string) => re.fenceStart.test(l)
  const isHeading = (l: string, idx: number) => re.atxHeading.test(l) ||
    (idx + 1 < lines.length && re.setextUnderline.test(lines[idx + 1]))
  const isHr = (l: string) => re.hr.test(l)
  const isListItem = (l: string) => re.list.test(l)
  const isBlockquote = (l: string) => re.blockquote.test(l)
  const isHtmlBlockOpen = (l: string) => re.htmlBlockOpen.test(l)
  const isHtmlBlockClose = (l: string) => re.htmlBlockClose.test(l)

  // ---------- main sweep ---------- //
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]

    // Inside fenced code: keep going until closing fence.
    if (inFence) {
      buffer.push(line)
      if (line.startsWith(fenceMarker)) {
        inFence = false
        flush()
      }
      continue
    }

    // Blank line → paragraph boundary.
    if (line.trim() === '') {
      flush()
      continue
    }

    // Fenced code start.
    if (isFenceStart(line)) {
      flush()
      inFence = true
      fenceMarker = line.match(re.fenceStart)![2] // ``` or ~~~
      buffer.push(line)
      continue
    }

    // Block‑type single‑line constructs.
    if (isHeading(line, i) || isHr(line) || isListItem(line) || isBlockquote(line)) {
      flush()
      buffer.push(line)

      // include Setext underline if present
      if (isHeading(line, i) && re.setextUnderline.test(lines[i + 1] ?? '')) {
        buffer.push(lines[++i])
      }

      flush()
      continue
    }

    // Simple HTML blocks (start and end tags on their own lines)
    if (isHtmlBlockOpen(line)) {
      flush()
      buffer.push(line)
      for (i = i + 1; i < lines.length && !isHtmlBlockClose(lines[i]); i++) {
        buffer.push(lines[i])
      }
      if (i < lines.length) buffer.push(lines[i])
      flush()
      continue
    }

    // Default → part of a paragraph.
    buffer.push(line)
  }

  flush()
  return chunks.filter(Boolean)
}
