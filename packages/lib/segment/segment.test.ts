import { chunkMarkdown } from './segment'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(JSON.stringify(chunkMarkdown(`收到，是王女士对吧，你的新店做滋补这个品类，年营业额还没跑起来，你对AI数字人也是完全不懂，看到你最关心新店装修怎么开始…

我直接说句真心话，现在开实体店，不结合抖音线上的打法和获客，你就是在闭门造车，尤其滋补类的赛道，客户决策周期长、自己说不明白核心价值，很容易铺得一地鸡毛。装修其实不是最大难题，大部分人是装完了才发现没人进店——流量起不来，一切白搭

还有你对AI和抖音的认知现在属于“空白区”，这反而是机会，因为你没被那些野路子误导过。装修和数字化运营这块，课上都有标准流程还有案例，不是平台硬堆料那套，而是教你怎么一步一步搞定前端引流、空间布置、AI助手嫁接的

关键三点啊，一定要回答我：
- 抖音，你现在做了吗？有发过内容吗？效果怎样？
- 你这个店就你自己在做，还是连锁？背后有团队没？
- 店开在哪里？哪个城市？这直接影响到本地流量打法

你把这三点补全，我给你精确梳理下课上该怎么用好这些工具和玩法`), null, 4))
  }, 60000)

  it('123', async () => {
    console.log(JSON.stringify(chunkMarkdown(`这篇文章包含markdown语法基本的内容, 目的是放在自己的博客园上, 通过开发者控制台快速选中,  
从而自定义自己博客园markdown样式.当然本文也可以当markdown语法学习之用.  

在markdown里强制换行是在末尾添加2个空格+1个回车.  
在markdown里可以使用 \\ 对特殊符号进行转义.  

# 1. 标题

**语法**
\`\`\`
# This is an <h1> tag
## This is an <h2> tag
### This is an <h3> tag
#### This is an <h4> tag
\`\`\`

**实例**

# This is an h1 tag
## This is an h2 tag
### This is an h3 tag
#### This is an h4 tag

# 2. 强调和斜体

**语法**
\`\`\`
*This text will be italic*
_This will also be italic_

**This text will be bold**
__This will also be bold__
\`\`\`
(个人不喜欢2个下划线中间包含的内容被斜体, 会和网址冲突, 我会在自定义博客园样式中去除这个样式.)  

**实例**

*This text will be italic*
_This will also be italic_

**This text will be bold**
__This will also be bold__

# 3. 有序列表和无序列表

**语法**
\`\`\`
* Item 1
* Item 2
* Item 3

1. Item 1
2. Item 2
3. Item 3
\`\`\`

**实例**
* Item 1
* Item 2
* Item 3

1. Item 1
2. Item 2
3. Item 3

# 4. 图片

**语法**
\`\`\`
![img-name](img-url)
\`\`\`

**实例**
![博客园logo](https://news.cnblogs.com/images/logo.gif)

# 5. 超链接

**语法**
\`\`\`
[link-name](link-url)
\`\`\`

**实例**

[阿胜4K](http://www.cnblogs.com/asheng2016/)

# 6. 引用

**语法**
\`\`\`
> 引用本意是引用别人的话之类  
> 但我个人喜欢把引用当成"注意"使用  
\`\`\`

**实例**

> If you please draw me a sheep!  
> 不想当将军的士兵, 不是好士兵.  

# 7. 单行代码

**语法**
\`\`\`
\`This is an inline code.\`
\`\`\`

**实例**

\`同样的单行代码, 我经常用来显示特殊名词\`

# 8. 多行代码

**语法**
\`\`\`\`
​\`\`\`javascript
for (var i=0; i<100; i++) {
    console.log("hello world" + i);
}
​\`\`\`
\`\`\`\`

**实例**

\`\`\`js
for (var i=0; i<100; i++) {
    console.log("hello world" + i);
}
\`\`\`

也可以通过缩进来显示代码, 下面是示例:  

    console.loe("Hello_World");

# 参考链接

https://guides.github.com/features/mastering-markdown/  
https://help.github.com/articles/basic-writing-and-formatting-syntax/  `), null, 4))
  }, 60000)
})