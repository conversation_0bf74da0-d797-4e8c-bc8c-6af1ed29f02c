import { Csv<PERSON>el<PERSON> } from './csv_parse'
import path from 'path'
import Papa from 'papaparse'


describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    CsvHelper.write(path.join(__dirname, 'test.csv'), [{ '测试': '你妈', '没了': 'hehe' }])
  }, 60000)

  it('2d json', async () => {
    CsvHelper.write2DJson(path.join(__dirname, 'test.csv'), {
      '小张': {
        '姓名': '小张',
        '身高': '175'
      },
      '小王': {
        '姓名': '小王',
        '身高': '180'
      }
    }, ['1', '2'])
  }, 60000)

})