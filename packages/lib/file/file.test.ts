import { <PERSON>Helper } from './index'


import { FolderWalker } from './folder_walker/walker'
import { FileNode } from './folder_walker/filenode'
import { PathHelper } from '../path'
import * as path from 'path'
import { ObjectUtil } from '../object'
import { TreeWalk } from '../tree/walker'

import axios from 'axios'
import { FreeSpiritOss } from '../../model/oss/oss'

describe('FileHelper Test', function () {
  it('getUrlFileSize', async () => {
    const res = await axios.head('https://ac-java-user.oss-cn-shanghai.aliyuncs.com/weremote/chat-logs/091A9740B8254CF8832ACD9F5F717071/4c1267464c29b5ba675866fa93833c1d/3/3730337346074289531.jpg')
    const head = res.headers
    console.log(parseInt(head['content-length'] as string, 10) / 1024) // KB
  }, 30000)


  it('检查文件是否存在', async () => {
    expect(await FileHelper.isExist(__dirname)).toBe(true)
    expect(await FileHelper.isExist('')).toBe(false)
  }, 30000)

  it('生成唯一文件名', async () => {
    expect(await FileHelper.generateUniqueFilename(__dirname, 'index.ts')).toBe('index_1.ts')
    expect(await FileHelper.generateUniqueFilename(__dirname, 'a-b-c')).toBe('a-b-c_1')
  })

  it('替换合法文件名', async () => {
    expect(await PathHelper.formatFilename('f?:i/le>  你%好|,世\\\\界.ext')).toBe('f--i-le-  你-好-,世--界.ext')
    expect(await PathHelper.formatFilename('2020/1/2')).toBe('2020-1-2')
  })

  it('创建文件夹', async () => {
    const testFolder = path.join(__dirname, 'a-b-c', 'a')
    await FileHelper.makeFolder(testFolder)
    expect(await FileHelper.isExist(testFolder) && await FileHelper.isFolder(testFolder)).toBe(true)
  })

  it('获取文件大小', async () => {
    const testFile = path.join(__dirname, 'i123.ts')
    expect(await FileHelper.getFileSize(testFile)).toBe(0)
  }, 30000)

  it('Touch File', async () => {
    const testFile = path.join(__dirname, 'i123.ts')
    await FileHelper.touchFile(testFile)
  }, 30000)


  it('文件列表', async () => {
    const rootFolder = process.cwd()
    console.log(await FileHelper.listFiles(path.join(rootFolder, 'resource', 'emotion')))
  }, 30000)

  it('emojiMap', async () => {
    const emojiMap = new Map<string, string[]>()
  }, 30000)

  it('todo', async () => {
    // 把目录批量上传到 oss, 并获取地址

  }, 30000)

  it('获取表情oss 地址', async () => {
    const walker = new FolderWalker(path.join(process.cwd(), 'resource', 'emotion'))

    const fileTree =  await walker.buildFileTree()

    const emojiMap = new Map<string, string[]>() // emojiType -> filepaths

    TreeWalk.dfs(fileTree, (fileNode: FileNode) => {
      if (fileNode.type === 'file') {
        // 上级目录名作为类型
        const emojiType = path.basename(path.dirname(fileNode.filepath))

        if (!emojiMap.has(emojiType)) {
          emojiMap.set(emojiType, [])
        }

        const emojis = emojiMap.get(emojiType)
        if (emojis) {
          emojis.push(fileNode.filepath)
        }
      }
    })

    emojiMap.delete('emotion')

    const emojiOssMap = new Map<string, string[]>()

    // 把文件批量上传到 oss, 并获取公开链接
    for (const emojiMapElement of emojiMap) {
      const [emojiType, filePaths] = emojiMapElement

      for (const filePath of filePaths) {
        const bucket = new FreeSpiritOss('static')

        const res = await bucket.upload(filePath)

        // 获取展示链接
        const displayUrl = bucket.getSingedDisplayUrl(res.url)
        if (!emojiOssMap.has(emojiType)) {
          emojiOssMap.set(emojiType, [])
        }

        const emojiTypeArr = emojiOssMap.get(emojiType) as string[]
        emojiTypeArr.push(displayUrl)
      }
    }

    console.log(emojiOssMap)
  })

  it('file walker', async () => {
    const fileMap = new Map<string, string>([['a-b-c', 'xx']])
    const mapF = (fileNode: FileNode) => {
      fileNode.originalName = fileMap.get(path.basename(fileNode.filepath)) || 'fk'
    }

    const walker = new FolderWalker(__dirname, { map: mapF })
    console.log(JSON.stringify(await walker.buildFileTree(), null, 4))
  }, 30000)

  it('文件测试', async () => {
    const a = '/Users/<USER>/Desktop/test/test.md'
    let dirname = path.dirname(a)
    while (dirname !== '.' && dirname !== '/') {
      console.log(dirname)
      dirname = path.dirname(dirname)
    }
  }, 30000)

  it('listPermission', async () => {
    console.log(await FileHelper.listPermission(__dirname))

    console.log(await FileHelper.listPermission(path.join(__dirname, 'i1231.ts')))
  }, 30000)

  it('mkdir', async () => {
    await FileHelper.makeFolder(path.join(__dirname, 'a'))
  }, 30000)

  it('', async () => {
    console.log(new Date('02/14/2023'))
  }, 30000)
})
