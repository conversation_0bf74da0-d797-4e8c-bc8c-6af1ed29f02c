type LongArchiveSuffixPattern = string | RegExp


/**
 * 前端定义的允许上传的压缩格式
 */
export const CompressedFormats = [
  '.zip',
  '.rar',
  '.tar',
  '.tar.gz',
  '.taz',
  '.tgz',
  '.tar.bz2',
  '.tb2',
  '.tbz',
  '.tbz2',
  '.tz2',
  '.7z',
]


/**
 * 定义所有压缩文件的后缀。
 * 分为简单和复杂两种匹配方式
 */
interface IArchiveSuffix {
  long?: LongArchiveSuffixPattern []
  short: string[]
}

class ArchiveSuffix implements IArchiveSuffix {
  long?: LongArchiveSuffixPattern []
  short: string[]

  public constructor(param: IArchiveSuffix) {
    this.long = param.long
    this.short = param.short
  }

  /**
   * 检测文件名是否符合此压缩文档的后缀规范
   */
  public detect(fullPath: string, shortExt: string): boolean {
    // 首先判断短后缀
    if (this.short.includes(shortExt.toLowerCase())) {
      return true
    }

    if (this.long != null) {
      // 接着判断长后缀，依次遍历每个判断器
      for (const matcher of this.long) {
        if (matcher instanceof RegExp) {
          // 如果提供一个正则表达式，使用正则表达式进行匹配
          if (matcher.test(fullPath)) {
            return true
          }
        } else {
          // 如果文件名以长后缀结尾，那么匹配成功
          if (fullPath.endsWith(matcher.toLowerCase())) {
            return true
          }
        }
      }
    }
    return false
  }
}

interface ISupportedArchiveTypes {
  ZIP: ArchiveSuffix
  RAR: ArchiveSuffix
  TAR: ArchiveSuffix
  TAR_GZIP: ArchiveSuffix
  TAR_BZIP2: ArchiveSuffix
  P7ZIP: ArchiveSuffix

  [key: string]: ArchiveSuffix
}

export const SupportedArchiveTypes: ISupportedArchiveTypes = {
  /*
   * ZIP 格式允许的文件名后缀：
   * *.zip，*.z01 ~ *.z99 (WinZIP 的分卷格式 ZIP)
   */
  ZIP: new ArchiveSuffix({
    long: [/\.z\d{2}$/i],
    short: ['zip'],
  },
  ),

  /*
   * RAR 格式允许的文件名后缀：
   * *.rar
   *
   * 包含分卷的后缀：
   * *.part001.rar ~ *.part999.rar
   * *.r01 ~ *.r99 (老式 WinRAR 的分卷后缀)
   */
  RAR: new ArchiveSuffix({
    long: [/\.r\d{2}$/i],
    short: ['rar'],
  }),

  TAR: new ArchiveSuffix({
    short: ['tar'],
  }),

  // 这里定义了 tar_gzip 和 tar_bzip2 的有效拓展名
  // 参考: https://en.wikipedia.org/wiki/Tar_(computing)#Suffixes_for_compressed_files
  TAR_GZIP: new ArchiveSuffix({
    long: ['.tar.gz'],
    short: ['taz', 'tgz'],
  }),

  TAR_BZIP2: new ArchiveSuffix({
    long: ['.tar.bz2'],
    short: ['tb2', 'tbz', 'tbz2', 'tz2'],
  }),

  P7ZIP: new ArchiveSuffix({
    short: ['7z'],
  }),
}
