import fs, { createReadStream, Stats } from 'fs'
import { posix as path } from 'path'
import mime from 'mime'
import { exec } from 'child_process'
import { SupportedArchiveTypes } from './types'
import { PathHelper } from '../path'
import axios from 'axios'

interface IMimeType {
  contentType: string
  fileType: string
}

export class FileHelper {
  /**
   * 获取网络文件大小， 获取不到返回 -1
   */
  public static async getFileSizeFromUrl(url: string): Promise<number> {
    const res = await axios.head(url)
    const head = res.headers

    if (!head['content-length']) {
      return -1
    }
    return parseInt(head['content-length'] as string, 10)
  }



  /**
   * 获取文件状态信息
   */
  public static async getFileStat(filepath: string): Promise<Stats> {
    return new Promise((resolve, reject) => {
      fs.stat(filepath, (err, stats) => {
        if (err) {
          reject(err)
        } else {
          resolve(stats)
        }
      })
    })
  }

  /**
   * 获取以 byte 为单位的文件大小
   * @param filePath
   * @param unit
   */
  public static getFileSize(filePath: string, unit: 'byte' | 'kb' | 'mb' | 'gb' = 'byte'): number {
    const stats = fs.statSync(filePath)
    let size = stats.size

    switch (unit) {
      case 'byte':
        break
      case 'kb':
        size = size / 1024
        break
      case 'mb':
        size = size / 1024 / 1024
        break
      case 'gb':
        size = size / 1024 / 1024 / 1024
        break
    }

    return size
  }

  /**
   * 向文件末尾添加内容
   * @param filePath
   * @param data
   */
  public static async appendFile(filePath: string, data: string) {
    return new Promise((resolve, reject) => {
      fs.appendFile(filePath, data, (err) => {
        if (err) {
          return reject(err)
        }
        resolve(true)
      })
    })
  }

  /**
   * 判断文件是否是压缩文件
   * @param fileName
   */
  public static isCompressed(fileName: string) {
    const fileExt = PathHelper.getFileExt(fileName)

    // 比较的方式：根据 SupportedArchiveTypes，比较所有受支持的类型是否有任意个为 True
    for (const key in SupportedArchiveTypes) {
      if (SupportedArchiveTypes.hasOwnProperty(key)) {
        const matcher = SupportedArchiveTypes[key]
        if (matcher.detect(fileName, fileExt)) return true
      }
    }

    return false
  }

  /**
   * 检查一个目录/文件是否存在，且是否有可读权限
   * @param filepath
   */
  public static async isExist(filepath: string): Promise<boolean> {
    return new Promise((resolve) => {
      fs.access(filepath, fs.constants.F_OK | fs.constants.R_OK, function (err) {
        if (err) {
          return resolve(false)
        }

        resolve(true)
      })
    })
  }

  public static async listPermission(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      exec(`ls -l ${filePath}`, (e, stdout, stderr) => {
        if (e) {
          reject(e)
        }

        resolve(stdout)
      })
    })
  }

  public static async whoAmI(): Promise<string> {
    return new Promise((resolve, reject) => {
      exec('whoami', (e, stdout, stderr) => {
        if (e) {
          reject(e)
        }

        resolve(stdout)
      })
    })
  }

  /**
   * 判断是否是文件
   * @param filepath
   * @return boolean
   */
  public static async isFile(filepath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      fs.lstat(filepath, function (err, stats) {
        if (err) {
          return reject(err)
        }

        resolve(stats.isFile())
      })
    })
  }

  /**
   * 判断是否是文件夹
   * @param filepath
   * @return boolean
   */
  public static async isFolder(filepath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      fs.lstat(filepath, function (err, stats) {
        if (err) {
          return reject(err)
        }

        resolve(stats.isDirectory())
      })
    })
  }

  /**
   * 递归异步创建文件夹
   * @param filepath
   * @return Promise<string>
   */
  public static async makeFolder(filepath: string): Promise<string> {
    await fs.promises.mkdir(filepath, { recursive: true })
    return filepath
  }

  /**
   * 同步创建目录
   * @param filepath
   */
  public static makeFolderSync(filepath: string): string {
    fs.mkdirSync(filepath, { recursive: true })

    return filepath
  }

  /**
   * 检查目录是否存在，存在的话递归删除
   * @param folderPath
   */
  public static async removeFolder(folderPath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const option = {
        recursive: true, // 递归删除
        maxRetries: 3,
        retryDelay: 3,
      }
      fs.rmdir(folderPath, option, function (err) {
        if (err) {
          return reject(err)
        }

        resolve(true)
      })
    })
  }

  /**
   * 删除文件
   * @param filePath
   */
  public static async removeFile(filePath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      fs.unlink(filePath, function (err) {
        if (err) {
          return reject(err)
        }

        resolve(true)
      })
    })
  }

  /**
   * 写文档
   * @param filepath
   * @param buffer
   */
  public static async writeFile(filepath: string, buffer: Buffer | Uint8Array | string): Promise<string> {
    return new Promise((resolve, reject) => {
      fs.writeFile(filepath, buffer, (err) => {
        if (err) {
          return reject(err)
        }
        resolve(filepath)
      })
    })
  }

  /**
   * 在搜索到的最后一个匹配字符串位置后添加内容
   * @param filePath
   * @param search
   * @param content
   */
  public static insertFileAfterString(filePath: string, search: string, content: string) {
    return new Promise<void>((resolve, reject) => {
      // 读取
      fs.readFile(filePath, (err, data) => {
        if (err) {
          return reject(err)
        }

        let file_content = data.toString()
        // 查找字符串位置
        const position = file_content.lastIndexOf(search) + search.length

        // 写入字符串 = 字符串之前 + 新加入内容 + 字符串之后
        file_content = file_content.substring(0, position) + content + file_content.substring(position)

        // 写入
        fs.writeFile(filePath, Buffer.from(file_content), (e) => {
          if (e) {
            return reject(e)
          }
          resolve()
        })
      })
    })
  }

  /**
   * 创建文件
   */
  public static async makeFile(filepath: string) {
    await fs.promises.writeFile(filepath, '')
  }

  /**
   * 读取文件转换为 String
   */
  public static async readFile(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      fs.readFile(filePath, function (err, data) {
        if (err) {
          return reject(err)
        }
        resolve(data.toString('utf8'))
      })
    })
  }

  /**
   * 读取文件转换为 String
   */
  public static async readFileAsBuffer(
    filePath: string,
    options?: { encoding?: null | undefined; flag?: string | undefined } | undefined | null,
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      fs.readFile(filePath, options, function (err, data) {
        if (err) {
          return reject(err)
        }

        if (typeof data === 'string') {
          data = Buffer.from(data, 'utf8')
        }
        resolve(data)
      })
    })
  }

  /**
   * 列出目录下所有文件，以绝对路径返回, 不会包含当前目录
   * @param folderPath
   */
  public static async listFiles(folderPath: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
      fs.readdir(folderPath, function (err, files) {
        if (err) {
          return reject(err)
        }

        const result = files.map((file) => {
          return path.join(folderPath, file)
        })
        resolve(result)
      })
    })
  }

  /**
   * 递归复制文件夹或文件
   * @param src
   * @param dest
   */
  public static async copy(src: string, dest: string) {
    await fs.promises.mkdir(dest, { recursive: true })
    const entries = await fs.promises.readdir(src, { withFileTypes: true })

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name)
      const destPath = path.join(dest, entry.name)

      entry.isDirectory() ? await FileHelper.copy(srcPath, destPath) : await fs.promises.copyFile(srcPath, destPath)
    }
  }

  /**
   * 生成一个与当前文件夹内文件不重名的文件名, 返回文件名
   * @param folderPath
   * @param filename
   */
  public static async generateUniqueFilename(folderPath: string, filename: string) {
    let uniqueId = 1
    filename = filename.toLowerCase()

    let uniqueName = filename
    const extension = path.extname(filename)
    const filenameOnly = path.basename(filename, extension)

    while (await FileHelper.isExist(path.join(folderPath, uniqueName))) {
      uniqueName = `${filenameOnly}_${uniqueId}${extension}`
      uniqueId += 1
    }
    return uniqueName
  }

  /**
   * 为文件分配可读写权限，保证可以正常使用
   * @param filename
   */
  public static async grantFilePermission(filename: string, mode = 0o755): Promise<void> {
    return new Promise(function (resolve, reject) {
      fs.chmod(filename, mode, function (err) {
        if (err != null) {
          reject(err)
        }
        resolve()
      })
    })
  }

  /**
   * 创建空文件
   * @param filepath
   */
  public static async touchFile(filepath: string) {
    return new Promise<void>((resolve, reject) => {
      fs.open(filepath, 'w', function (err, fd) {
        if (err != null) {
          reject(err)
        }
        fs.close(fd, function (error) {
          if (err != null) {
            reject(error)
          }
          resolve()
        })
      })
    })
  }

  /**
   * 使用 mime 对文件类型进行分类
   * @param filepath
   */
  public static getFileType(filepath: string): IMimeType {
    const mimeType = mime.getType(filepath)

    if (!mimeType) {
      return {
        contentType: 'application/octet-stream',
        fileType: '',
      }
    }

    const mimeSplit = mimeType.split('/')
    if (!mimeSplit.length) {
      return {
        contentType: 'application/octet-stream',
        fileType: '',
      }
    }

    return {
      contentType: mimeType,
      fileType: mimeSplit[0],
    }
  }

  public static getFolderName(url: string) {
    return path.basename(path.dirname(url))
  }


  // 给定目录，取出 n 个 sample 文件
  public static async sampleFiles(folder: string, number: number) {
    const files = await FileHelper.listFiles(folder)
    if (files.length <= number) {
      return files
    }

    // 随机选取 20 个
    const randomFiles: string[] = []

    while (randomFiles.length < number) {
      const randomIndex = Math.floor(Math.random() * files.length)
      const randomFile = files[randomIndex]
      if (!randomFiles.includes(randomFile)) {
        randomFiles.push(randomFile)
      }
    }

    return randomFiles
  }

  static async moveFiles(files: string[], newFolder: string) {
    // 并发移动文件
    const promises = files.map((file) => {
      return fs.promises.rename(file, path.join(newFolder, path.basename(file)))
    })


    return await Promise.all(promises)
  }

  static async readFileAsStream(filePath: string) {
    return createReadStream(filePath)
  }

  static async jsonDump(obj: any, filePath: string) {
    return await fs.promises.writeFile(filePath, JSON.stringify(obj, null, 2))
  }

  static async jsonLoad(filePath: string) {
    return JSON.parse(await this.readFile(filePath))
  }
}
