import { FileNode, FileNodeType } from './filenode'
import { FileHelper } from '../index'

interface IFolderWalkerOptions {
  map?: (fileNode: FileNode) => void // 允许对每个文件节点进行处理
  filter?: (fileNode: FileNode) => boolean // 允许对每个文件节点进行过滤
}

// 文件夹遍历转 文件树
export class FolderWalker {
  private readonly folderPath: string
  private readonly options: IFolderWalkerOptions

  constructor(folderPath: string, options?: IFolderWalkerOptions) {
    this.folderPath = folderPath
    this.options = options ?? {}
  }

  // 创建文件树
  public async buildFileTree(): Promise<FileNode> {
    const dfs = async (filePath: string) => {
      const fileType = await FileHelper.isFolder(filePath) ? FileNodeType.Folder : FileNodeType.File
      const fileNode = new FileNode(filePath, fileType)
      if (this.options.map) {
        this.options.map(fileNode)
      }

      if (this.options.filter && !this.options.filter(fileNode)) {
        return null
      }

      if (fileType === FileNodeType.File) {
        return fileNode
      }

      const files = await FileHelper.listFiles(filePath)
      for (const file of files) {
        const childFileNode = await dfs(file)
        if (childFileNode) {
          fileNode.addChild(childFileNode)
        }
      }

      return fileNode
    }

    const fileRoot = await dfs(this.folderPath)

    if (!fileRoot) {
      throw new Error('建树失败')
    }

    return fileRoot
  }

}
