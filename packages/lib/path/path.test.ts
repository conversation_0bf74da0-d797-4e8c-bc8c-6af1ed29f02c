import { PathHelper } from './index'

describe('Test', function () {
  it('should pass', async () => {
    const res = PathHelper.isHttpLink('wolai-secure-test://cover/2Muxbt6GFhJT1Ya7G76eP1/两个美女.jpg')
    console.log('wolai-secure-test://cover/2Muxbt6GFhJT1Ya7G76eP1/两个美女.jpg'.split('//')[0].split(':')[0])
    console.log(res)
    const res1 = PathHelper.isHttpLink('http://cover/2Muxbt6GFhJT1Ya7G76eP1/两个美女.jpg')
    console.log(res1)
  })


  it('isAbsolute', async () => {
    console.log(PathHelper.isAbsolute('image/a.png'))
  }, 30000)

  it('getFileNameFromUrl', async () => {
    console.log(PathHelper.getFileNameFromUrl('https://www.baidu.com/img/bd_logo1.png#a=b'))
  }, 30000)


  it('relative', async () => {
    console.log(PathHelper.relativePath('a/1.md', 'a/b/1.md'))
  }, 30000)

})
