import { z } from 'zod'
import { Config } from '../../../config'
import { AzureOpenAIClient, CheapOpenAI } from '../llm/client'
import { JSONHelper } from '../../json/json'
import { toJsonSchema } from 'openai-zod-functions'

describe('Test', function () {
  beforeAll(() => {
  })

  it('', async () => {
    const query = '老子博士后，计算机专业'
    const functionDefinition =  {
      name: 'analyzeUserPersonaTags',
      description: '对从聊天记录中提取客户画像标签进行处理',
      schema: z.object({
        educational_background: z.string().describe('客户当前的学历，尽量使用缩写，如大四，本科，保研，研0，研一，研二，在职，博士等。如果没有，则填空字符串'),
        goal: z.string().describe('客户的目标，使用简短的不超过5个字的描述，如找工作，发论文，保研，做项目等。如果没有，则填空字符串'),
        field: z.string().describe('客户的研究领域，尽量使用缩写，如CV,NLP,RL等。如果没有，则填空字符串'),
        major: z.string().describe('客户的专业方向，如机械、计算机、数学等。如果没有，则填空字符串'),
      }),
    }

    console.log(JSON.stringify(toJsonSchema(functionDefinition as any).parameters, null, 2))

    const jsonRes = await AzureOpenAIClient.getClient().predict(`<Instructions>
    You are tasked with converting a user query to JSON format. 
    
    <Input>
    <user_query>
    ${query}
    </user_query>
    </Input>
    
    Here's how you should approach this task:
    
    1. Read the user's query carefully. Focus on extracting information related to the fields described as follows:
    ${functionDefinition.description}
    ${JSON.stringify(toJsonSchema(functionDefinition as any).parameters, null, 2)}  
    
    2. Map the extracted information to the corresponding fields. Pay special attention to the data types and units (paying close attention to the allowed enum values).
    
    3. Construct the JSON. Start with an opening curly brace, include each field as a key with the extracted value as the value, and separate multiple fields with commas. Close with a closing curly brace. Put the JSON inside <JSON> tag.
    
    4. Ensure that the JSON is correctly formatted with proper keys, values, and data types as specified in the field descriptions.
  
    
    Follow these steps to convert the user query you receive into the appropriate JSON format. Think step by steps. Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.
    </Instructions>`)

    // regex 提取出 JSON 部分
    const jsonStr = JSONHelper.extractJSONPart(jsonRes)

    if (!jsonStr) {
      throw new Error('Failed to extract JSON part from GPT4 prompt')
    }

    console.log(jsonStr)
  }, 30000)

  it('azure test', async () => {
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_PROJECT = Config.setting.projectName
    const response = await AzureOpenAIClient.getClient({ model: 'gpt-5-chat', maxTokens: 2048, reasoningEffort: 'low' }).invoke('怎么做AI')
    console.log(response)
    const summary = response?.additional_kwargs?.reasoning
    console.log(summary)
    // if (response?.additional_kwargs?.reasoning?.summary as string[]) {
    //   for (const summary of response?.additional_kwargs?.reasoning?.summary) {
    //     console.log(summary)
    //   }
    // }
  }, 1E8)

  it('cheap test', async () => {
    const response = await CheapOpenAI.getClient({ model: 'gpt-5', maxTokens: 2048, reasoningEffort: 'low' }).invoke('怎么做AI')
    console.log(response)
  }, 1E8)
})