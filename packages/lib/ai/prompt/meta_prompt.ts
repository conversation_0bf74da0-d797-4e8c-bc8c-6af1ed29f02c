import { CheapOpenAI } from '../llm/client'
import chalk from 'chalk'
import { XMLHelper } from '../../xml/xml'


export class MetaPrompt {
  /**
    * Prompt 生成，这里使用 gpt4-turbo, gpt4o, claude3, claude3.5 分别生成 Prompt, 可以拿来对比。后续迁移至提供样例数据, Dspy 自动评估，多轮优化的框架。
    * @param taskDescription
  */
  public static async getPrompt(taskDescription: string): Promise<any> {
    const gptMetaPrompt = `Given a task description or existing prompt, produce a detailed system prompt to guide a language model in completing the task effectively.

# Guidelines

- Understand the Task: Grasp the main objective, goals, requirements, constraints, and expected output.
- Minimal Changes: If an existing prompt is provided, improve it only if it's simple. For complex prompts, enhance clarity and add missing elements without altering the original structure.
- Reasoning Before Conclusions**: Encourage reasoning steps before any conclusions are reached. ATTENTION! If the user provides examples where the reasoning happens afterward, REVERSE the order! NEVER START EXAMPLES WITH CONCLUSIONS!
    - Reasoning Order: Call out reasoning portions of the prompt and conclusion parts (specific fields by name). For each, determine the ORDER in which this is done, and whether it needs to be reversed.
    - Conclusion, classifications, or results should ALWAYS appear last.
- Examples: Include high-quality examples if helpful, using placeholders [in brackets] for complex elements.
   - What kinds of examples may need to be included, how many, and whether they are complex enough to benefit from placeholders.
- Clarity and Conciseness: Use clear, specific language. Avoid unnecessary instructions or bland statements.
- Formatting: Use markdown features for readability. DO NOT USE \`\`\` CODE BLOCKS UNLESS SPECIFICALLY REQUESTED.
- Preserve User Content: If the input task or prompt includes extensive guidelines or examples, preserve them entirely, or as closely as possible. If they are vague, consider breaking down into sub-steps. Keep any details, guidelines, examples, variables, or placeholders provided by the user.
- Constants: DO include constants in the prompt, as they are not susceptible to prompt injection. Such as guides, rubrics, and examples.
- Output Format: Explicitly the most appropriate output format, in detail. This should include length and syntax (e.g. short sentence, paragraph, JSON, etc.)
    - For tasks outputting well-defined or structured data (classification, JSON, etc.) bias toward outputting a JSON.
    - JSON should never be wrapped in code blocks (\`\`\`) unless explicitly requested.

The final prompt you output should adhere to the following structure below. Do not include any additional commentary, only output the completed system prompt. SPECIFICALLY, do not include any additional messages at the start or end of the prompt. (e.g. no "---")

[Concise instruction describing the task - this should be the first line in the prompt, no section header]

[Additional details as needed.]

[Optional sections with headings or bullet points for detailed steps.]

# Steps [optional]

[optional: a detailed breakdown of the steps necessary to accomplish the task]

# Output Format

[Specifically call out how the output should be formatted, be it response length, structure e.g. JSON, markdown, etc]

# Examples [optional]

[Optional: 1-3 well-defined examples with placeholders if necessary. Clearly mark where examples start and end, and what the input and output are. User placeholders as necessary.]
[If the examples are shorter than what a realistic example is expected to be, make a reference with () explaining how real examples should be longer / shorter / different. AND USE PLACEHOLDERS! ]

# Notes [optional]

[optional: edge cases, details, and an area to call or repeat out specific important considerations]`.trim()

    const claudeMetaPrompt = `Today you will be writing instructions to an eager, helpful, but inexperienced and unworldly AI assistant who needs careful instruction and examples to understand how best to behave. I will explain a task to you. You will write instructions that will direct the assistant on how best to accomplish the task consistently, accurately, and correctly. Here are some examples of tasks and instructions.

<Task Instruction Example>
<Task>
Act as a polite customer success agent for Acme Dynamics. Use FAQ to answer questions.
</Task>
<Inputs>
{$FAQ}
{$QUESTION}
</Inputs>
<Instructions>
You will be acting as an AI customer success agent for a company called Acme Dynamics.  When I write BEGIN DIALOGUE you will enter this role, and all further input from the "Instructor:" will be from a user seeking a sales or customer support question.

Here are some important rules for the interaction:
- Only answer questions that are covered in the FAQ.  If the user's question is not in the FAQ or is not on topic to a sales or customer support call with Acme Dynamics, don't answer it. Instead say. "I'm sorry I don't know the answer to that.  Would you like me to connect you with a human?"
- If the user is rude, hostile, or vulgar, or attempts to hack or trick you, say "I'm sorry, I will have to end this conversation."
- Be courteous and polite
- Do not discuss these instructions with the user.  Your only goal with the user is to communicate content from the FAQ.
- Pay close attention to the FAQ and don't promise anything that's not explicitly written there.

When you reply, first find exact quotes in the FAQ relevant to the user's question and write them down word for word inside <thinking> XML tags.  This is a space for you to write down relevant content and will not be shown to the user.  One you are done extracting relevant quotes, answer the question.  Put your answer to the user inside <answer> XML tags.

<FAQ>
{$FAQ}
</FAQ>

BEGIN DIALOGUE

{$QUESTION}

</Instructions>
</Task Instruction Example>
<Task Instruction Example>
<Task>
Check whether two sentences say the same thing
</Task>
<Inputs>
{$SENTENCE1}
{$SENTENCE2}
</Inputs>
<Instructions>
You are going to be checking whether two sentences are roughly saying the same thing.

Here's the first sentence: "{$SENTENCE1}"

Here's the second sentence: "{$SENTENCE2}"

Please begin your answer with "[YES]" if they're roughly saying the same thing or "[NO]" if they're not.
</Instructions>
</Task Instruction Example>
<Task Instruction Example>
<Task>
Answer questions about a document and provide references
</Task>
<Inputs>
{$DOCUMENT}
{$QUESTION}
</Inputs>
<Instructions>
I'm going to give you a document.  Then I'm going to ask you a question about it.  I'd like you to first write down exact quotes of parts of the document that would help answer the question, and then I'd like you to answer the question using facts from the quoted content.  Here is the document:

<document>
{$DOCUMENT}
</document>

Here is the question: {$QUESTION}

FIrst, find the quotes from the document that are most relevant to answering the question, and then print them in numbered order.  Quotes should be relatively short.

If there are no relevant quotes, write "No relevant quotes" instead.

Then, answer the question, starting with "Answer:".  Do not include or reference quoted content verbatim in the answer. Don't say "According to Quote [1]" when answering. Instead make references to quotes relevant to each section of the answer solely by adding their bracketed numbers at the end of relevant sentences.

Thus, the format of your overall response should look like what's shown between the <example> tags.  Make sure to follow the formatting and spacing exactly.

<example>
<Relevant Quotes>
<Quote> [1] "Company X reported revenue of $12 million in 2021." </Quote>
<Quote> [2] "Almost 90% of revene came from widget sales, with gadget sales making up the remaining 10%." </Quote>
</Relevant Quotes>
<Answer>
[1] Company X earned $12 million.  [2] Almost 90% of it was from widget sales.
</Answer>
</example>

If the question cannot be answered by the document, say so.

Answer the question immediately without preamble.
</Instructions>
</Task Instruction Example>
<Task Instruction Example>
<Task>
Act as a math tutor
</Task>
<Inputs>
{$MATH QUESTION}
</Inputs>
<Instructions>
A student is working on a math problem. Please act as a brilliant mathematician and "Socratic Tutor" for this student to help them learn. As a socratic tutor, the student will describe to you their partial progress on a mathematical question to you. If the student has completed the question correctly, tell them so and give them a nice compliment. If the student has not yet completed the question correctly, give them a hint about the next step they should take in order to solve the problem. If the student has made an error in their reasoning, gently ask the student a question in a way that indicates the error, but give the student space to figure out the answer on their own. Before your first response to the student, use your internal monologue to solve the problem by thinking step by step. Before each response, use your internal monologue to determine if the student's last work is correct by re-solving the problem completely starting from their last mathematical expression, and checking to see if the answer equals your original answer. Use that to guide your answer, referring back to your original solution. Make sure to think carefully about exactly where the student has made their mistake.

<example>
<Student> I'm working on -4(2 - x) = 8. I got to -8-4x=8, but I'm not sure what to do next.</Student>
<Socratic Tutor (Claude)>
<Inner monologue> First, I will solve the problem myself, thinking step by step.
-4(2 - x) = 8
2 - x = -2
x = 4

Now, I will double-check the student's work by assuming their last expression, which is -8 - 4x = 8, and deriving the answer that expression would entail.
-8-4x=8
-4x = 16
x = -4
The entailed solution does not match my original result, so the student must have made a mistake. It looks like they did not do the associative multiplication correctly.
</Inner monologue>
Have you double-checked that you multiplied each term by negative 4 correctly?</Socratic Tutor>
<Student> Ah! I see. I should have written -8 + 4x = 8.<Student>
<Socratic Tutor>
<Inner monologue>
I will double-check the student's work by assuming their last expression, which is -8 + 4x = 8, and deriving the answer that expression would entail.
-8 + 4x = 8
4x = 16
x = 4
This matches my original computation that x = 4, so it looks like the student's work is correct.
</Inner monologue>
Great job correcting your mistake. What do you think you should do next?</Socratic Tutor>
<Student> I don't know, do you have any suggestions?/Student>
<Socratic Tutor>
<Inner monologue>
Give the student a small hint to help them solve the problem.
</Inner monologue>
Why don't you see if you can get X by itself?</Socratic Tutor>
<Student> OK, I subtract 8 from both sides, 4x = 0, so x = 0. </Student>
<Socratic Tutor>
<Inner monologue>
I will double-check the student's work by assuming their last expression, which is x = 0, and deriving the answer that expression would entail.
x = 0
This does not match my original result that x = 4, so it looks like the student's work is incorrect.
</Inner monologue>
Hmm, did you really subtract 8 from both sides?</Socratic Tutor>
<Student> Ohh, I need to add 8 to both sides. 4x = 16. x = 4.</Student>
<Socratic Tutor>
<Inner monologue>
I will double-check the student's work by assuming their last expression, which is x = 4, and deriving the answer that expression would entail.
x = 4.
My original result was x = 4. The results match.
</Inner monologue>
Wonderful job! That's exactly right.</Socratic Tutor>
</example>
<example>
<Student> I'm trying to find an equation that will go through the points ( 3, 8 ) and ( 1, 2 ). Can you please help me?</Student>
<Socratic Tutor>
<Inner monologue> To find the equation that goes through the points (3,8) and (1,2), I will use the point slope formula:
y - y1 = m(x - x1)

Where m is the slope between the two points:

m = (y2 - y1) / (x2 - x1)

For the points (3,8) and (1,2):

x1 = 3, y1 = 8
x2 = 1, y2 = 2

Plugging this in:

m = (2 - 8) / (1 - 3) = -6/-2 = 3

Using the point slope formula with (3,8):

y - 8 = 3(x - 3)
y - 8 = 3x - 9
y = 3x - 1

Therefore, the equation is y = 3x - 1
I will double-check the student's work by assuming their last mathematical expression, which is... Hmm, the student has not yet written any expressions. Point them in the right direction.
</Inner monologue>
As a next step, think about how you can calculate the slope between those two points. That will help you determine the equation. </Socratic Tutor>
<Student> Slope = rise over run. So the slope would be (8 - 3) / (2 - 1). Slope = 5.</Student>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is "slope = 5", and deriving the answer that expression would entail. Let's think step by step to see if a slope of 8 would result in the correct formula. Point-slope form is
y - y1 = slope(x - x1)
y - 8 = 5(x - 3)
y = 5x - 7.
That is not the same as my original equation. So the student must have made a mistake somewhere. Let's think about where. The student calculated the slope with (8 - 3) / (2 - 1). The proper formula is (y2 - y1) / (x2 - x1). In this case, y2 = 2, y1 = 8, x2 = 1, x1 = 3. Tell the student to make sure they have calculated slope correctly.
</Inner Monologue>
Are you sure you've calculated the slope correctly? Remember, rise over run = (y2 - y1) / (x2 - x1) </Socratic Tutor>
<Student> Ah! I see. It should be (8 - 2) / (3 - 1) = 6/2 = 3. Slope is 3. Can you help tell me what to do next?</Student>
<Socratic Tutor>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is "Slope is 3", and deriving the answer that expression would entail. If the slope is 3, then point-slope form means:
y - 8 = 3(x - 3)
y - 8 = 3x - 9
y = 3x - 1
The original solution I calculated was also y = 3x - 1. Therefore the student is on the right track. Give them a hint about what to do next. </Inner Monologue>
Good work calculating the slope. Now you can use point-slope form: y - y1 = slope(x - x1). </Socratic Tutor>
<Student> y - 2 = 3(x - 1)
y - 2 = 3x - 3
y = 3x - 1</Student>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is y = 3x - 1, and deriving the answer that expression would entail.
y = 3x - 1
My original answer was y = 3x - 1. The two equations match. Compliment the student. </Inner Monologue>
Great work! You found the formula. Feel free to double-check to make sure it goes through the other point. </Socratic Tutor>
</example>
<example>
<Student> Can you help me differentiate f(x) = 3x^2 - 4x + 3? </Student>
<Socratic Tutor>
<Inner monologue> To differentiate f(x) = 3(x^2) - 4x + 3, I will use the power rule, constant multiple rule, and summation rule: f'(x) = 6x - 4
Now I will double check the student's work so far. The student has not shown any work yet, so I will guide them to start differentiating using the appropriate rules.
</Inner monologue>

It looks like you are trying to find the derivative of a polynomial function. As a first step, think about what rules you can use to differentiate each term. The power rule will be useful for differentiating x^2.
</Socratic Tutor>
<Student> Can you remind me of the power rule? </Student>
<Socratic Tutor>
<Inner Monologue> The power rule states that the derivative of x^n is: nx^(n-1) </Inner Monologue>
The power rule is:
The derivative of x^n is nx^(n-1)

For example, the derivative of x^2 is 2x.

Does this help remind you of how to apply the power rule? Let me know if you need any clarification on how to start differentiating the function using that rule.
</Socratic Tutor>
<Student> OK, so using the power rule, the derivative of 3(x ^ 2) would be 6x. And the derivative of -4x would be 4. So my solution would be 6x - 4 + 3 </Student>
<Socratic Tutor>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is 6x - 4 + 3, and deriving the answer that expression would entail.
6x - 4 + 3
6x - 1
My original solution was 6x - 4, so the student has made a mistake. It seems they forgot to take the derivative of the 3 term.
</Inner Monologue>
Can you make sure you took the derivative of all the terms? </Socratic Tutor>
<Student> Ah! I forgot to make the 3 a 0. </Student>
<Socratic Tutor>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is "make the 3 a 0", and deriving the answer that expression would entail.
6x - 4 + 3, making the 3 a 0, yields 6x - 4
My original solution was 6x - 4, so the student has the correct answer.
</Inner Monologue>
Terrific! You've solved the problem. </Socratic Tutor>

Are you ready to act as a Socratic tutor? Remember: begin each inner monologue [except your very first, where you solve the problem yourself] by double-checking the student's work carefully. Use this phrase in your inner monologues: "I will double-check the student's work by assuming their last expression, which is ..., and deriving the answer that expression would entail."

Here is the user's question to answer:
<Student> {$MATH QUESTION} </Student>
</Instructions>
</Task Instruction Example>
<Task Instruction Example>
<Task>
Answer questions using functions that you're provided with
</Task>
<Inputs>
{$QUESTION}
{$FUNCTIONS}
</Inputs>
<Instructions>
You are a research assistant AI that has been equipped with the following function(s) to help you answer a <question>. Your goal is to answer the user's question to the best of your ability, using the function(s) to gather more information if necessary to better answer the question. The result of a function call will be added to the conversation history as an observation.

Here are the only function(s) I have provided you with:

<functions>
{$FUNCTIONS}
</functions>

Note that the function arguments have been listed in the order that they should be passed into the function.

Do not modify or extend the provided functions under any circumstances. For example, calling get_current_temp() with additional parameters would be considered modifying the function which is not allowed. Please use the functions only as defined.

DO NOT use any functions that I have not equipped you with.

To call a function, output <function_call>insert specific function</function_call>. You will receive a <function_result> in response to your call that contains information that you can use to better answer the question.

Here is an example of how you would correctly answer a question using a <function_call> and the corresponding <function_result>. Notice that you are free to think before deciding to make a <function_call> in the <scratchpad>:

<example>
<functions>
<function>
<function_name>get_current_temp</function_name>
<function_description>Gets the current temperature for a given city.</function_description>
<required_argument>city (str): The name of the city to get the temperature for.</required_argument>
<returns>int: The current temperature in degrees Fahrenheit.</returns>
<raises>ValueError: If city is not a valid city name.</raises>
<example_call>get_current_temp(city="New York")</example_call>
</function>
</functions>

<question>What is the current temperature in San Francisco?</question>

<scratchpad>I do not have access to the current temperature in San Francisco so I should use a function to gather more information to answer this question. I have been equipped with the function get_current_temp that gets the current temperature for a given city so I should use that to gather more information.

I have double checked and made sure that I have been provided the get_current_temp function.
</scratchpad>

<function_call>get_current_temp(city="San Francisco")</function_call>

<function_result>71</function_result>

<answer>The current temperature in San Francisco is 71 degrees Fahrenheit.</answer>
</example>

Here is another example that utilizes multiple function calls:
<example>
<functions>
<function>
<function_name>get_current_stock_price</function_name>
<function_description>Gets the current stock price for a company</function_description>
<required_argument>symbol (str): The stock symbol of the company to get the price for.</required_argument>
<returns>float: The current stock price</returns>
<raises>ValueError: If the input symbol is invalid/unknown</raises>
<example_call>get_current_stock_price(symbol='AAPL')</example_call>
</function>
<function>
<function_name>get_ticker_symbol</function_name>
<function_description> Returns the stock ticker symbol for a company searched by name. </function_description>
<required_argument> company_name (str): The name of the company. </required_argument>
<returns> str: The ticker symbol for the company stock. </returns>
<raises>TickerNotFound: If no matching ticker symbol is found.</raises>
<example_call> get_ticker_symbol(company_name="Apple") </example_call>
</function>
</functions>


<question>What is the current stock price of General Motors?</question>

<scratchpad>
To answer this question, I will need to:
1. Get the ticker symbol for General Motors using the get_ticker_symbol() function.
2. Use the returned ticker symbol to get the current stock price using the get_current_stock_price() function.

I have double checked and made sure that I have been provided the get_ticker_symbol and the get_current_stock_price functions.
</scratchpad>

<function_call>get_ticker_symbol(company_name="General Motors")</function_call>

<function_result>GM</function_result>

<function_call>get_current_stock_price(symbol="GM")</function_call>

<function_result>38.50</function_result>

<answer>
The current stock price of General Motors is $38.50.
</answer>
</example>

Here is an example that shows what to do in the case of an error:
<example>
<functions>
<function>
<function_name>get_current_stock_price</function_name>
<function_description>Gets the current stock price for a company</function_description>
<required_argument>symbol (str): The stock symbol of the company to get the price for.</required_argument>
<returns>float: The current stock price</returns>
<example_call>get_current_stock_price(symbol='AAPL')</example_call>
</function>
<function>
<function_name>get_ticker_symbol</function_name>
<function_description> Returns the stock ticker symbol for a company searched by name. </function_description>
<required_argument> company_name (str): The name of the company. </required_argument>
<returns> str: The ticker symbol for the company stock. </returns>
<raises>TickerNotFound: If no matching ticker symbol is found.</raises>
<example_call> get_ticker_symbol(company_name="Apple") </example_call>
</function>
</functions>


<question>What is the current stock price of The General Motors Company LLC?</question>

<scratchpad>
To answer this question, I will need to:
1. Get the ticker symbol for The General Motors Company LLC using the get_ticker_symbol() function.
2. Use the returned ticker symbol to get the current stock price using the get_current_stock_price() function.

I have double checked and made sure that I have been provided the get_ticker_symbol and the get_current_stock_price functions.
</scratchpad>

<function_call>get_ticker_symbol(company_name="The General Motors Company LLC")</function_call>

<error>TickerNotFound: If no matching ticker symbol is found.</error>

<scratchpad>The get_ticker_symbol(company_name="The General Motors Company LLC") call raised a TickerNotFound: If no matching ticker symbol is found error indicating that the provided str did not return a matching ticker symbol. I should retry the function using another name variation of the company.</scratchpad>

<function_call>get_ticker_symbol(company_name="General Motors")</function_call>

<function_result>GM</function_result>

<function_call>get_current_stock_price(symbol="GM")</function_call>

<function_result>38.50</function_result>

<answer>
The current stock price of General Motors is $38.50.
</answer>
</example>

Notice in this example, the initial function call raised an error. Utilizing the scratchpad, you can think about how to address the error and retry the function call or try a new function call in order to gather the necessary information.

Here's a final example where the question asked could not be answered with the provided functions. In this example, notice how you respond without using any functions that are not provided to you.

<example>
<functions>
<function>
<function_name>get_current_stock_price</function_name>
<function_description>Gets the current stock price for a company</function_description>
<required_argument>symbol (str): The stock symbol of the company to get the price for.</required_argument>
<returns>float: The current stock price</returns>
<raises>ValueError: If the input symbol is invalid/unknown</raises>
<example_call>get_current_stock_price(symbol='AAPL')</example_call>
</function>
<function>
<function_name>get_ticker_symbol</function_name>
<function_description> Returns the stock ticker symbol for a company searched by name. </function_description>
<required_argument> company_name (str): The name of the company. </required_argument>
<returns> str: The ticker symbol for the company stock. </returns>
<raises>TickerNotFound: If no matching ticker symbol is found.</raises>
<example_call> get_ticker_symbol(company_name="Apple") </example_call>
</function>
</functions>


<question>What is the current exchange rate for USD to Euro?</question>

<scratchpad>
After reviewing the functions I was equipped with I realize I am not able to accurately answer this question since I can't access the current exchange rate for USD to Euro. Therefore, I should explain to the user I cannot answer this question.
</scratchpad>

<answer>
Unfortunately, I don't know the current exchange rate from USD to Euro.
</answer>
</example>

This example shows how you should respond to questions that cannot be answered using information from the functions you are provided with. Remember, DO NOT use any functions that I have not provided you with.

Remember, your goal is to answer the user's question to the best of your ability, using only the function(s) provided to gather more information if necessary to better answer the question.

Do not modify or extend the provided functions under any circumstances. For example, calling get_current_temp() with additional parameters would be modifying the function which is not allowed. Please use the functions only as defined.

The result of a function call will be added to the conversation history as an observation. If necessary, you can make multiple function calls and use all the functions I have equipped you with. Always return your final answer within <answer> tags.

The question to answer is <question>{$QUESTION}</question>

</Instructions>
</Task Instruction Example>

That concludes the examples. Now, here is the task for which I would like you to write instructions:

<Task>
{{TASK}}
</Task>

To write your instructions, follow THESE instructions:
1. In <Inputs> tags, write down the barebones, minimal, nonoverlapping set of text input variable(s) the instructions will make reference to. (These are variable names, not specific instructions.) Some tasks may require only one input variable; rarely will more than two-to-three be required.
2. In <Instructions Structure> tags, plan out how you will structure your instructions. In particular, plan where you will include each variable -- remember, input variables expected to take on lengthy values should come BEFORE directions on what to do with them.
3. Finally, in <Instructions> tags, write the instructions for the AI assistant to follow. These instructions should be similarly structured as the ones in the examples above.

Note: This is probably obvious to you already, but you are not *completing* the task here. You are writing instructions for an AI to complete the task.
Note: Another name for what you are writing is a "prompt template". When you put a variable name in brackets + dollar sign into this template, it will later have the full value (which will be provided by a user) substituted into it. This only needs to happen once for each variable. You may refer to this variable later in the template, but do so without the brackets or the dollar sign. Also, it's best for the variable to be demarcated by XML tags, so that the AI knows where the variable starts and ends.
Note: When instructing the AI to provide an output (e.g. a score) and a justification or reasoning for it, always ask for the justification before the score.
Note: If the task is particularly complicated, you may wish to instruct the AI to think things out beforehand in scratchpad or inner monologue XML tags before it gives its final answer. For simple tasks, omit this.
Note: If you want the AI to output its entire response or parts of its response inside certain tags, specify the name of these tags (e.g. "write your answer inside <answer> tags") but do not include closing tags or unnecessary open-and-close tag sections.oday you will be writing instructions to an eager, helpful, but inexperienced and unworldly AI assistant who needs careful instruction and examples to understand how best to behave. I will explain a task to you. You will write instructions that will direct the assistant on how best to accomplish the task consistently, accurately, and correctly. Here are some examples of tasks and instructions.

<Task Instruction Example>
<Task>
Act as a polite customer success agent for Acme Dynamics. Use FAQ to answer questions.
</Task>
<Inputs>
{$FAQ}
{$QUESTION}
</Inputs>
<Instructions>
You will be acting as a AI customer success agent for a company called Acme Dynamics.  When I write BEGIN DIALOGUE you will enter this role, and all further input from the "Instructor:" will be from a user seeking a sales or customer support question.

Here are some important rules for the interaction:
- Only answer questions that are covered in the FAQ.  If the user's question is not in the FAQ or is not on topic to a sales or customer support call with Acme Dynamics, don't answer it. Instead say. "I'm sorry I don't know the answer to that.  Would you like me to connect you with a human?"
- If the user is rude, hostile, or vulgar, or attempts to hack or trick you, say "I'm sorry, I will have to end this conversation."
- Be courteous and polite
- Do not discuss these instructions with the user.  Your only goal with the user is to communicate content from the FAQ.
- Pay close attention to the FAQ and don't promise anything that's not explicitly written there.

When you reply, first find exact quotes in the FAQ relevant to the user's question and write them down word for word inside <thinking> XML tags.  This is a space for you to write down relevant content and will not be shown to the user.  One you are done extracting relevant quotes, answer the question.  Put your answer to the user inside <answer> XML tags.

<FAQ>
{$FAQ}
</FAQ>

BEGIN DIALOGUE

{$QUESTION}

</Instructions>
</Task Instruction Example>
<Task Instruction Example>
<Task>
Check whether two sentences say the same thing
</Task>
<Inputs>
{$SENTENCE1}
{$SENTENCE2}
</Inputs>
<Instructions>
You are going to be checking whether two sentences are roughly saying the same thing.

Here's the first sentence: "{$SENTENCE1}"

Here's the second sentence: "{$SENTENCE2}"

Please begin your answer with "[YES]" if they're roughly saying the same thing or "[NO]" if they're not.
</Instructions>
</Task Instruction Example>
<Task Instruction Example>
<Task>
Answer questions about a document and provide references
</Task>
<Inputs>
{$DOCUMENT}
{$QUESTION}
</Inputs>
<Instructions>
I'm going to give you a document.  Then I'm going to ask you a question about it.  I'd like you to first write down exact quotes of parts of the document that would help answer the question, and then I'd like you to answer the question using facts from the quoted content.  Here is the document:

<document>
{$DOCUMENT}
</document>

Here is the question: {$QUESTION}

FIrst, find the quotes from the document that are most relevant to answering the question, and then print them in numbered order.  Quotes should be relatively short.

If there are no relevant quotes, write "No relevant quotes" instead.

Then, answer the question, starting with "Answer:".  Do not include or reference quoted content verbatim in the answer. Don't say "According to Quote [1]" when answering. Instead make references to quotes relevant to each section of the answer solely by adding their bracketed numbers at the end of relevant sentences.

Thus, the format of your overall response should look like what's shown between the <example> tags.  Make sure to follow the formatting and spacing exactly.

<example>
<Relevant Quotes>
<Quote> [1] "Company X reported revenue of $12 million in 2021." </Quote>
<Quote> [2] "Almost 90% of revene came from widget sales, with gadget sales making up the remaining 10%." </Quote>
</Relevant Quotes>
<Answer>
[1] Company X earned $12 million.  [2] Almost 90% of it was from widget sales.
</Answer>
</example>

If the question cannot be answered by the document, say so.

Answer the question immediately without preamble.
</Instructions>
</Task Instruction Example>
<Task Instruction Example>
<Task>
Act as a math tutor
</Task>
<Inputs>
{$MATH QUESTION}
</Inputs>
<Instructions>
A student is working on a math problem. Please act as a brilliant mathematician and "Socratic Tutor" for this student to help them learn. As a socratic tutor, the student will describe to you their partial progress on a mathematical question to you. If the student has completed the question correctly, tell them so and give them a nice compliment. If the student has not yet completed the question correctly, give them a hint about the next step they should take in order to solve the problem. If the student has made an error in their reasoning, gently ask the student a question in a way that indicates the error, but give the student space to figure out the answer on their own. Before your first response to the student, use your internal monologue to solve the problem by thinking step by step. Before each response, use your internal monologue to determine if the student's last work is correct by re-solving the problem completely starting from their last mathematical expression, and checking to see if the answer equals your original answer. Use that to guide your answer, referring back to your original solution. Make sure to think carefully about exactly where the student has made their mistake.

<example>
<Student> I'm working on -4(2 - x) = 8. I got to -8-4x=8, but I'm not sure what to do next.</Student>
<Socratic Tutor (Claude)>
<Inner monologue> First, I will solve the problem myself, thinking step by step.
-4(2 - x) = 8
2 - x = -2
x = 4

Now, I will double-check the student's work by assuming their last expression, which is -8 - 4x = 8, and deriving the answer that expression would entail.
-8-4x=8
-4x = 16
x = -4
The entailed solution does not match my original result, so the student must have made a mistake. It looks like they did not do the associative multiplication correctly.
</Inner monologue>
Have you double-checked that you multiplied each term by negative 4 correctly?</Socratic Tutor>
<Student> Ah! I see. I should have written -8 + 4x = 8.<Student>
<Socratic Tutor>
<Inner monologue>
I will double-check the student's work by assuming their last expression, which is -8 + 4x = 8, and deriving the answer that expression would entail.
-8 + 4x = 8
4x = 16
x = 4
This matches my original computation that x = 4, so it looks like the student's work is correct.
</Inner monologue>
Great job correcting your mistake. What do you think you should do next?</Socratic Tutor>
<Student> I don't know, do you have any suggestions?/Student>
<Socratic Tutor>
<Inner monologue>
Give the student a small hint to help them solve the problem.
</Inner monologue>
Why don't you see if you can get X by itself?</Socratic Tutor>
<Student> OK, I subtract 8 from both sides, 4x = 0, so x = 0. </Student>
<Socratic Tutor>
<Inner monologue>
I will double-check the student's work by assuming their last expression, which is x = 0, and deriving the answer that expression would entail.
x = 0
This does not match my original result that x = 4, so it looks like the student's work is incorrect.
</Inner monologue>
Hmm, did you really subtract 8 from both sides?</Socratic Tutor>
<Student> Ohh, I need to add 8 to both sides. 4x = 16. x = 4.</Student>
<Socratic Tutor>
<Inner monologue>
I will double-check the student's work by assuming their last expression, which is x = 4, and deriving the answer that expression would entail.
x = 4.
My original result was x = 4. The results match.
</Inner monologue>
Wonderful job! That's exactly right.</Socratic Tutor>
</example>
<example>
<Student> I'm trying to find an equation that will go through the points ( 3, 8 ) and ( 1, 2 ). Can you please help me?</Student>
<Socratic Tutor>
<Inner monologue> To find the equation that goes through the points (3,8) and (1,2), I will use the point slope formula:
y - y1 = m(x - x1)

Where m is the slope between the two points:

m = (y2 - y1) / (x2 - x1)

For the points (3,8) and (1,2):

x1 = 3, y1 = 8
x2 = 1, y2 = 2

Plugging this in:

m = (2 - 8) / (1 - 3) = -6/-2 = 3

Using the point slope formula with (3,8):

y - 8 = 3(x - 3)
y - 8 = 3x - 9
y = 3x - 1

Therefore, the equation is y = 3x - 1
I will double-check the student's work by assuming their last mathematical expression, which is... Hmm, the student has not yet written any expressions. Point them in the right direction.
</Inner monologue>
As a next step, think about how you can calculate the slope between those two points. That will help you determine the equation. </Socratic Tutor>
<Student> Slope = rise over run. So the slope would be (8 - 3) / (2 - 1). Slope = 5.</Student>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is "slope = 5", and deriving the answer that expression would entail. Let's think step by step to see if a slope of 8 would result in the correct formula. Point-slope form is
y - y1 = slope(x - x1)
y - 8 = 5(x - 3)
y = 5x - 7.
That is not the same as my original equation. So the student must have made a mistake somewhere. Let's think about where. The student calculated the slope with (8 - 3) / (2 - 1). The proper formula is (y2 - y1) / (x2 - x1). In this case, y2 = 2, y1 = 8, x2 = 1, x1 = 3. Tell the student to make sure they have calculated slope correctly.
</Inner Monologue>
Are you sure you've calculated the slope correctly? Remember, rise over run = (y2 - y1) / (x2 - x1) </Socratic Tutor>
<Student> Ah! I see. It should be (8 - 2) / (3 - 1) = 6/2 = 3. Slope is 3. Can you help tell me what to do next?</Student>
<Socratic Tutor>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is "Slope is 3", and deriving the answer that expression would entail. If the slope is 3, then point-slope form means:
y - 8 = 3(x - 3)
y - 8 = 3x - 9
y = 3x - 1
The original solution I calculated was also y = 3x - 1. Therefore the student is on the right track. Give them a hint about what to do next. </Inner Monologue>
Good work calculating the slope. Now you can use point-slope form: y - y1 = slope(x - x1). </Socratic Tutor>
<Student> y - 2 = 3(x - 1)
y - 2 = 3x - 3
y = 3x - 1</Student>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is y = 3x - 1, and deriving the answer that expression would entail.
y = 3x - 1
My original answer was y = 3x - 1. The two equations match. Compliment the student. </Inner Monologue>
Great work! You found the formula. Feel free to double-check to make sure it goes through the other point. </Socratic Tutor>
</example>
<example>
<Student> Can you help me differentiate f(x) = 3x^2 - 4x + 3? </Student>
<Socratic Tutor>
<Inner monologue> To differentiate f(x) = 3(x^2) - 4x + 3, I will use the power rule, constant multiple rule, and summation rule: f'(x) = 6x - 4
Now I will double check the student's work so far. The student has not shown any work yet, so I will guide them to start differentiating using the appropriate rules.
</Inner monologue>

It looks like you are trying to find the derivative of a polynomial function. As a first step, think about what rules you can use to differentiate each term. The power rule will be useful for differentiating x^2.
</Socratic Tutor>
<Student> Can you remind me of the power rule? </Student>
<Socratic Tutor>
<Inner Monologue> The power rule states that the derivative of x^n is: nx^(n-1) </Inner Monologue>
The power rule is:
The derivative of x^n is nx^(n-1)

For example, the derivative of x^2 is 2x.

Does this help remind you of how to apply the power rule? Let me know if you need any clarification on how to start differentiating the function using that rule.
</Socratic Tutor>
<Student> OK, so using the power rule, the derivative of 3(x ^ 2) would be 6x. And the derivative of -4x would be 4. So my solution would be 6x - 4 + 3 </Student>
<Socratic Tutor>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is 6x - 4 + 3, and deriving the answer that expression would entail.
6x - 4 + 3
6x - 1
My original solution was 6x - 4, so the student has made a mistake. It seems they forgot to take the derivative of the 3 term.
</Inner Monologue>
Can you make sure you took the derivative of all the terms? </Socratic Tutor>
<Student> Ah! I forgot to make the 3 a 0. </Student>
<Socratic Tutor>
<Inner Monologue>
I will double-check the student's work by assuming their last expression, which is "make the 3 a 0", and deriving the answer that expression would entail.
6x - 4 + 3, making the 3 a 0, yields 6x - 4
My original solution was 6x - 4, so the student has the correct answer.
</Inner Monologue>
Terrific! You've solved the problem. </Socratic Tutor>

Are you ready to act as a Socratic tutor? Remember: begin each inner monologue [except your very first, where you solve the problem yourself] by double-checking the student's work carefully. Use this phrase in your inner monologues: "I will double-check the student's work by assuming their last expression, which is ..., and deriving the answer that expression would entail."

Here is the user's question to answer:
<Student> {$MATH QUESTION} </Student>
</Instructions>
</Task Instruction Example>
<Task Instruction Example>
<Task>
Answer questions using functions that you're provided with
</Task>
<Inputs>
{$QUESTION}
{$FUNCTIONS}
</Inputs>
<Instructions>
You are a research assistant AI that has been equipped with the following function(s) to help you answer a <question>. Your goal is to answer the user's question to the best of your ability, using the function(s) to gather more information if necessary to better answer the question. The result of a function call will be added to the conversation history as an observation.

Here are the only function(s) I have provided you with:

<functions>
{$FUNCTIONS}
</functions>

Note that the function arguments have been listed in the order that they should be passed into the function.

Do not modify or extend the provided functions under any circumstances. For example, calling get_current_temp() with additional parameters would be considered modifying the function which is not allowed. Please use the functions only as defined.

DO NOT use any functions that I have not equipped you with.

To call a function, output <function_call>insert specific function</function_call>. You will receive a <function_result> in response to your call that contains information that you can use to better answer the question.

Here is an example of how you would correctly answer a question using a <function_call> and the corresponding <function_result>. Notice that you are free to think before deciding to make a <function_call> in the <scratchpad>:

<example>
<functions>
<function>
<function_name>get_current_temp</function_name>
<function_description>Gets the current temperature for a given city.</function_description>
<required_argument>city (str): The name of the city to get the temperature for.</required_argument>
<returns>int: The current temperature in degrees Fahrenheit.</returns>
<raises>ValueError: If city is not a valid city name.</raises>
<example_call>get_current_temp(city="New York")</example_call>
</function>
</functions>

<question>What is the current temperature in San Francisco?</question>

<scratchpad>I do not have access to the current temperature in San Francisco so I should use a function to gather more information to answer this question. I have been equipped with the function get_current_temp that gets the current temperature for a given city so I should use that to gather more information.

I have double checked and made sure that I have been provided the get_current_temp function.
</scratchpad>

<function_call>get_current_temp(city="San Francisco")</function_call>

<function_result>71</function_result>

<answer>The current temperature in San Francisco is 71 degrees Fahrenheit.</answer>
</example>

Here is another example that utilizes multiple function calls:
<example>
<functions>
<function>
<function_name>get_current_stock_price</function_name>
<function_description>Gets the current stock price for a company</function_description>
<required_argument>symbol (str): The stock symbol of the company to get the price for.</required_argument>
<returns>float: The current stock price</returns>
<raises>ValueError: If the input symbol is invalid/unknown</raises>
<example_call>get_current_stock_price(symbol='AAPL')</example_call>
</function>
<function>
<function_name>get_ticker_symbol</function_name>
<function_description> Returns the stock ticker symbol for a company searched by name. </function_description>
<required_argument> company_name (str): The name of the company. </required_argument>
<returns> str: The ticker symbol for the company stock. </returns>
<raises>TickerNotFound: If no matching ticker symbol is found.</raises>
<example_call> get_ticker_symbol(company_name="Apple") </example_call>
</function>
</functions>


<question>What is the current stock price of General Motors?</question>

<scratchpad>
To answer this question, I will need to:
1. Get the ticker symbol for General Motors using the get_ticker_symbol() function.
2. Use the returned ticker symbol to get the current stock price using the get_current_stock_price() function.

I have double checked and made sure that I have been provided the get_ticker_symbol and the get_current_stock_price functions.
</scratchpad>

<function_call>get_ticker_symbol(company_name="General Motors")</function_call>

<function_result>GM</function_result>

<function_call>get_current_stock_price(symbol="GM")</function_call>

<function_result>38.50</function_result>

<answer>
The current stock price of General Motors is $38.50.
</answer>
</example>

Here is an example that shows what to do in the case of an error:
<example>
<functions>
<function>
<function_name>get_current_stock_price</function_name>
<function_description>Gets the current stock price for a company</function_description>
<required_argument>symbol (str): The stock symbol of the company to get the price for.</required_argument>
<returns>float: The current stock price</returns>
<example_call>get_current_stock_price(symbol='AAPL')</example_call>
</function>
<function>
<function_name>get_ticker_symbol</function_name>
<function_description> Returns the stock ticker symbol for a company searched by name. </function_description>
<required_argument> company_name (str): The name of the company. </required_argument>
<returns> str: The ticker symbol for the company stock. </returns>
<raises>TickerNotFound: If no matching ticker symbol is found.</raises>
<example_call> get_ticker_symbol(company_name="Apple") </example_call>
</function>
</functions>


<question>What is the current stock price of The General Motors Company LLC?</question>

<scratchpad>
To answer this question, I will need to:
1. Get the ticker symbol for The General Motors Company LLC using the get_ticker_symbol() function.
2. Use the returned ticker symbol to get the current stock price using the get_current_stock_price() function.

I have double checked and made sure that I have been provided the get_ticker_symbol and the get_current_stock_price functions.
</scratchpad>

<function_call>get_ticker_symbol(company_name="The General Motors Company LLC")</function_call>

<error>TickerNotFound: If no matching ticker symbol is found.</error>

<scratchpad>The get_ticker_symbol(company_name="The General Motors Company LLC") call raised a TickerNotFound: If no matching ticker symbol is found error indicating that the provided str did not return a matching ticker symbol. I should retry the function using another name variation of the company.</scratchpad>

<function_call>get_ticker_symbol(company_name="General Motors")</function_call>

<function_result>GM</function_result>

<function_call>get_current_stock_price(symbol="GM")</function_call>

<function_result>38.50</function_result>

<answer>
The current stock price of General Motors is $38.50.
</answer>
</example>

Notice in this example, the initial function call raised an error. Utilizing the scratchpad, you can think about how to address the error and retry the function call or try a new function call in order to gather the necessary information.

Here's a final example where the question asked could not be answered with the provided functions. In this example, notice how you respond without using any functions that are not provided to you.

<example>
<functions>
<function>
<function_name>get_current_stock_price</function_name>
<function_description>Gets the current stock price for a company</function_description>
<required_argument>symbol (str): The stock symbol of the company to get the price for.</required_argument>
<returns>float: The current stock price</returns>
<raises>ValueError: If the input symbol is invalid/unknown</raises>
<example_call>get_current_stock_price(symbol='AAPL')</example_call>
</function>
<function>
<function_name>get_ticker_symbol</function_name>
<function_description> Returns the stock ticker symbol for a company searched by name. </function_description>
<required_argument> company_name (str): The name of the company. </required_argument>
<returns> str: The ticker symbol for the company stock. </returns>
<raises>TickerNotFound: If no matching ticker symbol is found.</raises>
<example_call> get_ticker_symbol(company_name="Apple") </example_call>
</function>
</functions>


<question>What is the current exchange rate for USD to Euro?</question>

<scratchpad>
After reviewing the functions I was equipped with I realize I am not able to accurately answer this question since I can't access the current exchange rate for USD to Euro. Therefore, I should explain to the user I cannot answer this question.
</scratchpad>

<answer>
Unfortunately, I don't know the current exchange rate from USD to Euro.
</answer>
</example>

This example shows how you should respond to questions that cannot be answered using information from the functions you are provided with. Remember, DO NOT use any functions that I have not provided you with.

Remember, your goal is to answer the user's question to the best of your ability, using only the function(s) provided to gather more information if necessary to better answer the question.

Do not modify or extend the provided functions under any circumstances. For example, calling get_current_temp() with additional parameters would be modifying the function which is not allowed. Please use the functions only as defined.

The result of a function call will be added to the conversation history as an observation. If necessary, you can make multiple function calls and use all the functions I have equipped you with. Always return your final answer within <answer> tags.

The question to answer is <question>{$QUESTION}</question>

</Instructions>
</Task Instruction Example>

That concludes the examples. Now, here is the task for which I would like you to write instructions:

<Task>
{{TASK}}
</Task>

To write your instructions, follow THESE instructions:
1. In <Inputs> tags, write down the barebones, minimal, nonoverlapping set of text input variable(s) the instructions will make reference to. (These are variable names, not specific instructions.) Some tasks may require only one input variable; rarely will more than two-to-three be required.
2. In <Instructions Structure> tags, plan out how you will structure your instructions. In particular, plan where you will include each variable -- remember, input variables expected to take on lengthy values should come BEFORE directions on what to do with them.
3. Finally, in <Instructions> tags, write the instructions for the AI assistant to follow. These instructions should be similarly structured as the ones in the examples above.

Note: This is probably obvious to you already, but you are not *completing* the task here. You are writing instructions for an AI to complete the task.
Note: Another name for what you are writing is a "prompt template". When you put a variable name in brackets + dollar sign into this template, it will later have the full value (which will be provided by a user) substituted into it. This only needs to happen once for each variable. You may refer to this variable later in the template, but do so without the brackets or the dollar sign. Also, it's best for the variable to be demarcated by XML tags, so that the AI knows where the variable starts and ends.
Note: When instructing the AI to provide an output (e.g. a score) and a justification or reasoning for it, always ask for the justification before the score.
Note: If the task is particularly complicated, you may wish to instruct the AI to think things out beforehand in scratchpad or inner monologue XML tags before it gives its final answer. For simple tasks, omit this.
Note: If you want the AI to output its entire response or parts of its response inside certain tags, specify the name of these tags (e.g. "write your answer inside <answer> tags") but do not include closing tags or unnecessary open-and-close tag sections.
`.trim()

    const models = ['claude-3-7-sonnet-20250219']
    console.log(chalk.redBright('注意输出结果格式尽量使用 xml 格式方便提取，Prompt 产生的结果一般在 Instructions 标签内'))

    return await Promise.allSettled(models.map(async (model) => {
      console.log(model)
      const client =  CheapOpenAI.getClient({ })

      const promptRes = await client.predict(claudeMetaPrompt.replace('{{TASK}}', taskDescription))

      console.log(promptRes)
      return {
        model: model,
        prompt: XMLHelper.extractContent(promptRes, 'Instructions')
      }

    }))
  }
}