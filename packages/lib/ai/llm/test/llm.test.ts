import { ChatPromptTemplate, PromptTemplate, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../llm_model'
import { HumanMessage } from '@langchain/core/messages'
import { Client } from 'langsmith'

describe('Test', function () {
  beforeAll(() => {

  })

  it('LLM 返回类型识别', async () => {
    // ✅ 自动推断为 Promise<Record<string, unknown> | null>
    const data = await LLM.predict('请严格输出 JSON', { responseJSON: true, })

    const text = await LLM.predict('普通文本', { responseJSON: false })
    const fuck = await LLM.predict('普通文本')

    const llm = new LLM()
    console.log(await llm.predict('普通文本'))

    const llm1 = new LLM({ responseJSON: true })
    console.log(await llm1.predict('fk json'))

    console.log(data, typeof data)
    console.log(text, typeof text)
    console.log(fuck, typeof fuck)
  }, 30000)

  it('PromptTemplate 示例', async () => {
    const template = SystemMessagePromptTemplate.fromTemplate('请用 {{tool}} 解答以下问题：{{question}}', { templateFormat: 'mustache' })

    console.log(await LLM.predict(template, undefined, { tool: 'ChatGPT', question: '你好' }))

    // console.log(await LLM.predict('你好'))
  }, 60000)

  it('ChatPromptTemplate 示例', async () => {
    const promptTemplate = ChatPromptTemplate.fromMessages<{chat_history: string}>([
      ['system', 'hello'],
      ['user', '{chat_history}'],
      // ['ai', 'good']
    ])

    console.log(await new LLM().predictMessage(promptTemplate, { chat_history: 'hi' }))

    console.log(await new LLM().predictMessage([new HumanMessage('hi')]))
  }, 60000)

  it('添加 Prompt 模版', async () => {
    const prompt = PromptTemplate.fromTemplate('hi {name}, good {event}')

    const client = new Client()
    await client.pushPrompt('test_prompt', {
      object: prompt
    })
  }, 60000)

  it ('拉取数据，并新建 DataSet', async () => {
    // 参数配置
    const params = {
      metadataKey: 'promptName', // 要查询的元数据键
      metadataValue: 'TestPrompt', // 要查询的元数据值
      datasetName: `TestPrompt Dataset_${new Date ().toLocaleString()}`, // 数据集名称
      description: '用于测试的 PromptTemplate 数据集'
    }

    // 从 LangSmith 拉取数据，以 PromptTemplate 的 参数作为输入，模型的输出作为输出。构建数据集

    const client = new Client ()

    // 创建数据集来存储示例
    const datasetName = params.datasetName
    const dataset = await client.createDataset (datasetName, { description: params.description })

    // 用于按 trace_id 分组运行的对象
    const runsByTraceId: Record<string, {
        input?: any
        output?: any
        metadata?: any
      }> = {}

    // 查询匹配过滤条件的运行
    const runs = client.listRuns ({
      projectName: 'moer',
      filter: `and (eq (metadata_key, '${params.metadataKey}'), eq (metadata_value, '${params.metadataValue}'))`,
      error: false
    })

    // 第一遍：按 trace_id 分组运行
    for await (const run of runs) {
      const traceId = run.trace_id
      if (!traceId) continue

      if (!runsByTraceId [traceId]) {
        runsByTraceId [traceId] = {
          metadata: {
            run_id: run.id,
            trace_id: traceId,
            timestamp: run.start_time,
          }
        }
      }

      // 从 PromptTemplate 运行中提取输入
      if (run.name === 'PromptTemplate') {
        runsByTraceId [traceId].input = run.inputs
      }

      // 从 StrOutputParser 运行中提取输出
      if (run.name === 'StrOutputParser' && run.outputs?.output) {
        runsByTraceId [traceId].output = { output: run.outputs.output }
      }
    }

    // 用于收集输入、输出和元数据的数组
    const inputs: any [] = []
    const outputs: any [] = []
    const metadata: any[] = []

    // 第二遍：收集完整的示例
    for (const traceId in runsByTraceId) {
      const runData = runsByTraceId [traceId]
      if (runData.input && runData.output) {
        inputs.push (runData.input)
        outputs.push (runData.output)
        metadata.push (runData.metadata)
      }
    }

    // 如果收集到了示例，批量创建
    if (inputs.length > 0) {
      await client.createExamples ({
        inputs,
        outputs,
        metadata,
        datasetId: dataset.id,
      })

      console.log (`创建了数据集"${datasetName}"，包含 ${inputs.length} 个示例`)
    } else {
      console.log (' 未找到匹配的运行来创建示例 ')
    }
  }, 1E8)
})