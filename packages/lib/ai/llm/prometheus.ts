import { Counter } from 'prom-client'

export const llmMessagePredictCounter = new Counter({
  name: 'llm_message_predict',
  help: 'The times of llm message perdict',
  labelNames:['bot_id']
})

export const llmMessagePredictErrorCounter = new Counter({
  name: 'llm_message_predict_error',
  help: 'The times of llm message perdict error',
  labelNames:['bot_id']
})

export const llmImageChatCounter = new Counter({
  name: 'llm_image_chat',
  help: 'The times of llm image chat',
  labelNames:['bot_id']
})

export const llmImageChatErrorCounter = new Counter({
  name: 'llm_image_chat_error',
  help: 'The times of llm image chat error',
  labelNames:['bot_id']
})