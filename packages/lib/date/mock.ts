export const OriginalDate: DateConstructor = Date // 导出原始的 Date 构造函数

export function fixDate(fixedDateString: string) {
  function MockDateConstructor(...args: any[]) {
    if (args.length === 0) {
      const now = new OriginalDate()
      const fixedDate = new OriginalDate(fixedDateString)
      now.setFullYear(fixedDate.getFullYear(), fixedDate.getMonth(), fixedDate.getDate())
      return now
    } else {
      // @ts-ignore fku
      return new OriginalDate(...args)
    }
  }

  // Copy all own properties, including non-enumerable ones, but exclude 'prototype'
  Object.getOwnPropertyNames(OriginalDate).forEach((prop) => {
    if (prop !== 'prototype') {
      Object.defineProperty(
        MockDateConstructor,
        prop,
          Object.getOwnPropertyDescriptor(OriginalDate, prop)!
      )
    }
  })

  MockDateConstructor.prototype = OriginalDate.prototype;

  (globalThis as any).Date = MockDateConstructor
}