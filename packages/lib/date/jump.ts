export let isJumpedTime = false
export const OriginalDate: DateConstructor = Date // 导出原始的 Date 构造函数

export function jumpDate(fixedDateString: string) {
  const OriginalDate = Date

  const delta = new OriginalDate(fixedDateString).getTime() - new OriginalDate().getTime()

  function MockDateConstructor(...args: any[]) {
    if (args.length === 0) {
      // No arguments: return the current time adjusted by delta
      const now = new OriginalDate()
      return new OriginalDate(now.getTime() + delta)
    } else {
      // @ts-ignore fku
      return new OriginalDate(...args)
    }
  }

  // Copy all static properties from the original Date constructor
  Object.getOwnPropertyNames(OriginalDate).forEach((prop) => {
    if (prop !== 'prototype') {
      Object.defineProperty(
        MockDateConstructor,
        prop,
        Object.getOwnPropertyDescriptor(OriginalDate, prop)!
      )
    }
  })

  // Set the prototype to ensure instanceof checks work correctly
  MockDateConstructor.prototype = OriginalDate.prototype;

  // Replace the global Date constructor with the mock
  (globalThis as any).Date = MockDateConstructor;

  // Expose MockDate globally if you want to access it directly
  (globalThis as any).MockDate = MockDateConstructor

  isJumpedTime = true
}