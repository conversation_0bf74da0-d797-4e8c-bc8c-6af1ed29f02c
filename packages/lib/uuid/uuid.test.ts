import { UUID } from './uuid'

describe('UUID class', () => {
  it('should generate a valid UUID v4', () => {
    const uuidV4 = UUID.v4()
    expect(UUID.isUUID(uuidV4)).toBe(true)

  })

  it('should generate a valid short UUID', () => {
    const shortUUID = UUID.short()
    expect(UUID.isShortUUID(shortUUID)).toBe(true)

  })

  it('should not accept an invalid UUID', () => {
    const invalidUUID = 'not-a-uuid'
    expect(UUID.isUUID(invalidUUID)).toBe(false)
  })

  it('should not accept an invalid short UUID', () => {
    const invalidShortUUID = 'tooshort'
    expect(UUID.isShortUUID(invalidShortUUID)).toBe(false)
  })
})