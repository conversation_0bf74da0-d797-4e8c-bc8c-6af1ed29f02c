import { Config } from '../../config'

export async function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export async function randomSleep(min: number, max: number): Promise<void> {
  const rand = Math.floor(Math.random() * (max - min + 1)) + min
  return new Promise((resolve) => setTimeout(resolve, rand))
}

export async function commonSleep () {
  await sleep(Config.setting.waitingTime.activePush.interval)
}