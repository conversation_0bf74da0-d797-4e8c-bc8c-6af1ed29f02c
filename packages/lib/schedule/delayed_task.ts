interface DelayOptions {
    s?: number // 秒
    m?: number // 分钟
    h?: number // 小时
}

export class DelayedTask {
  private delay: number
  private task: () => Promise<any>
  private condition: () => Promise<boolean>
  private autoRetry: boolean
  private timeoutId: NodeJS.Timeout | null
  private updateChecker: (() => Promise<any>) | undefined

  constructor(delay: number | DelayOptions, task: () => Promise<any>, condition: () => Promise<boolean>, autoRetry: boolean = false, updateConditionChecker?: ()=> Promise<any>) {
    this.delay = 0
    if (typeof delay === 'number') {
      this.delay = delay
    } else {
      const { s, m, h, } = delay
      if (s !== undefined) this.delay += s * 1000
      if (m !== undefined) this.delay += m * 60 * 1000
      if (h !== undefined) this.delay += h * 60 * 60 * 1000
    }

    this.task = task
    this.condition = condition
    this.autoRetry = autoRetry
    this.timeoutId = null
    this.updateChecker = updateConditionChecker
  }

  async start() {
    this.timeoutId = setTimeout(async () => {
      try {
        if (await this.condition()) {
          await this.task()
        } else {
          console.log('不满足条件，任务被取消')
          if (this.autoRetry) { // 递归重试，直到完成
            console.log(`将在 ${this.delay / 1000 }s 后重试`)

            if (this.updateChecker) {
              await this.updateChecker()
            }

            await this.start()
          }
        }
      } catch (error) {
        console.error('Task execution failed:', error)
      }
    }, this.delay)
  }

  public cancel() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
    }
  }
}