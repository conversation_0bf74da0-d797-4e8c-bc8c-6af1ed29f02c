export class DouyinParser {
  static isDouyinForward(line: string): boolean {
    // Check if the line starts with a number followed by a period
    // and contains the word "复制" (copy) and a Douyin URL
    if (line.includes('复制打开抖音') || line.includes('【Ai算法工程师Future的作品】')) {
      return true
    }

    const pattern = /^\d+\.\d+\s*复制打开抖音.*https:\/\/v\.douyin\.com\/\w+\//
    return pattern.test(line)
  }

  static getDouyinForwardText(line: string): string {
    const res = ''

    // Extract the text between the author and the Douyin URL
    const pattern = /【.*?】(.*?)\s*https:\/\/v\.douyin\.com\/\w+\//
    const match = line.match(pattern)
    if (match && match.length > 1) {
      return match[1].trim()
    } else if (line.includes('【Ai算法工程师Future的作品】') && line.includes('...')) {
      // 匹配 【Ai算法工程师Future的作品】之后 ... 之前的文字
      const pattern = /【Ai算法工程师Future的作品】(.*?)\.\.\./
      const match = line.match(pattern)
      if (match && match.length > 1) {
        return match[1].trim()
      }
    }
    return res
  }
}
