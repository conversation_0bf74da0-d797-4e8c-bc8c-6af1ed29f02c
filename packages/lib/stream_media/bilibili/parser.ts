import axios from 'axios'

export class BilibiliVideoParser {
  private static baseUrl = 'https://api.bilibili.com/x/web-interface/view'

  public static async getVideoTitle(url: string): Promise<string | null> {
    const bvid = this.extractBvid(url)
    if (!bvid) {
      console.error('Invalid Bilibili video URL.')
      return null
    }

    try {
      const response = await axios.get(`${this.baseUrl}?bvid=${bvid}`)
      if (response.data.code === 0) {
        return response.data.data.title
      } else {
        console.error('Failed to fetch video information.')
        return null
      }
    } catch (error) {
      console.error('Request failed:', error)
      return null
    }
  }

  private static extractBvid(url: string): string | null {
    const regex = /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(?:av(\d+)\/)?BV(\w+)/i
    const match = url.match(regex)

    if (match && match[2]) {
      return `BV${match[2]}`
    } else if (match && match[1]) {
      return this.convertAvToBv(parseInt(match[1], 10))
    } else {
      return null
    }
  }

  private static  convertAvToBv(av: number): string {
    const table = 'fZodR9XQDSUm21yCkr6zBqiveYah8bt4xsWpHnJE7jL5VG3guMTKNPAwcF'
    const tr:Record<string, number> = {}
    for (let i = 0; i < 58; i++) {
      tr[table[i]] = i
    }
    const s = [11, 10, 3, 8, 4, 6]
    const xor = 177451812
    const add = 8728348608

    let r = 0
    for (let i = 0; i < 6; i++) {
      r += tr[av.toString()[s[i]]] * 58 ** i
    }
    return `BV${(r - add) ^ xor}`
  }

  static isBilibiliVideoUrl(url: string) {
    // 定义用于匹配Bilibili视频链接的正则表达式
    const regex = /(?:https?:\/\/)?(?:www\.)?(?:bilibili\.com\/video\/(?:av(\d+)\/)?BV(\w+)|b23\.tv\/(\w+))/i

    return regex.test(url)
  }

  static async b23TvToBilibiliUrl(url: string) {
    try {
      // 匹配出 b23.tv 的链接
      const regex = /(?:https?:\/\/)?(?:www\.)?(b23\.tv\/(\w+))/i
      const match = url.match(regex)
      if (!match || !match[2]) {
        console.error('Invalid b23.tv URL.')
        return null
      }
      const shortUrl = match[0]

      const response = await axios.head(shortUrl, { maxRedirects: 5 }) // 允许最多5次重定向
      const realUrl = response.request.res.responseUrl // 获取最终URL
      const urlObj = new URL(realUrl)

      // 构造并返回没有查询参数和片段的URL
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`
    } catch (e) {
      console.error(e)
      throw e // 向调用者抛出异常
    }
  }

}