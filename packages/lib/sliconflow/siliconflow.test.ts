import Siliconflow from './siliconflow'
import { Config } from '../../config/config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const siliconflow = new Siliconflow()

    siliconflow.auth(Config.setting.siliconFlow.apiKey)


    const response = await siliconflow.createRerank({
      query: '牛油果',
      documents: ['苹果', '香蕉', '水果', '蔬菜'],
      top_n: 4,
      return_documents: true,

    })

    console.log(JSON.stringify(response, null, 4))
  })

})