# ElizaSales

A programmer who subconsciously views himself as an artist will enjoy what he does and will do it better.

**ElizaSales 究竟是个什么东东？**

ElizaSales 是一款智能的会话式 AI 助手，专门在聊天软件上处理与潜在客户的互动。你可以把它看作是一名自动化的销售，它能够聊天、理解上下文、获取信息、记住历史互动，并遵循复杂的对话流程。我们的目标？全自动化 AI 销售，达到比 Top Sales 更高的转化率。

## ✨ 主要特性与神奇内核 ✨

- **会话式 AI 核心：** 支持多模型接入 LLMs（OpenAI、Azure OpenAI、通义千问等）
- **对话集成：** 支持多聊天框架接入，企微/WhatsApp
- **流程引擎：** 定义基于客户输入与状态的对话逻辑和动作。
- **长期记忆：** 存储客户记忆和客户画像，实现上下文感知的对话。
- **状态管理：** 每个客户独立维护对话状态，确保对话的连续性。
- **可靠消息处理：** 使用消息队列（BullMQ）处理延迟消息，确保消息不丢失。
- **可配置与扩展性强：** 区分项目层（apps) 和通用库，适合多个项目持续增长

---

## 🛠️ 我们热爱的技术栈 🛠️

- **语言：** TypeScript（严格类型！）
- **Monorepo：** pnpm 工作空间
- **数据库：** MongoDB
- **向量数据库：** Elasticsearch
- **缓存/队列：** Redis、BullMQ
- **HTTP：** Axios
- **日志：** Pino
- **测试：** Jest

---

## 🏗️ 项目结构揭秘 🏗️

这是一个 pnpm monorepo 项目结构：

```
ElizaSales
├── apps/                # 独立项目（例如 Yuhe，墨尔）
│   └── yuhe/            # 项目代码
├── packages/            # 可复用代码库
│   ├── config/          # 配置文件（API 密钥、环境设置）
│   ├── lib/             # 通用工具库（日期、文件、路径、HTTP、对象工具等， 可以独立作为 npm 包）
│   ├── model/           # 外部服务与数据库封装
│   └── service/         # 核心业务逻辑（消息处理、LLM 回复、记忆管理）
├── prisma/              # Prisma 模型定义
├── dev/                 # 本地测试文件夹，会被 gitignore 掉
├── scripts/             # 工具脚本
├── pnpm-workspace.yaml  # Monorepo 定义
├── package.json         # 根包文件
└── tsconfig.json        # 根 TypeScript 配置
```

请从 apps 中选择一个项目从 client 下的 client_server 作为入口开始阅读

---

## 🚀 快速上手 🚀

1. **前置要求：**

   - Node.js >= 18
   - pnpm (`npm install -g pnpm`)

2. **安装：**

   - 克隆仓库。
   - git checkout develop 分支， 请在 develop 分支下进行开发， 不要在 main(生产) 分支下开发
   - 根目录运行 `pnpm install` 安装所有依赖。

3. **数据库设置：**

   - 运行 `pnpx prisma generate` 生成 Prisma 客户端。

4. **本地调试：**
   - 本地自动化测试 写一个测试文件 调用 WorFlow.step()
   - 本地连接客户端调试
     - 配置中的`syq`客户端目前使用`natapp.cn`进行内网穿透。
       从 `https://natapp.cn/#download` 下载对应的客户端，然后使用 `./natapp -authtoken=98992eb2883bc316` 启动内网穿透
     - 使用以下命令启动本地服务：`npm run client:syq`
     - 可以开始跟 本地测试账号（企微或 whatsapp 等）聊天进行测试
   - 测试账号测试，代码提交到开发分支，测试账号会热更新，可以直接跟账号聊天测试

---

## 📜 黄金守则（请务必遵守） 📜

1. **面向测试编程：**

   - 好的代码应该是便于测试的。所以复杂逻辑要拆分成独立函数方便测试，独立函数的参数应尽量简单，便于测试。

2. **模型调用规范：**

   - 使用封装后的 LLM 类进行模型调用，内置模型轮转机制，已配置 Langsmith，方便日志排查。

3. **日志记录要规范：**

   - 项目代码中禁用 `console.log`(除了 lib 文件夹以外，lib 文件下只能使用 console.log)，必须使用统一的日志工具 `logger.log`。
   - 当 logger 的第一个参数为 { chat_id } 时，参数会被写入后台，便于 问题排查。如 `logger.trace({ chat_id }, 'hi')`。

4. **Prompt 编写流程推荐：**

   - **深入理解业务**，了解人在解决这个任务的时候需要什么 context, 思考方式是怎样的。
   - 使用 MetaPrompt 方法调用生成 Prompt。
   - 编写单元测试并进行验证。
   - 想一想效果不好，是不是要结合更多的 context, 或者给定的方向是错误的或者不够精准。或者任务比较复杂可能需要通过加一层 reasoning 来指引。
   - 尽量使用 PromptTemplate 加 invoke(参数) 的方式来调用，便于对参数的保存和提取。

5. **消息队列的账号绑定：**
   - 创建新队列时，需要添加账号 ID 至消息队列名称，不然不同的账号会对消息队列的任务进行抢占处理。比如 new Queue('A*TASK') 是个错误的命名方式，而 new Queue('A_TASK*' + accountId) 是更好的命名方式。

---

💡 新人小贴士：如何优雅 coding？

- **调试神器：** 了解下日志，后台，LangSmith 这三个看问题的地方。开启 `apps/yuhe/workflow/context/context.ts` 中的 `debug: true` 可打印 Prompt，调试对话逻辑必备。
- **AI 辅助：** 可以通过给 AI 看整个代码仓库，以及给他看文档来提升 AI 对项目的理解
- **代码规范：** 时刻记住代码不只是为了自己编写，也是给其他人看和使用。保证对外暴漏方法的简约性，和代码的规范性，尽量不要使用嵌套函数。在找钺清 review 之前，可以使用 claude 来 review 代码，避免 PR 被反复打回。
- **问老司机：** 项目复杂，遇到问题先自己想一下，然后及时咨询团队内资深成员，快速找到最优解决方案。
